"use client";

import React, { useState, useEffect } from "react";
import { X, Percent, Save, Search, ChevronDown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface GradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  grade?: any | null;
  students: any[];
  subjects: any[];
  examTypes: any[];
  loading?: boolean;
}

export default function GradeModal({
  isOpen,
  onClose,
  onSubmit,
  grade,
  students,
  subjects,
  examTypes,
  loading = false
}: GradeModalProps) {
  const [formData, setFormData] = useState({
    student_id: "",
    subject_id: "",
    exam_type: "",
    term: "First Term" as "First Term" | "Second Term" | "Third Term",
    academic_year: "2024-2025",
    score: "",
    grade: "",
    comments: ""
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Search states
  const [studentSearch, setStudentSearch] = useState('');
  const [subjectSearch, setSubjectSearch] = useState('');
  const [examTypeSearch, setExamTypeSearch] = useState('');
  const [showStudentDropdown, setShowStudentDropdown] = useState(false);
  const [showSubjectDropdown, setShowSubjectDropdown] = useState(false);
  const [showExamTypeDropdown, setShowExamTypeDropdown] = useState(false);

  const isEditing = !!grade;

  useEffect(() => {
    if (isOpen) {
      if (grade) {
        console.log('Grade data for editing:', grade); // Debug log

        // Extract IDs more robustly
        let studentId = "";
        let subjectId = "";
        let examTypeId = "";

        // Handle student_id extraction
        if (grade.student_id) {
          if (typeof grade.student_id === 'object' && grade.student_id._id) {
            studentId = grade.student_id._id;
          } else if (typeof grade.student_id === 'string') {
            studentId = grade.student_id;
          }
        }

        // Handle subject_id extraction
        if (grade.subject_id) {
          if (typeof grade.subject_id === 'object' && grade.subject_id._id) {
            subjectId = grade.subject_id._id;
          } else if (typeof grade.subject_id === 'string') {
            subjectId = grade.subject_id;
          }
        }

        // Handle exam_type extraction
        if (grade.exam_type) {
          if (typeof grade.exam_type === 'object' && grade.exam_type._id) {
            examTypeId = grade.exam_type._id;
          } else if (typeof grade.exam_type === 'string') {
            examTypeId = grade.exam_type;
          }
        }

        // Convert percentage score to /20 for display
        const scoreOn20 = grade.score ? (grade.score * 20 / 100).toFixed(1) : "";

        console.log('Extracted IDs:', { studentId, subjectId, examTypeId }); // Debug log

        setFormData({
          student_id: studentId,
          subject_id: subjectId,
          exam_type: examTypeId,
          term: grade.term || "First Term",
          academic_year: grade.academic_year || "2024-2025",
          score: scoreOn20,
          grade: grade.grade || "",
          comments: grade.comments || ""
        });
      } else {
        setFormData({
          student_id: "",
          subject_id: "",
          exam_type: "",
          term: "First Term",
          academic_year: "2024-2025",
          score: "",
          grade: "",
          comments: ""
        });
      }
      setErrors({});
      // Reset search states
      setStudentSearch('');
      setSubjectSearch('');
      setExamTypeSearch('');
      setShowStudentDropdown(false);
      setShowSubjectDropdown(false);
      setShowExamTypeDropdown(false);
    }
  }, [isOpen, grade]);

  // Auto-calculate grade based on score (/20 system) with French/English mentions
  useEffect(() => {
    if (formData.score) {
      const score = parseFloat(formData.score);
      let calculatedGrade = "";

      // French/English grading system based on /20 score
      if (score >= 18) calculatedGrade = "A+/Excellent";
      else if (score >= 16) calculatedGrade = "A/Très bien";
      else if (score >= 14) calculatedGrade = "B+/Bien";
      else if (score >= 12) calculatedGrade = "B/Assez bien";
      else if (score >= 10) calculatedGrade = "C+/Passable";
      else if (score >= 8) calculatedGrade = "C/Insuffisant";
      else if (score >= 6) calculatedGrade = "D+/Médiocre";
      else if (score >= 4) calculatedGrade = "D/Très insuffisant";
      else calculatedGrade = "F/Nul";

      setFormData(prev => ({ ...prev, grade: calculatedGrade }));
    }
  }, [formData.score]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShowStudentDropdown(false);
      setShowSubjectDropdown(false);
      setShowExamTypeDropdown(false);
    };

    if (showStudentDropdown || showSubjectDropdown || showExamTypeDropdown) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showStudentDropdown, showSubjectDropdown, showExamTypeDropdown]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.student_id) {
      newErrors.student_id = "Student is required";
    }
    if (!formData.subject_id) {
      newErrors.subject_id = "Subject is required";
    }
    if (!formData.exam_type) {
      newErrors.exam_type = "Exam type is required";
    }
    if (!formData.score) {
      newErrors.score = "Score is required";
    } else {
      const score = parseFloat(formData.score);
      if (isNaN(score) || score < 0 || score > 20) {
        newErrors.score = "Score must be a number between 0 and 20";
      }
    }
    if (!formData.academic_year) {
      newErrors.academic_year = "Academic year is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const scoreOn20 = parseFloat(formData.score);
      // Convert /20 score to percentage for backend storage
      const scorePercentage = (scoreOn20 * 100) / 20;

      const submitData = {
        ...formData,
        score: scorePercentage
      };
      await onSubmit(submitData);
      onClose();
    } catch (error) {
      console.error("Error submitting grade:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  // Helper functions for search and selection
  const getSelectedStudentName = () => {
    const selectedStudent = students.find(s => s._id === formData.student_id);
    return selectedStudent ? `${selectedStudent.first_name} ${selectedStudent.last_name} (${selectedStudent.student_id})` : '';
  };

  const getSelectedSubjectName = () => {
    const selectedSubject = subjects.find(s => s._id === formData.subject_id);
    return selectedSubject ? selectedSubject.name : '';
  };

  const getSelectedExamTypeName = () => {
    const selectedExamType = examTypes.find(e => e._id === formData.exam_type);
    return selectedExamType ? selectedExamType.type : '';
  };

  const filteredStudents = students.filter(student =>
    `${student.first_name} ${student.last_name} ${student.student_id}`.toLowerCase().includes(studentSearch.toLowerCase())
  );

  const filteredSubjects = subjects.filter(subject =>
    subject.name.toLowerCase().includes(subjectSearch.toLowerCase())
  );

  const filteredExamTypes = examTypes.filter(examType =>
    examType.type.toLowerCase().includes(examTypeSearch.toLowerCase())
  );

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Percent className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {isEditing ? "Edit Grade" : "Add New Grade"}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isEditing ? "Update grade record" : "Create new grade record"}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {/* Student Search */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Student <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div
                    className={`w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 ${
                      errors.student_id
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowStudentDropdown(!showStudentDropdown);
                      setShowSubjectDropdown(false);
                      setShowExamTypeDropdown(false);
                    }}
                  >
                    <span className="text-gray-900 dark:text-white">
                      {getSelectedStudentName() || "Select student"}
                    </span>
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  </div>

                  {showStudentDropdown && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
                      <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            placeholder="Search students..."
                            value={studentSearch}
                            onChange={(e) => setStudentSearch(e.target.value)}
                            className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-600 dark:text-white text-sm"
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                      <div className="max-h-48 overflow-y-auto">
                        {filteredStudents.length > 0 ? (
                          filteredStudents.map((student) => (
                            <div
                              key={student._id}
                              className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleInputChange("student_id", student._id);
                                setShowStudentDropdown(false);
                                setStudentSearch('');
                              }}
                            >
                              <div className="font-medium">{student.first_name} {student.last_name}</div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">{student.student_id}</div>
                            </div>
                          ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                            No students found
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                {errors.student_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.student_id}</p>
                )}
              </div>

              {/* Subject Search */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Subject <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div
                    className={`w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 ${
                      errors.subject_id
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowSubjectDropdown(!showSubjectDropdown);
                      setShowStudentDropdown(false);
                      setShowExamTypeDropdown(false);
                    }}
                  >
                    <span className="text-gray-900 dark:text-white">
                      {getSelectedSubjectName() || "Select subject"}
                    </span>
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  </div>

                  {showSubjectDropdown && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
                      <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            placeholder="Search subjects..."
                            value={subjectSearch}
                            onChange={(e) => setSubjectSearch(e.target.value)}
                            className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-600 dark:text-white text-sm"
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                      <div className="max-h-48 overflow-y-auto">
                        {filteredSubjects.length > 0 ? (
                          filteredSubjects.map((subject) => (
                            <div
                              key={subject._id}
                              className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleInputChange("subject_id", subject._id);
                                setShowSubjectDropdown(false);
                                setSubjectSearch('');
                              }}
                            >
                              {subject.name}
                            </div>
                          ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                            No subjects found
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                {errors.subject_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.subject_id}</p>
                )}
              </div>

              {/* Exam Type Search */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Exam Type <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div
                    className={`w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 ${
                      errors.exam_type
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowExamTypeDropdown(!showExamTypeDropdown);
                      setShowStudentDropdown(false);
                      setShowSubjectDropdown(false);
                    }}
                  >
                    <span className="text-gray-900 dark:text-white">
                      {getSelectedExamTypeName() || "Select exam type"}
                    </span>
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  </div>

                  {showExamTypeDropdown && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
                      <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            placeholder="Search exam types..."
                            value={examTypeSearch}
                            onChange={(e) => setExamTypeSearch(e.target.value)}
                            className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-600 dark:text-white text-sm"
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                      <div className="max-h-48 overflow-y-auto">
                        {filteredExamTypes.length > 0 ? (
                          filteredExamTypes.map((examType) => (
                            <div
                              key={examType._id}
                              className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleInputChange("exam_type", examType._id);
                                setShowExamTypeDropdown(false);
                                setExamTypeSearch('');
                              }}
                            >
                              {examType.type}
                            </div>
                          ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                            No exam types found
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                {errors.exam_type && (
                  <p className="mt-1 text-sm text-red-500">{errors.exam_type}</p>
                )}
              </div>

              {/* Term and Academic Year */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Term
                  </label>
                  <div className="relative">
                    <select
                      value={formData.term}
                      onChange={(e) => handleInputChange("term", e.target.value)}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white appearance-none"
                    >
                      <option value="First Term">First Term</option>
                      <option value="Second Term">Second Term</option>
                      <option value="Third Term">Third Term</option>
                    </select>
                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Academic Year
                  </label>
                  <input
                    type="text"
                    value={formData.academic_year}
                    onChange={(e) => handleInputChange("academic_year", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white ${
                      errors.academic_year 
                        ? "border-red-500 dark:border-red-500" 
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    placeholder="2024-2025"
                  />
                  {errors.academic_year && (
                    <p className="mt-1 text-sm text-red-500">{errors.academic_year}</p>
                  )}
                </div>
              </div>

              {/* Score and Grade */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Score (/20)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="20"
                    step="0.1"
                    value={formData.score}
                    onChange={(e) => handleInputChange("score", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white ${
                      errors.score
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    placeholder="17.1"
                  />
                  {errors.score && (
                    <p className="mt-1 text-sm text-red-500">{errors.score}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Grade (Auto-calculated - French/English)
                  </label>
                  <input
                    type="text"
                    value={formData.grade}
                    onChange={(e) => handleInputChange("grade", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                    placeholder="A+/Excellent"
                  />
                </div>
              </div>

              {/* Comments */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Comments (Optional)
                </label>
                <textarea
                  value={formData.comments}
                  onChange={(e) => handleInputChange("comments", e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Additional comments about the student's performance..."
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>{isSubmitting ? "Saving..." : (isEditing ? "Update" : "Create")}</span>
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
