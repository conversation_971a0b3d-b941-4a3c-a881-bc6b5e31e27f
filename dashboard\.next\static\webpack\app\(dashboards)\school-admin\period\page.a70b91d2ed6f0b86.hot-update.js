"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/period/page",{

/***/ "(app-pages-browser)/./src/components/utils/TableFix.tsx":
/*!*******************************************!*\
  !*** ./src/components/utils/TableFix.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,ChevronLeft,ChevronRight,Eye,Pen,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,ChevronLeft,ChevronRight,Eye,Pen,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,ChevronLeft,ChevronRight,Eye,Pen,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,ChevronLeft,ChevronRight,Eye,Pen,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,ChevronLeft,ChevronRight,Eye,Pen,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,ChevronLeft,ChevronRight,Eye,Pen,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,ChevronLeft,ChevronRight,Eye,Pen,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst DataTableFix = (param)=>{\n    let { columns, data, actions = [], hasSearch = true, defaultItemsPerPage = 5, loading = false, onLoadingChange = ()=>{}, onSelectionChange, handleDeleteMultiple, handleDeleteAll, showCheckbox = true, idAccessor = '_id', enableBulkActions = true, clearSelection = false, onSelectionCleared } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultItemsPerPage);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const itemsPerPageOptions = [\n        5,\n        10,\n        15,\n        20,\n        \"All\"\n    ];\n    const getPageNumbers = (current, total)=>{\n        if (total <= 5) return [\n            ...Array(total)\n        ].map((_, i)=>i + 1);\n        const pages = [];\n        if (current > 2) pages.push(1);\n        if (current > 3) pages.push('...');\n        const start = Math.max(2, current - 1);\n        const end = Math.min(total - 1, current + 1);\n        for(let i = start; i <= end; i++){\n            pages.push(i);\n        }\n        if (current < total - 2) pages.push('...');\n        if (current < total) pages.push(total);\n        return pages;\n    };\n    // Ajouter une clé unique à chaque ligne pour l'identifier\n    const dataWithKeys = data.map((row, index)=>({\n            row,\n            key: row[idAccessor] ? String(row[idAccessor]) : \"row-\".concat(index)\n        }));\n    // Filtrer les données en fonction du terme de recherche\n    const filteredData = dataWithKeys.filter((param)=>{\n        let { row } = param;\n        return columns.some((column)=>{\n            // Use searchValue if available, otherwise fall back to accessor\n            let value;\n            if (column.searchValue && typeof column.searchValue === \"function\") {\n                value = column.searchValue(row);\n            } else if (typeof column.accessor === \"function\") {\n                value = column.accessor(row);\n            } else {\n                value = row[column.accessor];\n            }\n            return value === null || value === void 0 ? void 0 : value.toString().toLowerCase().includes(searchTerm.toLowerCase());\n        });\n    });\n    // Calculer les données à afficher pour la page actuelle\n    const totalItems = filteredData.length;\n    const effectiveItemsPerPage = itemsPerPage === \"All\" ? totalItems : itemsPerPage;\n    const totalPages = Math.ceil(totalItems / effectiveItemsPerPage);\n    const startIndex = (currentPage - 1) * effectiveItemsPerPage;\n    const endIndex = startIndex + effectiveItemsPerPage;\n    const currentData = filteredData.slice(startIndex, endIndex);\n    const prevClearSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(clearSelection);\n    // Gérer la sélection/désélection d'une ligne\n    const handleRowSelection = (row)=>{\n        setSelectedRows((prev)=>{\n            if (prev.includes(row)) {\n                return prev.filter((r)=>r !== row);\n            } else {\n                return [\n                    ...prev,\n                    row\n                ];\n            }\n        });\n    };\n    // Gérer la sélection/désélection de toutes les lignes\n    const handleSelectAll = ()=>{\n        if (selectedRows.length === filteredData.length) {\n            setSelectedRows([]);\n        } else {\n            const allRows = filteredData.map((param)=>{\n                let { row } = param;\n                return row;\n            });\n            setSelectedRows(allRows);\n        }\n    };\n    const handleSelectAllData = ()=>{\n        const allRows = data;\n        setSelectedRows(allRows);\n    };\n    const handleDeselectAll = ()=>{\n        setSelectedRows([]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DataTableFix.useEffect\": ()=>{\n            if (onSelectionChange) {\n                onSelectionChange(selectedRows);\n            }\n        }\n    }[\"DataTableFix.useEffect\"], [\n        selectedRows,\n        onSelectionChange\n    ]);\n    // Effect to notify parent of selection changes\n    // useEffect(() => {\n    //     onSelectionChange?.(selectedRows);\n    // }, [selectedRows, onSelectionChange]);\n    // Effect to clear selection when clearSelection prop changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DataTableFix.useEffect\": ()=>{\n            if (clearSelection && selectedRows.length > 0) {\n                setSelectedRows([]);\n                onSelectionCleared === null || onSelectionCleared === void 0 ? void 0 : onSelectionCleared();\n            }\n        }\n    }[\"DataTableFix.useEffect\"], [\n        clearSelection,\n        selectedRows.length,\n        onSelectionCleared\n    ]);\n    // Handle delete selected\n    const handleDeleteSelected = ()=>{\n        if (selectedRows.length === 0) return;\n        const selectedIds = selectedRows.map((row)=>String(row[idAccessor]));\n        handleDeleteMultiple === null || handleDeleteMultiple === void 0 ? void 0 : handleDeleteMultiple(selectedIds);\n    };\n    // Vérifier si toutes les lignes sont sélectionnées\n    const isAllSelected = selectedRows.length === filteredData.length && filteredData.length > 0;\n    const isAllDataSelected = selectedRows.length === data.length && data.length > 0;\n    // Fonctions pour la pagination\n    const goToPreviousPage = ()=>{\n        if (currentPage > 1) {\n            setCurrentPage(currentPage - 1);\n        }\n    };\n    const goToNextPage = ()=>{\n        if (currentPage < totalPages) {\n            setCurrentPage(currentPage + 1);\n        }\n    };\n    // Gérer le changement du nombre d'éléments par page\n    const handleItemsPerPageChange = (event)=>{\n        const value = event.target.value;\n        setItemsPerPage(value === \"All\" ? \"All\" : Number(value));\n        setCurrentPage(1);\n    };\n    // Gérer la recherche\n    const handleSearch = (event)=>{\n        if (event.key === \"Enter\") {\n            setCurrentPage(1);\n            setIsLoading(true);\n            setTimeout(()=>{\n                setIsLoading(false);\n            }, 1000);\n        }\n    };\n    // Supprimer le filtre de recherche\n    const clearSearchFilter = ()=>{\n        setSearchTerm(\"\");\n        setCurrentPage(1);\n    };\n    // Supprimer tous les filtres\n    const clearAllFilters = ()=>{\n        setSearchTerm(\"\");\n        setCurrentPage(1);\n    };\n    // Get delete button text based on selection\n    const getDeleteButtonText = ()=>{\n        if (selectedRows.length === 0) return \"\";\n        if (selectedRows.length === 1) return \"Delete Selected (1)\";\n        return \"Delete Selected (\".concat(selectedRows.length, \")\");\n    };\n    // Fonction pour mapper les actions aux icônes\n    const getActionIcon = (action, row)=>{\n        switch(action.label.toLowerCase()){\n            case \"view\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>action.onClick(row),\n                    className: \"text-gray-500 hover:text-teal\",\n                    title: \"View Details\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 21\n                }, undefined);\n            case \"edit\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>action.onClick(row),\n                    className: \"text-gray-500 hover:text-blue-500\",\n                    title: \"Edit\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 21\n                }, undefined);\n            case \"delete\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>action.onClick(row),\n                    className: \"text-gray-500 hover:text-red-500\",\n                    title: \"Delete\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 21\n                }, undefined);\n            case \"manage\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>action.onClick(row),\n                    className: \"text-gray-500 hover:text-teal\",\n                    title: \"Manage\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 21\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            enableBulkActions && selectedRows.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                exit: {\n                    opacity: 0,\n                    y: -10\n                },\n                transition: {\n                    duration: 0.2\n                },\n                className: \"mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-3 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                            children: [\n                                selectedRows.length,\n                                \" of \",\n                                data.length,\n                                \" selected\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                !isAllDataSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    transition: {\n                                        type: 'spring',\n                                        stiffness: 300\n                                    },\n                                    onClick: handleSelectAllData,\n                                    className: \"px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm\",\n                                    children: [\n                                        \"Select All (\",\n                                        data.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    transition: {\n                                        type: 'spring',\n                                        stiffness: 300\n                                    },\n                                    onClick: handleDeselectAll,\n                                    className: \"px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 text-sm\",\n                                    children: \"Deselect All\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                selectedRows.length > 0 && !isAllDataSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    transition: {\n                                        type: 'spring',\n                                        stiffness: 300\n                                    },\n                                    onClick: handleDeleteSelected,\n                                    className: \"px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                                    children: getDeleteButtonText()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 33\n                                }, undefined),\n                                isAllDataSelected && handleDeleteAll && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    transition: {\n                                        type: 'spring',\n                                        stiffness: 300\n                                    },\n                                    onClick: handleDeleteAll,\n                                    className: \"px-3 py-2 bg-red-700 text-white rounded hover:bg-red-800 text-sm\",\n                                    children: [\n                                        \"Delete All (\",\n                                        data.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                lineNumber: 289,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full rounded-lg border border-gray-300 darK:border dark:border-gray-800 shadow-sm flex flex-col\",\n                children: [\n                    hasSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full max-w-md\",\n                            children: [\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute left-1 top-1/2 transform -translate-y-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        size: 8,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>{\n                                        setIsLoading(true);\n                                        setSearchTerm(e.target.value);\n                                        setTimeout(()=>{\n                                            setIsLoading(false);\n                                        }, 1000);\n                                    },\n                                    onKeyDown: handleSearch,\n                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-600 dark:text-foreground dark:bg-gray-700 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-teal\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 29\n                                }, undefined),\n                                searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearSearchFilter,\n                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 21\n                    }, undefined),\n                    searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-teal border border-gray-200 dark:border-gray-700 py-1 px-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"Active filters:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center border border-teal space-x-1 bg-teal-200 px-2 py-1 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-teal\",\n                                                children: [\n                                                    \"Search: \",\n                                                    searchTerm\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearSearchFilter,\n                                                className: \"text-teal\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 14\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearAllFilters,\n                                className: \"text-gray-500 hover:text-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-h-[400px] overflow-y-auto overflow-x-auto custom-scrollbar\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-full px-4 py-4 inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        size: 28,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 29\n                                }, undefined) : currentData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"There is no data available\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 29\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-w-max\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full table-auto border-collapse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                className: \"sticky top-0 bg-gray-50 dark:bg-gray-800 z-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-gray-50 dark:bg-gray-800 text-left text-sm font-semibold text-foreground p-3\",\n                                                    children: [\n                                                        showCheckbox && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-4 py-3 w-12\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: isAllSelected,\n                                                                onChange: handleSelectAll,\n                                                                className: \"h-4 w-4 text-teal border-gray-300 rounded\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        columns.map((column, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 w-max\",\n                                                                children: column.header\n                                                            }, index, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 49\n                                                            }, undefined)),\n                                                        actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-4 py-3 text-right w-32\",\n                                                            children: \"Actions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: currentData.map((param)=>{\n                                                    let { row, key } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-t border-gray-200 transition-colors duration-200 w-max \".concat(selectedRows.includes(row) ? \"bg-gray-50 dark:bg-gray-700 border-l-4 border-l-teal\" : \"hover:bg-gray-50 dark:hover:bg-gray-700\"),\n                                                        children: [\n                                                            showCheckbox && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-4 py-3 w-12\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: selectedRows.includes(row),\n                                                                    onChange: ()=>handleRowSelection(row),\n                                                                    className: \"h-4 w-4 text-teal border-gray-300 rounded cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 61\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 57\n                                                            }, undefined),\n                                                            columns.map((column, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-foreground\",\n                                                                    children: typeof column.accessor === \"function\" ? column.accessor(row) : row[column.accessor]\n                                                                }, colIndex, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 57\n                                                                }, undefined)),\n                                                            actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-4 py-3 text-right w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-end space-x-2\",\n                                                                    children: actions.map((action, actionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                                            children: getActionIcon(action, row)\n                                                                        }, actionIndex, false, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                                            lineNumber: 481,\n                                                                            columnNumber: 69\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 61\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        ]\n                                                    }, key, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 49\n                                                    }, undefined);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 21\n                            }, undefined),\n                            filteredData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 dark:bg-gray-800 dark:text-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:block text-sm text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            \"Page \",\n                                            currentPage,\n                                            \" of \",\n                                            totalPages\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"itemsPerPage\",\n                                                className: \"hidden md:block text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Items per page\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"itemsPerPage\",\n                                                value: itemsPerPage,\n                                                onChange: handleItemsPerPageChange,\n                                                className: \"hidden sm:block px-2 py-1 border border-gray-300 w-[80px] rounded-md text-sm text-gray-600 dark:text-foreground dark:bg-gray-700 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-teal\",\n                                                children: itemsPerPageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: option,\n                                                        children: option\n                                                    }, option, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 41\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex max-sm:justify-between max-sm:w-full space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: goToPreviousPage,\n                                                disabled: currentPage === 1,\n                                                className: \"p-2 text-foreground hover:text-teal disabled:text-gray-500 \".concat(currentPage === 1 ? \"cursor-not-allowed\" : \"max-sm:border max-sm:border-teal max-sm:rounded-lg\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        size: 20,\n                                                        className: \"hidden sm:block\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sm:hidden\",\n                                                        children: \"Previous\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            getPageNumbers(currentPage, totalPages).map((page, index)=>typeof page === \"number\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setCurrentPage(page),\n                                                    className: \"px-3 w-[40px] h-[40px] py-1 rounded-full text-sm hidden sm:block \".concat(currentPage === page ? \"focus:outline-none ring-2 ring-teal text-teal\" : \"text-foreground hover:ring-2 hover:ring-teal hover:text-teal\"),\n                                                    children: page\n                                                }, index, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 41\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 text-gray-400 hidden sm:block\",\n                                                    children: \"...\"\n                                                }, index, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 41\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: goToNextPage,\n                                                disabled: currentPage === totalPages,\n                                                className: \"p-2 text-foreground hover:text-teal disabled:text-gray-500 \".concat(currentPage === totalPages ? \"cursor-not-allowed\" : \"max-sm:border max-sm:border-teal max-sm:rounded-lg\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_ChevronLeft_ChevronRight_Eye_Pen_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        size: 20,\n                                                        className: \"hidden sm:block\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sm:hidden\",\n                                                        children: \"Next\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n                lineNumber: 359,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\utils\\\\TableFix.tsx\",\n        lineNumber: 286,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DataTableFix, \"o8on8BLsd9QMQowFkPOCnKRiJb0=\");\n_c = DataTableFix;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataTableFix);\nvar _c;\n$RefreshReg$(_c, \"DataTableFix\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/utils/TableFix.tsx\n"));

/***/ })

});