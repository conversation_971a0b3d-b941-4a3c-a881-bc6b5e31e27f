"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/attendance/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m6 9 6 6 6-6\",\n            key: \"qrunsl\"\n        }\n    ]\n];\nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-down\", __iconNode);\n //# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1kb3duLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdhLGlCQUF1QjtJQUFDO1FBQUMsTUFBUTtRQUFBLENBQUU7WUFBQSxFQUFHLGVBQWdCO1lBQUEsS0FBSyxDQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhN0Usa0JBQWMsa0VBQWlCLGlCQUFnQixDQUFVIiwic291cmNlcyI6WyJEOlxcZWx5c2VlXFxQcm9qZXRcXHNyY1xcaWNvbnNcXGNoZXZyb24tZG93bi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwYXRoJywgeyBkOiAnbTYgOSA2IDYgNi02Jywga2V5OiAncXJ1bnNsJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uRG93blxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TmlBNUlEWWdOaUEyTFRZaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZXZyb24tZG93blxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25Eb3duID0gY3JlYXRlTHVjaWRlSWNvbignY2hldnJvbi1kb3duJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZXZyb25Eb3duO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/modals/AttendanceModal.tsx":
/*!***************************************************!*\
  !*** ./src/components/modals/AttendanceModal.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttendanceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AttendanceModal(param) {\n    let { isOpen, onClose, onSubmit, attendance, students, classes, subjects, schedules, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id: \"\",\n        schedule_id: \"\",\n        status: \"Present\",\n        date: new Date().toISOString().split('T')[0],\n        academic_year: \"2024-2025\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredStudents, setFilteredStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Multi-selection states\n    const [isMultiMode, setIsMultiMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedStudents, setSelectedStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [studentStatuses, setStudentStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [bulkStatus, setBulkStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Present\");\n    // Search states\n    const [scheduleSearch, setScheduleSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [studentSearch, setStudentSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showScheduleDropdown, setShowScheduleDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStudentDropdown, setShowStudentDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isEditing = !!attendance;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (attendance) {\n                    // Single edit mode\n                    setIsMultiMode(false);\n                    setFormData({\n                        student_id: attendance.student_id || \"\",\n                        schedule_id: attendance.schedule_id || \"\",\n                        status: attendance.status || \"Present\",\n                        date: attendance.date ? new Date(attendance.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n                        academic_year: attendance.academic_year || \"2024-2025\"\n                    });\n                } else {\n                    // New attendance - default to multi mode\n                    setIsMultiMode(true);\n                    setFormData({\n                        student_id: \"\",\n                        schedule_id: \"\",\n                        status: \"Present\",\n                        date: new Date().toISOString().split('T')[0],\n                        academic_year: \"2024-2025\"\n                    });\n                }\n                setErrors({});\n                setSelectedStudents(new Set());\n                setStudentStatuses({});\n                setBulkStatus(\"Present\");\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        isOpen,\n        attendance\n    ]);\n    // Filter students based on selected schedule\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (formData.schedule_id) {\n                const selectedSchedule = schedules.find({\n                    \"AttendanceModal.useEffect.selectedSchedule\": (s)=>s._id === formData.schedule_id\n                }[\"AttendanceModal.useEffect.selectedSchedule\"]);\n                if (selectedSchedule) {\n                    const classStudents = students.filter({\n                        \"AttendanceModal.useEffect.classStudents\": (student)=>student.class_id === selectedSchedule.class_id\n                    }[\"AttendanceModal.useEffect.classStudents\"]);\n                    setFilteredStudents(classStudents);\n                    // Initialize student statuses for multi-mode\n                    if (isMultiMode) {\n                        const initialStatuses = {};\n                        classStudents.forEach({\n                            \"AttendanceModal.useEffect\": (student)=>{\n                                initialStatuses[student._id] = \"Present\";\n                            }\n                        }[\"AttendanceModal.useEffect\"]);\n                        setStudentStatuses(initialStatuses);\n                    }\n                }\n            } else {\n                setFilteredStudents(students);\n                if (isMultiMode) {\n                    const initialStatuses = {};\n                    students.forEach({\n                        \"AttendanceModal.useEffect\": (student)=>{\n                            initialStatuses[student._id] = \"Present\";\n                        }\n                    }[\"AttendanceModal.useEffect\"]);\n                    setStudentStatuses(initialStatuses);\n                }\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        formData.schedule_id,\n        schedules,\n        students,\n        isMultiMode\n    ]);\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"AttendanceModal.useEffect.handleClickOutside\": ()=>{\n                    setShowScheduleDropdown(false);\n                    setShowStudentDropdown(false);\n                }\n            }[\"AttendanceModal.useEffect.handleClickOutside\"];\n            if (showScheduleDropdown || showStudentDropdown) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"AttendanceModal.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"AttendanceModal.useEffect\"];\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        showScheduleDropdown,\n        showStudentDropdown\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!isMultiMode && !formData.student_id) {\n            newErrors.student_id = \"Student is required\";\n        }\n        if (isMultiMode && selectedStudents.size === 0) {\n            newErrors.students = \"At least one student must be selected\";\n        }\n        if (!formData.schedule_id) {\n            newErrors.schedule_id = \"Class schedule is required\";\n        }\n        if (!formData.date) {\n            newErrors.date = \"Date is required\";\n        }\n        if (!formData.academic_year) {\n            newErrors.academic_year = \"Academic year is required\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsSubmitting(true);\n        try {\n            if (isMultiMode) {\n                // Submit multiple attendance records\n                const attendancePromises = Array.from(selectedStudents).map((studentId)=>{\n                    const studentStatus = studentStatuses[studentId] || \"Present\";\n                    return onSubmit({\n                        ...formData,\n                        student_id: studentId,\n                        status: studentStatus\n                    });\n                });\n                await Promise.all(attendancePromises);\n            } else {\n                // Submit single attendance record\n                await onSubmit(formData);\n            }\n            onClose();\n        } catch (error) {\n            console.error(\"Error submitting attendance:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    // Multi-selection functions\n    const toggleStudentSelection = (studentId)=>{\n        const newSelected = new Set(selectedStudents);\n        if (newSelected.has(studentId)) {\n            newSelected.delete(studentId);\n        } else {\n            newSelected.add(studentId);\n        }\n        setSelectedStudents(newSelected);\n    };\n    const selectAllStudents = ()=>{\n        const allStudentIds = new Set(filteredStudents.map((s)=>s._id));\n        setSelectedStudents(allStudentIds);\n    };\n    const deselectAllStudents = ()=>{\n        setSelectedStudents(new Set());\n    };\n    const applyBulkStatus = ()=>{\n        const newStatuses = {\n            ...studentStatuses\n        };\n        selectedStudents.forEach((studentId)=>{\n            newStatuses[studentId] = bulkStatus;\n        });\n        setStudentStatuses(newStatuses);\n    };\n    const updateStudentStatus = (studentId, status)=>{\n        setStudentStatuses((prev)=>({\n                ...prev,\n                [studentId]: status\n            }));\n    };\n    const getScheduleDisplay = (schedule)=>{\n        const subject = subjects.find((s)=>s._id === schedule.subject_id);\n        const classInfo = classes.find((c)=>c._id === schedule.class_id);\n        return \"\".concat((subject === null || subject === void 0 ? void 0 : subject.name) || 'Unknown Subject', \" - \").concat((classInfo === null || classInfo === void 0 ? void 0 : classInfo.name) || 'Unknown Class', \" (\").concat(schedule.day_of_week, \")\");\n    };\n    // Helper functions for search and selection\n    const getSelectedScheduleName = ()=>{\n        const selectedSchedule = schedules.find((s)=>s._id === formData.schedule_id);\n        return selectedSchedule ? getScheduleDisplay(selectedSchedule) : '';\n    };\n    const getSelectedStudentName = ()=>{\n        const selectedStudent = filteredStudents.find((s)=>s._id === formData.student_id);\n        return selectedStudent ? \"\".concat(selectedStudent.first_name, \" \").concat(selectedStudent.last_name, \" (\").concat(selectedStudent.student_id, \")\") : '';\n    };\n    const filteredSchedules = schedules.filter((schedule)=>getScheduleDisplay(schedule).toLowerCase().includes(scheduleSearch.toLowerCase()));\n    const filteredStudentsForSearch = filteredStudents.filter((student)=>\"\".concat(student.first_name, \" \").concat(student.last_name, \" \").concat(student.student_id).toLowerCase().includes(studentSearch.toLowerCase()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-black bg-opacity-50\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full mx-4 \".concat(isMultiMode ? 'max-w-4xl' : 'max-w-md'),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-lg flex items-center justify-center \".concat(isMultiMode ? 'bg-green-500' : 'bg-blue-500'),\n                                            children: isMultiMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 34\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 77\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                    children: isEditing ? \"Edit Attendance\" : isMultiMode ? \"Mark Multiple Attendance\" : \"Mark Attendance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: isEditing ? \"Update attendance record\" : isMultiMode ? \"Mark attendance for multiple students\" : \"Create new attendance record\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        !isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMultiMode(!isMultiMode),\n                                            className: \"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600\",\n                                            children: isMultiMode ? 'Single Mode' : 'Multi Mode'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"p-6 space-y-4 \".concat(isMultiMode ? 'max-h-96 overflow-y-auto' : ''),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: [\n                                                \"Class Schedule \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 34\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 \".concat(errors.schedule_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setShowScheduleDropdown(!showScheduleDropdown);\n                                                        setShowStudentDropdown(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900 dark:text-white\",\n                                                            children: getSelectedScheduleName() || \"Select class schedule\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showScheduleDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 border-b border-gray-200 dark:border-gray-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        placeholder: \"Search schedules...\",\n                                                                        value: scheduleSearch,\n                                                                        onChange: (e)=>setScheduleSearch(e.target.value),\n                                                                        className: \"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white text-sm\",\n                                                                        onClick: (e)=>e.stopPropagation()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-48 overflow-y-auto\",\n                                                            children: filteredSchedules.length > 0 ? filteredSchedules.map((schedule)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleInputChange(\"schedule_id\", schedule._id);\n                                                                        setShowScheduleDropdown(false);\n                                                                        setScheduleSearch('');\n                                                                    },\n                                                                    children: getScheduleDisplay(schedule)\n                                                                }, schedule._id, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 29\n                                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-3 py-2 text-gray-500 dark:text-gray-400 text-sm\",\n                                                                children: \"No schedules found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.schedule_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.schedule_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this),\n                                !isMultiMode ? // Single student selection\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Student\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.student_id,\n                                            onChange: (e)=>handleInputChange(\"student_id\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.student_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            disabled: !formData.schedule_id,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select student\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 21\n                                                }, this),\n                                                filteredStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: student._id,\n                                                        children: [\n                                                            student.first_name,\n                                                            \" \",\n                                                            student.last_name,\n                                                            \" (\",\n                                                            student.student_id,\n                                                            \")\"\n                                                        ]\n                                                    }, student._id, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 19\n                                        }, this),\n                                        errors.student_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.student_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 17\n                                }, this) : // Multi-student selection\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                    children: [\n                                                        \"Students (\",\n                                                        selectedStudents.size,\n                                                        \" selected)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: selectAllStudents,\n                                                            className: \"text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded\",\n                                                            disabled: !formData.schedule_id,\n                                                            children: \"Select All\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: deselectAllStudents,\n                                                            className: \"text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded\",\n                                                            children: \"Clear\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 19\n                                        }, this),\n                                        selectedStudents.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                        children: \"Apply to selected:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: bulkStatus,\n                                                        onChange: (e)=>setBulkStatus(e.target.value),\n                                                        className: \"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Present\",\n                                                                children: \"Present\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Absent\",\n                                                                children: \"Absent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Late\",\n                                                                children: \"Late\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Excused\",\n                                                                children: \"Excused\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: applyBulkStatus,\n                                                        className: \"px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600\",\n                                                        children: \"Apply\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md\",\n                                            children: filteredStudents.length > 0 ? filteredStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-600 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>toggleStudentSelection(student._id),\n                                                                    className: \"text-blue-500 hover:text-blue-600\",\n                                                                    children: selectedStudents.has(student._id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                            children: [\n                                                                                student.first_name,\n                                                                                \" \",\n                                                                                student.last_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                            children: student.student_id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        selectedStudents.has(student._id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: studentStatuses[student._id] || \"Present\",\n                                                            onChange: (e)=>updateStudentStatus(student._id, e.target.value),\n                                                            className: \"px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Present\",\n                                                                    children: \"Present\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Absent\",\n                                                                    children: \"Absent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Late\",\n                                                                    children: \"Late\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Excused\",\n                                                                    children: \"Excused\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 498,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, student._id, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 25\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center text-gray-500 dark:text-gray-400\",\n                                                children: formData.schedule_id ? \"No students found for this class\" : \"Please select a class schedule first\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 19\n                                        }, this),\n                                        errors.students && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.students\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 17\n                                }, this),\n                                !isMultiMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.status,\n                                            onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Present\",\n                                                    children: \"Present\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Absent\",\n                                                    children: \"Absent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Late\",\n                                                    children: \"Late\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Excused\",\n                                                    children: \"Excused\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Date\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.date,\n                                            onChange: (e)=>handleInputChange(\"date\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.date ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.date\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Academic Year\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.academic_year,\n                                            onChange: (e)=>handleInputChange(\"academic_year\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.academic_year ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            placeholder: \"e.g., 2024-2025\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.academic_year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.academic_year\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: onClose,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            disabled: isSubmitting,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                            children: [\n                                                isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isSubmitting ? \"Saving...\" : isEditing ? \"Update\" : isMultiMode ? \"Mark \".concat(selectedStudents.size, \" Students\") : \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n            lineNumber: 253,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, this);\n}\n_s(AttendanceModal, \"ujAStAdb6hvNxzWAVhpwmsAnTao=\");\n_c = AttendanceModal;\nvar _c;\n$RefreshReg$(_c, \"AttendanceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/AttendanceModal.tsx\n"));

/***/ })

});