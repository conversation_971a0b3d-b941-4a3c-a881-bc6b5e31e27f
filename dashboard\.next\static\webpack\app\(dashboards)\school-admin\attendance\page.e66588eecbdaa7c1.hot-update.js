"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/attendance/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/attendance/page.tsx":
/*!***************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/attendance/page.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttendancePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,FileCheck2,Filter,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/AttendanceServices */ \"(app-pages-browser)/./src/app/services/AttendanceServices.tsx\");\n/* harmony import */ var _app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/StudentServices */ \"(app-pages-browser)/./src/app/services/StudentServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_ClassScheduleServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/ClassScheduleServices */ \"(app-pages-browser)/./src/app/services/ClassScheduleServices.tsx\");\n/* harmony import */ var _components_modals_AttendanceModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/AttendanceModal */ \"(app-pages-browser)/./src/components/modals/AttendanceModal.tsx\");\n/* harmony import */ var _components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/DeleteConfirmationModal */ \"(app-pages-browser)/./src/components/modals/DeleteConfirmationModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    baseHref: \"/school-admin/attendance\",\n    title: \"Attendance\"\n};\nfunction AttendancePage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    // State management\n    const [attendanceRecords, setAttendanceRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalStudents: 0,\n        presentToday: 0,\n        absentToday: 0,\n        lateToday: 0,\n        excusedToday: 0,\n        attendanceRate: 0\n    });\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Date().toISOString().split('T')[0]);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isAttendanceModalOpen, setIsAttendanceModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [attendanceToEdit, setAttendanceToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [attendanceToDelete, setAttendanceToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedAttendances, setSelectedAttendances] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [schedules, setSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Export states\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showExportDropdown, setShowExportDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch attendance data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AttendancePage.useEffect\": ()=>{\n            const fetchAttendanceData = {\n                \"AttendancePage.useEffect.fetchAttendanceData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedDate) filters.date = selectedDate;\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedStatus !== 'all') filters.status = selectedStatus;\n                        // Fetch records and stats in parallel\n                        const [recordsResponse, statsResponse] = await Promise.all([\n                            (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceRecords)(schoolId, filters),\n                            (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceStats)(schoolId, selectedDate)\n                        ]);\n                        setAttendanceRecords(recordsResponse.attendance_records);\n                        setStats(statsResponse.stats);\n                    } catch (error) {\n                        console.error(\"Error fetching attendance data:\", error);\n                        showError(\"Error\", \"Failed to load attendance data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"AttendancePage.useEffect.fetchAttendanceData\"];\n            fetchAttendanceData();\n        }\n    }[\"AttendancePage.useEffect\"], [\n        schoolId,\n        selectedDate,\n        selectedClass,\n        selectedStatus\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AttendancePage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"AttendancePage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__.getStudentsBySchool)(schoolId),\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_9__.getClassesBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_10__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_ClassScheduleServices__WEBPACK_IMPORTED_MODULE_11__.getClassSchedulesBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setStudents(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch students:\", results[0].reason);\n                            setStudents([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setClasses(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[1].reason);\n                            setClasses([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setSubjects(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[2].reason);\n                            setSubjects([]);\n                        }\n                        if (results[3].status === 'fulfilled') {\n                            setSchedules(results[3].value);\n                        } else {\n                            console.error(\"Failed to fetch schedules:\", results[3].reason);\n                            setSchedules([]);\n                        // Don't show error for schedules as it's not critical\n                        }\n                        // Only show error if critical data failed to load\n                        const criticalDataFailed = results[0].status === 'rejected' || results[1].status === 'rejected' || results[2].status === 'rejected';\n                        if (criticalDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"AttendancePage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"AttendancePage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateAttendance = ()=>{\n        setAttendanceToEdit(null);\n        setIsAttendanceModalOpen(true);\n    };\n    const handleEditAttendance = (attendance)=>{\n        setAttendanceToEdit(attendance);\n        setIsAttendanceModalOpen(true);\n    };\n    const handleDeleteAttendance = (attendance)=>{\n        setAttendanceToDelete(attendance);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedAttendances(selectedRows);\n    };\n    // Modal submission functions\n    const handleAttendanceSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (attendanceToEdit) {\n                // Update existing attendance\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.updateAttendance)(attendanceToEdit._id, data);\n            } else {\n                // Create new attendance\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.createAttendance)({\n                    ...data,\n                    school_id: schoolId\n                });\n            }\n            // Refresh attendance list\n            const filters = {};\n            if (selectedDate) filters.date = selectedDate;\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedStatus !== 'all') filters.status = selectedStatus;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceRecords)(schoolId, filters),\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceStats)(schoolId, selectedDate)\n            ]);\n            setAttendanceRecords(recordsResponse.attendance_records);\n            setStats(statsResponse.stats);\n            setIsAttendanceModalOpen(false);\n            setAttendanceToEdit(null);\n            // Show success notification\n            showSuccess(attendanceToEdit ? \"Attendance Updated\" : \"Attendance Marked\", attendanceToEdit ? \"Attendance record has been updated successfully.\" : \"Attendance has been marked successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting attendance:\", error);\n            showError(\"Error\", \"Failed to save attendance. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (!schoolId) return;\n        try {\n            if (deleteType === \"single\" && attendanceToDelete) {\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.deleteAttendance)(attendanceToDelete._id);\n            } else if (deleteType === \"multiple\") {\n                const selectedIds = selectedAttendances.map((a)=>a._id);\n                await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.deleteMultipleAttendances)(selectedIds);\n                setClearSelection(true);\n            }\n            // Refresh attendance list\n            const filters = {};\n            if (selectedDate) filters.date = selectedDate;\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedStatus !== 'all') filters.status = selectedStatus;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceRecords)(schoolId, filters),\n                (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.getAttendanceStats)(schoolId, selectedDate)\n            ]);\n            setAttendanceRecords(recordsResponse.attendance_records);\n            setStats(statsResponse.stats);\n            setIsDeleteModalOpen(false);\n            setAttendanceToDelete(null);\n            setSelectedAttendances([]);\n            // Show success notification\n            if (deleteType === \"single\") {\n                showSuccess(\"Attendance Deleted\", \"Attendance record has been deleted successfully.\");\n            } else {\n                showSuccess(\"Attendances Deleted\", \"\".concat(selectedAttendances.length, \" attendance records have been deleted successfully.\"));\n            }\n        } catch (error) {\n            console.error(\"Error deleting attendance(s):\", error);\n            showError(\"Error\", \"Failed to delete attendance record(s). Please try again.\");\n            throw error;\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'Present':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n            case 'Absent':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n            case 'Late':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n            case 'Excused':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n        }\n    };\n    // Export functions\n    const handleExportPDF = async ()=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsExporting(true);\n        try {\n            const filters = {\n                class_id: selectedClass !== 'all' ? selectedClass : undefined,\n                date: selectedDate,\n                status: selectedStatus !== 'all' ? selectedStatus : undefined\n            };\n            const blob = await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.exportAttendancePDF)(schoolId, filters);\n            // Create download link\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"attendance-export-\".concat(selectedDate, \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            showSuccess(\"Export Successful\", \"Attendance records have been exported to PDF successfully.\");\n            setShowExportDropdown(false);\n        } catch (error) {\n            console.error(\"Error exporting to PDF:\", error);\n            showError(\"Export Failed\", \"Failed to export attendance to PDF. Please try again.\");\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const handleExportExcel = async ()=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsExporting(true);\n        try {\n            const filters = {\n                class_id: selectedClass !== 'all' ? selectedClass : undefined,\n                date: selectedDate,\n                status: selectedStatus !== 'all' ? selectedStatus : undefined\n            };\n            const blob = await (0,_app_services_AttendanceServices__WEBPACK_IMPORTED_MODULE_7__.exportAttendanceExcel)(schoolId, filters);\n            // Create download link\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"attendance-export-\".concat(selectedDate, \".xlsx\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            showSuccess(\"Export Successful\", \"Attendance records have been exported to Excel successfully.\");\n            setShowExportDropdown(false);\n        } catch (error) {\n            console.error(\"Error exporting to Excel:\", error);\n            showError(\"Export Failed\", \"Failed to export attendance to Excel. Please try again.\");\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Student\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-foreground\",\n                            children: row.student_name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-foreground/60\",\n                            children: row.student_id\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>\"\".concat(row.student_name, \" \").concat(row.student_id)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.class_name\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.subject_name\n        },\n        {\n            header: \"Period\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: [\n                        \"Period \",\n                        row.period_number\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>\"Period \".concat(row.period_number)\n        },\n        {\n            header: \"Teacher\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.teacher_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.teacher_name\n        },\n        {\n            header: \"Status\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(row.status)),\n                    children: row.status\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 428,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.status\n        },\n        {\n            header: \"Date\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: new Date(row.date).toLocaleDateString()\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>new Date(row.date).toLocaleDateString()\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (attendance)=>{\n                handleEditAttendance(attendance);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (attendance)=>{\n                handleDeleteAttendance(attendance);\n            }\n        }\n    ];\n    console.log(\"attendanceRecords\", attendanceRecords);\n    // Filter data based on selections\n    const filteredRecords = attendanceRecords.filter((record)=>{\n        if (selectedClass !== 'all' && record.class_name !== selectedClass) return false;\n        if (selectedStatus !== 'all' && record.status !== selectedStatus) return false;\n        return true;\n    });\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                lineNumber: 469,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n            lineNumber: 468,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Attendance Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Monitor and track student attendance across all classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    stats.attendanceRate,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Today's Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: stats.totalStudents\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Present Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: stats.presentToday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Absent Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: stats.absentToday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Late Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-yellow-600\",\n                                                        children: stats.lateToday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Date:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: selectedDate,\n                                                onChange: (e)=>setSelectedDate(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: classItem._id,\n                                                            children: classItem.name\n                                                        }, classItem._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Present\",\n                                                        children: \"Present\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Absent\",\n                                                        children: \"Absent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Late\",\n                                                        children: \"Late\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Excused\",\n                                                        children: \"Excused\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                onClick: ()=>setShowExportDropdown(!showExportDropdown),\n                                                disabled: isExporting,\n                                                className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: 14\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isExporting ? 'Exporting...' : 'Export'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this),\n                                            showExportDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-widget border border-stroke rounded-md shadow-lg z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleExportPDF,\n                                                        disabled: isExporting,\n                                                        className: \"w-full text-left px-4 py-2 text-sm text-foreground hover:bg-gray-100 dark:hover:bg-gray-800 disabled:opacity-50\",\n                                                        children: \"Export as PDF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleExportExcel,\n                                                        disabled: isExporting,\n                                                        className: \"w-full text-left px-4 py-2 text-sm text-foreground hover:bg-gray-100 dark:hover:bg-gray-800 disabled:opacity-50\",\n                                                        children: \"Export as Excel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 552,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Attendance Records (\",\n                                                filteredRecords.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateAttendance,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_FileCheck2_Filter_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Mark Attendance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredRecords,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                            lineNumber: 635,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_AttendanceModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    isOpen: isAttendanceModalOpen,\n                    onClose: ()=>{\n                        setIsAttendanceModalOpen(false);\n                        setAttendanceToEdit(null);\n                    },\n                    onSubmit: handleAttendanceSubmit,\n                    attendance: attendanceToEdit,\n                    students: students,\n                    classes: classes,\n                    subjects: subjects,\n                    schedules: schedules,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 668,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setAttendanceToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Attendance Record\" : \"Delete Selected Attendance Records\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this attendance record? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedAttendances.length, \" selected attendance records? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && attendanceToDelete ? \"\".concat(attendanceToDelete.student_name, \" - \").concat(attendanceToDelete.subject_name, \" (\").concat(new Date(attendanceToDelete.date).toLocaleDateString(), \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedAttendances.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 684,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n                    lineNumber: 711,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n            lineNumber: 476,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\attendance\\\\page.tsx\",\n        lineNumber: 475,\n        columnNumber: 5\n    }, this);\n}\n_s(AttendancePage, \"vlBacPWpX0fIRUo4buLpAuyqp6o=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = AttendancePage;\nvar _c;\n$RefreshReg$(_c, \"AttendancePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZHMpL3NjaG9vbC1hZG1pbi9hdHRlbmRhbmNlL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUrRjtBQUN4QjtBQUNqQjtBQUNDO0FBRVU7QUFDdkI7QUFDcUI7QUFDeEI7QUFZSTtBQUMwQjtBQUNIO0FBQ0s7QUFDVTtBQUNmO0FBQ2dCO0FBQ2pCO0FBSWpFLE1BQU1nQyxhQUFhO0lBQ2pCQyxNQUFNakMsc0lBQVVBO0lBQ2hCa0MsVUFBVTtJQUNWQyxPQUFPO0FBQ1Q7QUFFZSxTQUFTQztRQTBDTEM7O0lBekNqQixNQUFNLEVBQUVDLE1BQU0sRUFBRUQsSUFBSSxFQUFFLEdBQUd4Qiw4REFBT0E7SUFDaEMsTUFBTSxFQUFFMEIsTUFBTSxFQUFFQyxXQUFXLEVBQUVDLFdBQVcsRUFBRUMsU0FBUyxFQUFFLEdBQUdaLCtEQUFRQTtJQUVoRSxtQkFBbUI7SUFDbkIsTUFBTSxDQUFDYSxtQkFBbUJDLHFCQUFxQixHQUFHbEMsK0NBQVFBLENBQXFCLEVBQUU7SUFDakYsTUFBTSxDQUFDbUMsT0FBT0MsU0FBUyxHQUFHcEMsK0NBQVFBLENBQWtCO1FBQ2xEcUMsZUFBZTtRQUNmQyxjQUFjO1FBQ2RDLGFBQWE7UUFDYkMsV0FBVztRQUNYQyxjQUFjO1FBQ2RDLGdCQUFnQjtJQUNsQjtJQUNBLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHNUMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDNkMsY0FBY0MsZ0JBQWdCLEdBQUc5QywrQ0FBUUEsQ0FBQyxJQUFJK0MsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7SUFDdkYsTUFBTSxDQUFDQyxlQUFlQyxpQkFBaUIsR0FBR25ELCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ29ELGdCQUFnQkMsa0JBQWtCLEdBQUdyRCwrQ0FBUUEsQ0FBQztJQUVyRCxlQUFlO0lBQ2YsTUFBTSxDQUFDc0QsdUJBQXVCQyx5QkFBeUIsR0FBR3ZELCtDQUFRQSxDQUFDO0lBQ25FLE1BQU0sQ0FBQ3dELG1CQUFtQkMscUJBQXFCLEdBQUd6RCwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUMwRCxrQkFBa0JDLG9CQUFvQixHQUFHM0QsK0NBQVFBLENBQTBCO0lBQ2xGLE1BQU0sQ0FBQzRELG9CQUFvQkMsc0JBQXNCLEdBQUc3RCwrQ0FBUUEsQ0FBMEI7SUFDdEYsTUFBTSxDQUFDOEQsWUFBWUMsY0FBYyxHQUFHL0QsK0NBQVFBLENBQXdCO0lBQ3BFLE1BQU0sQ0FBQ2dFLHFCQUFxQkMsdUJBQXVCLEdBQUdqRSwrQ0FBUUEsQ0FBcUIsRUFBRTtJQUVyRiw0QkFBNEI7SUFDNUIsTUFBTSxDQUFDa0UsVUFBVUMsWUFBWSxHQUFHbkUsK0NBQVFBLENBQVEsRUFBRTtJQUNsRCxNQUFNLENBQUNvRSxTQUFTQyxXQUFXLEdBQUdyRSwrQ0FBUUEsQ0FBUSxFQUFFO0lBQ2hELE1BQU0sQ0FBQ3NFLFVBQVVDLFlBQVksR0FBR3ZFLCtDQUFRQSxDQUFRLEVBQUU7SUFDbEQsTUFBTSxDQUFDd0UsV0FBV0MsYUFBYSxHQUFHekUsK0NBQVFBLENBQVEsRUFBRTtJQUVwRCxpQkFBaUI7SUFDakIsTUFBTSxDQUFDMEUsY0FBY0MsZ0JBQWdCLEdBQUczRSwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUM0RSxnQkFBZ0JDLGtCQUFrQixHQUFHN0UsK0NBQVFBLENBQUM7SUFFckQsZ0JBQWdCO0lBQ2hCLE1BQU0sQ0FBQzhFLGFBQWFDLGVBQWUsR0FBRy9FLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2dGLG9CQUFvQkMsc0JBQXNCLEdBQUdqRiwrQ0FBUUEsQ0FBQztJQUU3RCwwQkFBMEI7SUFDMUIsTUFBTWtGLFdBQVd2RCxDQUFBQSxpQkFBQUEsNEJBQUFBLG1CQUFBQSxLQUFNd0QsVUFBVSxjQUFoQnhELHVDQUFBQSxnQkFBa0IsQ0FBQyxFQUFFLE1BQUlBLGlCQUFBQSwyQkFBQUEsS0FBTXlELFNBQVM7SUFFekQsaUNBQWlDO0lBQ2pDckYsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTXNGO2dFQUFzQjtvQkFDMUIsSUFBSSxDQUFDSCxVQUFVO3dCQUNibEQsVUFBVSxTQUFTO3dCQUNuQlksZUFBZTt3QkFDZjtvQkFDRjtvQkFFQSxJQUFJO3dCQUNGQSxlQUFlO3dCQUVmLGdCQUFnQjt3QkFDaEIsTUFBTTBDLFVBQWUsQ0FBQzt3QkFDdEIsSUFBSXpDLGNBQWN5QyxRQUFRQyxJQUFJLEdBQUcxQzt3QkFDakMsSUFBSUssa0JBQWtCLE9BQU9vQyxRQUFRRSxRQUFRLEdBQUd0Qzt3QkFDaEQsSUFBSUUsbUJBQW1CLE9BQU9rQyxRQUFRRyxNQUFNLEdBQUdyQzt3QkFFL0Msc0NBQXNDO3dCQUN0QyxNQUFNLENBQUNzQyxpQkFBaUJDLGNBQWMsR0FBRyxNQUFNQyxRQUFRQyxHQUFHLENBQUM7NEJBQ3pEdkYsc0ZBQW9CQSxDQUFDNEUsVUFBb0JJOzRCQUN6Qy9FLG9GQUFrQkEsQ0FBQzJFLFVBQW9CckM7eUJBQ3hDO3dCQUVEWCxxQkFBcUJ3RCxnQkFBZ0JJLGtCQUFrQjt3QkFDdkQxRCxTQUFTdUQsY0FBY3hELEtBQUs7b0JBQzlCLEVBQUUsT0FBTzRELE9BQU87d0JBQ2RDLFFBQVFELEtBQUssQ0FBQyxtQ0FBbUNBO3dCQUNqRC9ELFVBQVUsU0FBUztvQkFDckIsU0FBVTt3QkFDUlksZUFBZTtvQkFDakI7Z0JBQ0Y7O1lBRUF5QztRQUNGO21DQUFHO1FBQUNIO1FBQVVyQztRQUFjSztRQUFlRTtLQUFlO0lBRTFELG1DQUFtQztJQUNuQ3JELGdEQUFTQTtvQ0FBQztZQUNSLE1BQU1rRztnRUFBc0I7b0JBQzFCLElBQUksQ0FBQ2YsVUFBVTtvQkFFZixJQUFJO3dCQUNGLDRDQUE0Qzt3QkFDNUMsTUFBTWdCLFVBQVUsTUFBTU4sUUFBUU8sVUFBVSxDQUFDOzRCQUN2Q3JGLGtGQUFtQkEsQ0FBQ29FOzRCQUNwQm5FLCtFQUFrQkEsQ0FBQ21FOzRCQUNuQmxFLHFGQUFxQkEsQ0FBQ2tFOzRCQUN0QmpFLCtGQUF5QkEsQ0FBQ2lFO3lCQUMzQjt3QkFFRCxrQ0FBa0M7d0JBQ2xDLElBQUlnQixPQUFPLENBQUMsRUFBRSxDQUFDVCxNQUFNLEtBQUssYUFBYTs0QkFDckN0QixZQUFZK0IsT0FBTyxDQUFDLEVBQUUsQ0FBQ0UsS0FBSzt3QkFDOUIsT0FBTzs0QkFDTEosUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkcsT0FBTyxDQUFDLEVBQUUsQ0FBQ0csTUFBTTs0QkFDNURsQyxZQUFZLEVBQUU7d0JBQ2hCO3dCQUVBLElBQUkrQixPQUFPLENBQUMsRUFBRSxDQUFDVCxNQUFNLEtBQUssYUFBYTs0QkFDckNwQixXQUFXNkIsT0FBTyxDQUFDLEVBQUUsQ0FBQ0UsS0FBSzt3QkFDN0IsT0FBTzs0QkFDTEosUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkcsT0FBTyxDQUFDLEVBQUUsQ0FBQ0csTUFBTTs0QkFDM0RoQyxXQUFXLEVBQUU7d0JBQ2Y7d0JBRUEsSUFBSTZCLE9BQU8sQ0FBQyxFQUFFLENBQUNULE1BQU0sS0FBSyxhQUFhOzRCQUNyQ2xCLFlBQVkyQixPQUFPLENBQUMsRUFBRSxDQUFDRSxLQUFLO3dCQUM5QixPQUFPOzRCQUNMSixRQUFRRCxLQUFLLENBQUMsNkJBQTZCRyxPQUFPLENBQUMsRUFBRSxDQUFDRyxNQUFNOzRCQUM1RDlCLFlBQVksRUFBRTt3QkFDaEI7d0JBRUEsSUFBSTJCLE9BQU8sQ0FBQyxFQUFFLENBQUNULE1BQU0sS0FBSyxhQUFhOzRCQUNyQ2hCLGFBQWF5QixPQUFPLENBQUMsRUFBRSxDQUFDRSxLQUFLO3dCQUMvQixPQUFPOzRCQUNMSixRQUFRRCxLQUFLLENBQUMsOEJBQThCRyxPQUFPLENBQUMsRUFBRSxDQUFDRyxNQUFNOzRCQUM3RDVCLGFBQWEsRUFBRTt3QkFDZixzREFBc0Q7d0JBQ3hEO3dCQUVBLGtEQUFrRDt3QkFDbEQsTUFBTTZCLHFCQUFxQkosT0FBTyxDQUFDLEVBQUUsQ0FBQ1QsTUFBTSxLQUFLLGNBQ3ZCUyxPQUFPLENBQUMsRUFBRSxDQUFDVCxNQUFNLEtBQUssY0FDdEJTLE9BQU8sQ0FBQyxFQUFFLENBQUNULE1BQU0sS0FBSzt3QkFFaEQsSUFBSWEsb0JBQW9COzRCQUN0QnRFLFVBQVUsV0FBVzt3QkFDdkI7b0JBQ0YsRUFBRSxPQUFPK0QsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLG1DQUFtQ0E7d0JBQ2pEL0QsVUFBVSxTQUFTO29CQUNyQjtnQkFDRjs7WUFFQWlFO1FBQ0Y7bUNBQUc7UUFBQ2Y7S0FBUyxHQUFHLHNDQUFzQztJQUV0RCxpQkFBaUI7SUFDakIsTUFBTXFCLHlCQUF5QjtRQUM3QjVDLG9CQUFvQjtRQUNwQkoseUJBQXlCO0lBQzNCO0lBRUEsTUFBTWlELHVCQUF1QixDQUFDQztRQUM1QjlDLG9CQUFvQjhDO1FBQ3BCbEQseUJBQXlCO0lBQzNCO0lBRUEsTUFBTW1ELHlCQUF5QixDQUFDRDtRQUM5QjVDLHNCQUFzQjRDO1FBQ3RCMUMsY0FBYztRQUNkTixxQkFBcUI7SUFDdkI7SUFFQSxNQUFNa0QsdUJBQXVCO1FBQzNCNUMsY0FBYztRQUNkTixxQkFBcUI7SUFDdkI7SUFFQSxNQUFNbUQsd0JBQXdCLENBQUNDO1FBQzdCNUMsdUJBQXVCNEM7SUFDekI7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTUMseUJBQXlCLE9BQU9DO1FBQ3BDLElBQUksQ0FBQzdCLFVBQVU7WUFDYmxELFVBQVUsU0FBUztZQUNuQjtRQUNGO1FBRUEyQyxnQkFBZ0I7UUFDaEIsSUFBSTtZQUNGLElBQUlqQixrQkFBa0I7Z0JBQ3BCLDZCQUE2QjtnQkFDN0IsTUFBTWpELGtGQUFnQkEsQ0FBQ2lELGlCQUFpQnNELEdBQUcsRUFBRUQ7WUFDL0MsT0FBTztnQkFDTCx3QkFBd0I7Z0JBQ3hCLE1BQU12RyxrRkFBZ0JBLENBQUM7b0JBQUUsR0FBR3VHLElBQUk7b0JBQUUzQixXQUFXRjtnQkFBUztZQUN4RDtZQUVBLDBCQUEwQjtZQUMxQixNQUFNSSxVQUFlLENBQUM7WUFDdEIsSUFBSXpDLGNBQWN5QyxRQUFRQyxJQUFJLEdBQUcxQztZQUNqQyxJQUFJSyxrQkFBa0IsT0FBT29DLFFBQVFFLFFBQVEsR0FBR3RDO1lBQ2hELElBQUlFLG1CQUFtQixPQUFPa0MsUUFBUUcsTUFBTSxHQUFHckM7WUFFL0MsTUFBTSxDQUFDc0MsaUJBQWlCQyxjQUFjLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO2dCQUN6RHZGLHNGQUFvQkEsQ0FBQzRFLFVBQW9CSTtnQkFDekMvRSxvRkFBa0JBLENBQUMyRSxVQUFvQnJDO2FBQ3hDO1lBRURYLHFCQUFxQndELGdCQUFnQkksa0JBQWtCO1lBQ3ZEMUQsU0FBU3VELGNBQWN4RCxLQUFLO1lBQzVCb0IseUJBQXlCO1lBQ3pCSSxvQkFBb0I7WUFFcEIsNEJBQTRCO1lBQzVCNUIsWUFDRTJCLG1CQUFtQix1QkFBdUIscUJBQzFDQSxtQkFBbUIscURBQXFEO1FBRTVFLEVBQUUsT0FBT3FDLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDOUMvRCxVQUFVLFNBQVM7WUFDbkIsTUFBTStEO1FBQ1IsU0FBVTtZQUNScEIsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNc0Msc0JBQXNCO1FBQzFCLElBQUksQ0FBQy9CLFVBQVU7UUFFZixJQUFJO1lBQ0YsSUFBSXBCLGVBQWUsWUFBWUYsb0JBQW9CO2dCQUNqRCxNQUFNbEQsa0ZBQWdCQSxDQUFDa0QsbUJBQW1Cb0QsR0FBRztZQUMvQyxPQUFPLElBQUlsRCxlQUFlLFlBQVk7Z0JBQ3BDLE1BQU1vRCxjQUFjbEQsb0JBQW9CbUQsR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFSixHQUFHO2dCQUN0RCxNQUFNckcsMkZBQXlCQSxDQUFDdUc7Z0JBQ2hDckMsa0JBQWtCO1lBQ3BCO1lBRUEsMEJBQTBCO1lBQzFCLE1BQU1TLFVBQWUsQ0FBQztZQUN0QixJQUFJekMsY0FBY3lDLFFBQVFDLElBQUksR0FBRzFDO1lBQ2pDLElBQUlLLGtCQUFrQixPQUFPb0MsUUFBUUUsUUFBUSxHQUFHdEM7WUFDaEQsSUFBSUUsbUJBQW1CLE9BQU9rQyxRQUFRRyxNQUFNLEdBQUdyQztZQUUvQyxNQUFNLENBQUNzQyxpQkFBaUJDLGNBQWMsR0FBRyxNQUFNQyxRQUFRQyxHQUFHLENBQUM7Z0JBQ3pEdkYsc0ZBQW9CQSxDQUFDNEUsVUFBb0JJO2dCQUN6Qy9FLG9GQUFrQkEsQ0FBQzJFLFVBQW9CckM7YUFDeEM7WUFFRFgscUJBQXFCd0QsZ0JBQWdCSSxrQkFBa0I7WUFDdkQxRCxTQUFTdUQsY0FBY3hELEtBQUs7WUFDNUJzQixxQkFBcUI7WUFDckJJLHNCQUFzQjtZQUN0QkksdUJBQXVCLEVBQUU7WUFFekIsNEJBQTRCO1lBQzVCLElBQUlILGVBQWUsVUFBVTtnQkFDM0IvQixZQUFZLHNCQUFzQjtZQUNwQyxPQUFPO2dCQUNMQSxZQUFZLHVCQUF1QixHQUE4QixPQUEzQmlDLG9CQUFvQnFELE1BQU0sRUFBQztZQUNuRTtRQUNGLEVBQUUsT0FBT3RCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGlDQUFpQ0E7WUFDL0MvRCxVQUFVLFNBQVM7WUFDbkIsTUFBTStEO1FBQ1I7SUFDRjtJQUVBLE1BQU11QixpQkFBaUIsQ0FBQzdCO1FBQ3RCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTThCLGtCQUFrQjtRQUN0QixJQUFJLENBQUNyQyxVQUFVO1lBQ2JsRCxVQUFVLFNBQVM7WUFDbkI7UUFDRjtRQUVBK0MsZUFBZTtRQUNmLElBQUk7WUFDRixNQUFNTyxVQUFVO2dCQUNkRSxVQUFVdEMsa0JBQWtCLFFBQVFBLGdCQUFnQnNFO2dCQUNwRGpDLE1BQU0xQztnQkFDTjRDLFFBQVFyQyxtQkFBbUIsUUFBUUEsaUJBQWlCb0U7WUFDdEQ7WUFFQSxNQUFNQyxPQUFPLE1BQU03RyxxRkFBbUJBLENBQUNzRSxVQUFvQkk7WUFFM0QsdUJBQXVCO1lBQ3ZCLE1BQU1vQyxNQUFNQyxPQUFPQyxHQUFHLENBQUNDLGVBQWUsQ0FBQ0o7WUFDdkMsTUFBTUssT0FBT0MsU0FBU0MsYUFBYSxDQUFDO1lBQ3BDRixLQUFLRyxJQUFJLEdBQUdQO1lBQ1pJLEtBQUtJLFFBQVEsR0FBRyxxQkFBa0MsT0FBYnJGLGNBQWE7WUFDbERrRixTQUFTSSxJQUFJLENBQUNDLFdBQVcsQ0FBQ047WUFDMUJBLEtBQUtPLEtBQUs7WUFDVk4sU0FBU0ksSUFBSSxDQUFDRyxXQUFXLENBQUNSO1lBQzFCSCxPQUFPQyxHQUFHLENBQUNXLGVBQWUsQ0FBQ2I7WUFFM0IzRixZQUFZLHFCQUFxQjtZQUNqQ2tELHNCQUFzQjtRQUN4QixFQUFFLE9BQU9jLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekMvRCxVQUFVLGlCQUFpQjtRQUM3QixTQUFVO1lBQ1IrQyxlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxNQUFNeUQsb0JBQW9CO1FBQ3hCLElBQUksQ0FBQ3RELFVBQVU7WUFDYmxELFVBQVUsU0FBUztZQUNuQjtRQUNGO1FBRUErQyxlQUFlO1FBQ2YsSUFBSTtZQUNGLE1BQU1PLFVBQVU7Z0JBQ2RFLFVBQVV0QyxrQkFBa0IsUUFBUUEsZ0JBQWdCc0U7Z0JBQ3BEakMsTUFBTTFDO2dCQUNONEMsUUFBUXJDLG1CQUFtQixRQUFRQSxpQkFBaUJvRTtZQUN0RDtZQUVBLE1BQU1DLE9BQU8sTUFBTTVHLHVGQUFxQkEsQ0FBQ3FFLFVBQW9CSTtZQUU3RCx1QkFBdUI7WUFDdkIsTUFBTW9DLE1BQU1DLE9BQU9DLEdBQUcsQ0FBQ0MsZUFBZSxDQUFDSjtZQUN2QyxNQUFNSyxPQUFPQyxTQUFTQyxhQUFhLENBQUM7WUFDcENGLEtBQUtHLElBQUksR0FBR1A7WUFDWkksS0FBS0ksUUFBUSxHQUFHLHFCQUFrQyxPQUFickYsY0FBYTtZQUNsRGtGLFNBQVNJLElBQUksQ0FBQ0MsV0FBVyxDQUFDTjtZQUMxQkEsS0FBS08sS0FBSztZQUNWTixTQUFTSSxJQUFJLENBQUNHLFdBQVcsQ0FBQ1I7WUFDMUJILE9BQU9DLEdBQUcsQ0FBQ1csZUFBZSxDQUFDYjtZQUUzQjNGLFlBQVkscUJBQXFCO1lBQ2pDa0Qsc0JBQXNCO1FBQ3hCLEVBQUUsT0FBT2MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNkJBQTZCQTtZQUMzQy9ELFVBQVUsaUJBQWlCO1FBQzdCLFNBQVU7WUFDUitDLGVBQWU7UUFDakI7SUFDRjtJQUVBLGdCQUFnQjtJQUNoQixNQUFNMEQsVUFBVTtRQUNkO1lBQ0VDLFFBQVE7WUFDUkMsVUFBVSxDQUFDQyxvQkFDVCw4REFBQ0M7O3NDQUNDLDhEQUFDQzs0QkFBRUMsV0FBVTtzQ0FBK0JILElBQUlJLFlBQVk7Ozs7OztzQ0FDNUQsOERBQUNGOzRCQUFFQyxXQUFVO3NDQUE4QkgsSUFBSUssVUFBVTs7Ozs7Ozs7Ozs7O1lBRzdEQyxhQUFhLENBQUNOLE1BQTBCLEdBQXVCQSxPQUFwQkEsSUFBSUksWUFBWSxFQUFDLEtBQWtCLE9BQWZKLElBQUlLLFVBQVU7UUFDL0U7UUFDQTtZQUNFUCxRQUFRO1lBQ1JDLFVBQVUsQ0FBQ0Msb0JBQ1QsOERBQUNPO29CQUFLSixXQUFVOzhCQUF1QkgsSUFBSVEsVUFBVTs7Ozs7O1lBRXZERixhQUFhLENBQUNOLE1BQTBCQSxJQUFJUSxVQUFVO1FBQ3hEO1FBQ0E7WUFDRVYsUUFBUTtZQUNSQyxVQUFVLENBQUNDLG9CQUNULDhEQUFDTztvQkFBS0osV0FBVTs4QkFBV0gsSUFBSVMsWUFBWTs7Ozs7O1lBRTdDSCxhQUFhLENBQUNOLE1BQTBCQSxJQUFJUyxZQUFZO1FBQzFEO1FBQ0E7WUFDRVgsUUFBUTtZQUNSQyxVQUFVLENBQUNDLG9CQUNULDhEQUFDTztvQkFBS0osV0FBVTs7d0JBQVU7d0JBQVFILElBQUlVLGFBQWE7Ozs7Ozs7WUFFckRKLGFBQWEsQ0FBQ04sTUFBMEIsVUFBNEIsT0FBbEJBLElBQUlVLGFBQWE7UUFDckU7UUFDQTtZQUNFWixRQUFRO1lBQ1JDLFVBQVUsQ0FBQ0Msb0JBQ1QsOERBQUNPO29CQUFLSixXQUFVOzhCQUFXSCxJQUFJVyxZQUFZOzs7Ozs7WUFFN0NMLGFBQWEsQ0FBQ04sTUFBMEJBLElBQUlXLFlBQVk7UUFDMUQ7UUFDQTtZQUNFYixRQUFRO1lBQ1JDLFVBQVUsQ0FBQ0Msb0JBQ1QsOERBQUNPO29CQUFLSixXQUFXLDhDQUF5RSxPQUEzQnpCLGVBQWVzQixJQUFJbkQsTUFBTTs4QkFDckZtRCxJQUFJbkQsTUFBTTs7Ozs7O1lBR2Z5RCxhQUFhLENBQUNOLE1BQTBCQSxJQUFJbkQsTUFBTTtRQUNwRDtRQUNBO1lBQ0VpRCxRQUFRO1lBQ1JDLFVBQVUsQ0FBQ0Msb0JBQ1QsOERBQUNPO29CQUFLSixXQUFVOzhCQUFXLElBQUloRyxLQUFLNkYsSUFBSXJELElBQUksRUFBRWlFLGtCQUFrQjs7Ozs7O1lBRWxFTixhQUFhLENBQUNOLE1BQTBCLElBQUk3RixLQUFLNkYsSUFBSXJELElBQUksRUFBRWlFLGtCQUFrQjtRQUMvRTtLQUNEO0lBRUQsd0JBQXdCO0lBQ3hCLE1BQU1DLFVBQVU7UUFDZDtZQUNFQyxPQUFPO1lBQ1BDLFNBQVMsQ0FBQ2xEO2dCQUNSRCxxQkFBcUJDO1lBQ3ZCO1FBQ0Y7UUFDQTtZQUNFaUQsT0FBTztZQUNQQyxTQUFTLENBQUNsRDtnQkFDUkMsdUJBQXVCRDtZQUN6QjtRQUNGO0tBQ0Q7SUFDRFQsUUFBUTRELEdBQUcsQ0FBQyxxQkFBcUIzSDtJQUNqQyxrQ0FBa0M7SUFDbEMsTUFBTTRILGtCQUFrQjVILGtCQUFrQjZILE1BQU0sQ0FBQ0MsQ0FBQUE7UUFDL0MsSUFBSTdHLGtCQUFrQixTQUFTNkcsT0FBT1gsVUFBVSxLQUFLbEcsZUFBZSxPQUFPO1FBQzNFLElBQUlFLG1CQUFtQixTQUFTMkcsT0FBT3RFLE1BQU0sS0FBS3JDLGdCQUFnQixPQUFPO1FBQ3pFLE9BQU87SUFDVDtJQUVBLElBQUlULGFBQWE7UUFDZixxQkFDRSw4REFBQ2tHO1lBQUlFLFdBQVU7c0JBQ2IsNEVBQUM3SSwwRUFBY0E7Z0JBQUM4SixNQUFNO2dCQUFJQyxPQUFNOzs7Ozs7Ozs7OztJQUd0QztJQUVBLHFCQUNFLDhEQUFDN0osd0VBQWNBO1FBQUM4SixjQUFjO1lBQUM7WUFBZ0I7WUFBUztTQUFRO2tCQUM5RCw0RUFBQ3JLLGtGQUFZQTtZQUFDeUIsWUFBWUE7WUFBWTZJLFVBQVV2STs7OEJBQzlDLDhEQUFDaUg7b0JBQUlFLFdBQVU7O3NDQUViLDhEQUFDRjs0QkFBSUUsV0FBVTtzQ0FDYiw0RUFBQ0Y7Z0NBQUlFLFdBQVU7O2tEQUNiLDhEQUFDRjt3Q0FBSUUsV0FBVTs7MERBQ2IsOERBQUNGO2dEQUFJRSxXQUFVOzBEQUNiLDRFQUFDekosc0lBQVVBO29EQUFDeUosV0FBVTs7Ozs7Ozs7Ozs7MERBRXhCLDhEQUFDRjs7a0VBQ0MsOERBQUN1Qjt3REFBR3JCLFdBQVU7a0VBQXFDOzs7Ozs7a0VBQ25ELDhEQUFDRDt3REFBRUMsV0FBVTtrRUFBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFNdEMsOERBQUNGO3dDQUFJRSxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUVDLFdBQVU7O29EQUFzQzVHLE1BQU1PLGNBQWM7b0RBQUM7Ozs7Ozs7MERBQ3hFLDhEQUFDb0c7Z0RBQUVDLFdBQVU7MERBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNaEQsOERBQUNGOzRCQUFJRSxXQUFVOzs4Q0FDYiw4REFBQ0Y7b0NBQUlFLFdBQVU7OENBQ2IsNEVBQUNGO3dDQUFJRSxXQUFVOzswREFDYiw4REFBQ0Y7O2tFQUNDLDhEQUFDQzt3REFBRUMsV0FBVTtrRUFBNkI7Ozs7OztrRUFDMUMsOERBQUNEO3dEQUFFQyxXQUFVO2tFQUFzQzVHLE1BQU1FLGFBQWE7Ozs7Ozs7Ozs7OzswREFFeEUsOERBQUN3RztnREFBSUUsV0FBVTswREFDYiw0RUFBQ3ZKLHNJQUFLQTtvREFBQ3VKLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS3ZCLDhEQUFDRjtvQ0FBSUUsV0FBVTs4Q0FDYiw0RUFBQ0Y7d0NBQUlFLFdBQVU7OzBEQUNiLDhEQUFDRjs7a0VBQ0MsOERBQUNDO3dEQUFFQyxXQUFVO2tFQUE2Qjs7Ozs7O2tFQUMxQyw4REFBQ0Q7d0RBQUVDLFdBQVU7a0VBQXFDNUcsTUFBTUcsWUFBWTs7Ozs7Ozs7Ozs7OzBEQUV0RSw4REFBQ3VHO2dEQUFJRSxXQUFVOzBEQUNiLDRFQUFDdEosc0lBQVVBO29EQUFDc0osV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLNUIsOERBQUNGO29DQUFJRSxXQUFVOzhDQUNiLDRFQUFDRjt3Q0FBSUUsV0FBVTs7MERBQ2IsOERBQUNGOztrRUFDQyw4REFBQ0M7d0RBQUVDLFdBQVU7a0VBQTZCOzs7Ozs7a0VBQzFDLDhEQUFDRDt3REFBRUMsV0FBVTtrRUFBbUM1RyxNQUFNSSxXQUFXOzs7Ozs7Ozs7Ozs7MERBRW5FLDhEQUFDc0c7Z0RBQUlFLFdBQVU7MERBQ2IsNEVBQUN2SixzSUFBS0E7b0RBQUN1SixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUt2Qiw4REFBQ0Y7b0NBQUlFLFdBQVU7OENBQ2IsNEVBQUNGO3dDQUFJRSxXQUFVOzswREFDYiw4REFBQ0Y7O2tFQUNDLDhEQUFDQzt3REFBRUMsV0FBVTtrRUFBNkI7Ozs7OztrRUFDMUMsOERBQUNEO3dEQUFFQyxXQUFVO2tFQUFzQzVHLE1BQU1LLFNBQVM7Ozs7Ozs7Ozs7OzswREFFcEUsOERBQUNxRztnREFBSUUsV0FBVTswREFDYiw0RUFBQ3hKLHNJQUFRQTtvREFBQ3dKLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTzVCLDhEQUFDRjs0QkFBSUUsV0FBVTtzQ0FDYiw0RUFBQ0Y7Z0NBQUlFLFdBQVU7O2tEQUNiLDhEQUFDRjt3Q0FBSUUsV0FBVTs7MERBQ2IsOERBQUNySixzSUFBTUE7Z0RBQUNxSixXQUFVOzs7Ozs7MERBQ2xCLDhEQUFDSTtnREFBS0osV0FBVTswREFBc0M7Ozs7Ozs7Ozs7OztrREFHeEQsOERBQUNGO3dDQUFJRSxXQUFVOzswREFDYiw4REFBQ1c7Z0RBQU1YLFdBQVU7MERBQTZCOzs7Ozs7MERBQzlDLDhEQUFDc0I7Z0RBQ0NDLE1BQUs7Z0RBQ0xsRSxPQUFPdkQ7Z0RBQ1AwSCxVQUFVLENBQUNDLElBQU0xSCxnQkFBZ0IwSCxFQUFFQyxNQUFNLENBQUNyRSxLQUFLO2dEQUMvQzJDLFdBQVU7Ozs7Ozs7Ozs7OztrREFJZCw4REFBQ0Y7d0NBQUlFLFdBQVU7OzBEQUNiLDhEQUFDVztnREFBTVgsV0FBVTswREFBNkI7Ozs7OzswREFDOUMsOERBQUMyQjtnREFDQ3RFLE9BQU9sRDtnREFDUHFILFVBQVUsQ0FBQ0MsSUFBTXJILGlCQUFpQnFILEVBQUVDLE1BQU0sQ0FBQ3JFLEtBQUs7Z0RBQ2hEMkMsV0FBVTs7a0VBRVYsOERBQUM0Qjt3REFBT3ZFLE9BQU07a0VBQU07Ozs7OztvREFDbkJoQyxRQUFRK0MsR0FBRyxDQUFDLENBQUN5RCwwQkFDWiw4REFBQ0Q7NERBQTJCdkUsT0FBT3dFLFVBQVU1RCxHQUFHO3NFQUM3QzRELFVBQVVDLElBQUk7MkRBREpELFVBQVU1RCxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztrREFPaEMsOERBQUM2Qjt3Q0FBSUUsV0FBVTs7MERBQ2IsOERBQUNXO2dEQUFNWCxXQUFVOzBEQUE2Qjs7Ozs7OzBEQUM5Qyw4REFBQzJCO2dEQUNDdEUsT0FBT2hEO2dEQUNQbUgsVUFBVSxDQUFDQyxJQUFNbkgsa0JBQWtCbUgsRUFBRUMsTUFBTSxDQUFDckUsS0FBSztnREFDakQyQyxXQUFVOztrRUFFViw4REFBQzRCO3dEQUFPdkUsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQ3VFO3dEQUFPdkUsT0FBTTtrRUFBVTs7Ozs7O2tFQUN4Qiw4REFBQ3VFO3dEQUFPdkUsT0FBTTtrRUFBUzs7Ozs7O2tFQUN2Qiw4REFBQ3VFO3dEQUFPdkUsT0FBTTtrRUFBTzs7Ozs7O2tFQUNyQiw4REFBQ3VFO3dEQUFPdkUsT0FBTTtrRUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUk1Qiw4REFBQ3lDO3dDQUFJRSxXQUFVOzswREFDYiw4REFBQzFJLGtEQUFNQSxDQUFDeUssTUFBTTtnREFDWkMsWUFBWTtvREFBRUMsT0FBTztnREFBSztnREFDMUJDLFVBQVU7b0RBQUVELE9BQU87Z0RBQUs7Z0RBQ3hCckIsU0FBUyxJQUFNMUUsc0JBQXNCLENBQUNEO2dEQUN0Q2tHLFVBQVVwRztnREFDVmlFLFdBQVU7O2tFQUVWLDhEQUFDcEosc0lBQVFBO3dEQUFDcUssTUFBTTs7Ozs7O2tFQUNoQiw4REFBQ2I7a0VBQU1yRSxjQUFjLGlCQUFpQjs7Ozs7Ozs7Ozs7OzRDQUd2Q0Usb0NBQ0MsOERBQUM2RDtnREFBSUUsV0FBVTs7a0VBQ2IsOERBQUMrQjt3REFDQ25CLFNBQVNwQzt3REFDVDJELFVBQVVwRzt3REFDVmlFLFdBQVU7a0VBQ1g7Ozs7OztrRUFHRCw4REFBQytCO3dEQUNDbkIsU0FBU25CO3dEQUNUMEMsVUFBVXBHO3dEQUNWaUUsV0FBVTtrRUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBVVgsOERBQUNGOzRCQUFJRSxXQUFVOzs4Q0FDYiw4REFBQ0Y7b0NBQUlFLFdBQVU7O3NEQUNiLDhEQUFDb0M7NENBQUdwQyxXQUFVOztnREFBd0M7Z0RBQy9CYyxnQkFBZ0J4QyxNQUFNO2dEQUFDOzs7Ozs7O3NEQUc5Qyw4REFBQ2hILGtEQUFNQSxDQUFDeUssTUFBTTs0Q0FDWkMsWUFBWTtnREFBRUMsT0FBTzs0Q0FBSzs0Q0FDMUJDLFVBQVU7Z0RBQUVELE9BQU87NENBQUs7NENBQ3hCckIsU0FBU3BEOzRDQUNUd0MsV0FBVTs7OERBRVYsOERBQUNuSixzSUFBSUE7b0RBQUNvSyxNQUFNOzs7Ozs7OERBQ1osOERBQUNiOzhEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSVYsOERBQUNySiwyQ0FBUUE7b0NBQUNzTCx3QkFBVSw4REFBQ2xMLDBFQUFjQTt3Q0FBQzhKLE1BQU07d0NBQUlDLE9BQU07Ozs7Ozs4Q0FDbEQsNEVBQUNoSyxrRUFBWUE7d0NBQ1g4RyxNQUFNOEM7d0NBQ05wQixTQUFTQTt3Q0FDVGdCLFNBQVNBO3dDQUNUNEIscUJBQXFCO3dDQUNyQkMsbUJBQW1CMUU7d0NBQ25CRCxzQkFBc0JBO3dDQUN0Qi9CLGdCQUFnQkE7d0NBQ2hCMkcsb0JBQW9CLElBQU0xRyxrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU9wRCw4REFBQzNELDJFQUFlQTtvQkFDZHNLLFFBQVFsSTtvQkFDUm1JLFNBQVM7d0JBQ1BsSSx5QkFBeUI7d0JBQ3pCSSxvQkFBb0I7b0JBQ3RCO29CQUNBK0gsVUFBVTVFO29CQUNWTCxZQUFZL0M7b0JBQ1pRLFVBQVVBO29CQUNWRSxTQUFTQTtvQkFDVEUsVUFBVUE7b0JBQ1ZFLFdBQVdBO29CQUNYbUgsU0FBU2pIOzs7Ozs7OEJBSVgsOERBQUN2RCxtRkFBdUJBO29CQUN0QnFLLFFBQVFoSTtvQkFDUmlJLFNBQVM7d0JBQ1BoSSxxQkFBcUI7d0JBQ3JCSSxzQkFBc0I7b0JBQ3hCO29CQUNBK0gsV0FBVzNFO29CQUNYeEYsT0FDRXFDLGVBQWUsV0FDWCw2QkFDQTtvQkFFTitILFNBQ0UvSCxlQUFlLFdBQ1gsMEZBQ0EsbUNBQThELE9BQTNCRSxvQkFBb0JxRCxNQUFNLEVBQUM7b0JBRXBFeUUsVUFDRWhJLGVBQWUsWUFBWUYscUJBQ3ZCLEdBQXdDQSxPQUFyQ0EsbUJBQW1Cb0YsWUFBWSxFQUFDLE9BQXlDLE9BQXBDcEYsbUJBQW1CeUYsWUFBWSxFQUFDLE1BQTJELE9BQXZELElBQUl0RyxLQUFLYSxtQkFBbUIyQixJQUFJLEVBQUVpRSxrQkFBa0IsSUFBRyxPQUNuSWhDO29CQUVOdUUsV0FBV2pJLGVBQWUsYUFBYUUsb0JBQW9CcUQsTUFBTSxHQUFHRztvQkFDcEU4QyxNQUFNeEc7Ozs7Ozs4QkFJUiw4REFBQ3pDLGlFQUFjQTtvQkFBQ1EsUUFBUUE7b0JBQVE0SixTQUFTM0o7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSWpEO0dBbnFCd0JKOztRQUNHdkIsMERBQU9BO1FBQ3dCaUIsMkRBQVFBOzs7S0FGMUNNIiwic291cmNlcyI6WyJEOlxcZWx5c2VlXFxQcm9qZXRcXFBlcnNcXHNjaG9sYXJpZnlcXGRhc2hib2FyZFxcc3JjXFxhcHBcXChkYXNoYm9hcmRzKVxcc2Nob29sLWFkbWluXFxhdHRlbmRhbmNlXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCB7IEZpbGVDaGVjazIsIENhbGVuZGFyLCBVc2VycywgVHJlbmRpbmdVcCwgRmlsdGVyLCBEb3dubG9hZCwgUGx1cyB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcclxuaW1wb3J0IFNjaG9vbExheW91dCBmcm9tIFwiQC9jb21wb25lbnRzL0Rhc2hib2FyZC9MYXlvdXRzL1NjaG9vbExheW91dFwiO1xyXG5pbXBvcnQgeyBTdXNwZW5zZSwgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgRGF0YVRhYmxlRml4IGZyb20gXCJAL2NvbXBvbmVudHMvdXRpbHMvVGFibGVGaXhcIjtcclxuXHJcbmltcG9ydCBDaXJjdWxhckxvYWRlciBmcm9tIFwiQC9jb21wb25lbnRzL3dpZGdldHMvQ2lyY3VsYXJMb2FkZXJcIjtcclxuaW1wb3J0IHVzZUF1dGggZnJvbSBcIkAvYXBwL2hvb2tzL3VzZUF1dGhcIjtcclxuaW1wb3J0IFByb3RlY3RlZFJvdXRlIGZyb20gXCJAL2NvbXBvbmVudHMvdXRpbHMvUHJvdGVjdGVkUm91dGVcIjtcclxuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSBcImZyYW1lci1tb3Rpb25cIjtcclxuaW1wb3J0IHtcclxuICBnZXRBdHRlbmRhbmNlUmVjb3JkcyxcclxuICBnZXRBdHRlbmRhbmNlU3RhdHMsXHJcbiAgY3JlYXRlQXR0ZW5kYW5jZSxcclxuICB1cGRhdGVBdHRlbmRhbmNlLFxyXG4gIGRlbGV0ZUF0dGVuZGFuY2UsXHJcbiAgZGVsZXRlTXVsdGlwbGVBdHRlbmRhbmNlcyxcclxuICBleHBvcnRBdHRlbmRhbmNlUERGLFxyXG4gIGV4cG9ydEF0dGVuZGFuY2VFeGNlbCxcclxuICBBdHRlbmRhbmNlUmVjb3JkLFxyXG4gIEF0dGVuZGFuY2VTdGF0c1xyXG59IGZyb20gXCJAL2FwcC9zZXJ2aWNlcy9BdHRlbmRhbmNlU2VydmljZXNcIjtcclxuaW1wb3J0IHsgZ2V0U3R1ZGVudHNCeVNjaG9vbCB9IGZyb20gXCJAL2FwcC9zZXJ2aWNlcy9TdHVkZW50U2VydmljZXNcIjtcclxuaW1wb3J0IHsgZ2V0Q2xhc3Nlc0J5U2Nob29sIH0gZnJvbSBcIkAvYXBwL3NlcnZpY2VzL0NsYXNzU2VydmljZXNcIjtcclxuaW1wb3J0IHsgZ2V0U3ViamVjdHNCeVNjaG9vbElkIH0gZnJvbSBcIkAvYXBwL3NlcnZpY2VzL1N1YmplY3RTZXJ2aWNlc1wiO1xyXG5pbXBvcnQgeyBnZXRDbGFzc1NjaGVkdWxlc0J5U2Nob29sIH0gZnJvbSBcIkAvYXBwL3NlcnZpY2VzL0NsYXNzU2NoZWR1bGVTZXJ2aWNlc1wiO1xyXG5pbXBvcnQgQXR0ZW5kYW5jZU1vZGFsIGZyb20gXCJAL2NvbXBvbmVudHMvbW9kYWxzL0F0dGVuZGFuY2VNb2RhbFwiO1xyXG5pbXBvcnQgRGVsZXRlQ29uZmlybWF0aW9uTW9kYWwgZnJvbSBcIkAvY29tcG9uZW50cy9tb2RhbHMvRGVsZXRlQ29uZmlybWF0aW9uTW9kYWxcIjtcclxuaW1wb3J0IHsgdXNlVG9hc3QsIFRvYXN0Q29udGFpbmVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9Ub2FzdFwiO1xyXG5cclxuXHJcblxyXG5jb25zdCBuYXZpZ2F0aW9uID0ge1xyXG4gIGljb246IEZpbGVDaGVjazIsXHJcbiAgYmFzZUhyZWY6IFwiL3NjaG9vbC1hZG1pbi9hdHRlbmRhbmNlXCIsXHJcbiAgdGl0bGU6IFwiQXR0ZW5kYW5jZVwiXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBdHRlbmRhbmNlUGFnZSgpIHtcclxuICBjb25zdCB7IGxvZ291dCwgdXNlciB9ID0gdXNlQXV0aCgpO1xyXG4gIGNvbnN0IHsgdG9hc3RzLCByZW1vdmVUb2FzdCwgc2hvd1N1Y2Nlc3MsIHNob3dFcnJvciB9ID0gdXNlVG9hc3QoKTtcclxuXHJcbiAgLy8gU3RhdGUgbWFuYWdlbWVudFxyXG4gIGNvbnN0IFthdHRlbmRhbmNlUmVjb3Jkcywgc2V0QXR0ZW5kYW5jZVJlY29yZHNdID0gdXNlU3RhdGU8QXR0ZW5kYW5jZVJlY29yZFtdPihbXSk7XHJcbiAgY29uc3QgW3N0YXRzLCBzZXRTdGF0c10gPSB1c2VTdGF0ZTxBdHRlbmRhbmNlU3RhdHM+KHtcclxuICAgIHRvdGFsU3R1ZGVudHM6IDAsXHJcbiAgICBwcmVzZW50VG9kYXk6IDAsXHJcbiAgICBhYnNlbnRUb2RheTogMCxcclxuICAgIGxhdGVUb2RheTogMCxcclxuICAgIGV4Y3VzZWRUb2RheTogMCxcclxuICAgIGF0dGVuZGFuY2VSYXRlOiAwXHJcbiAgfSk7XHJcbiAgY29uc3QgW2xvYWRpbmdEYXRhLCBzZXRMb2FkaW5nRGF0YV0gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbc2VsZWN0ZWREYXRlLCBzZXRTZWxlY3RlZERhdGVdID0gdXNlU3RhdGUobmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0pO1xyXG4gIGNvbnN0IFtzZWxlY3RlZENsYXNzLCBzZXRTZWxlY3RlZENsYXNzXSA9IHVzZVN0YXRlKCdhbGwnKTtcclxuICBjb25zdCBbc2VsZWN0ZWRTdGF0dXMsIHNldFNlbGVjdGVkU3RhdHVzXSA9IHVzZVN0YXRlKCdhbGwnKTtcclxuXHJcbiAgLy8gTW9kYWwgc3RhdGVzXHJcbiAgY29uc3QgW2lzQXR0ZW5kYW5jZU1vZGFsT3Blbiwgc2V0SXNBdHRlbmRhbmNlTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNEZWxldGVNb2RhbE9wZW4sIHNldElzRGVsZXRlTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbYXR0ZW5kYW5jZVRvRWRpdCwgc2V0QXR0ZW5kYW5jZVRvRWRpdF0gPSB1c2VTdGF0ZTxBdHRlbmRhbmNlUmVjb3JkIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2F0dGVuZGFuY2VUb0RlbGV0ZSwgc2V0QXR0ZW5kYW5jZVRvRGVsZXRlXSA9IHVzZVN0YXRlPEF0dGVuZGFuY2VSZWNvcmQgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbZGVsZXRlVHlwZSwgc2V0RGVsZXRlVHlwZV0gPSB1c2VTdGF0ZTxcInNpbmdsZVwiIHwgXCJtdWx0aXBsZVwiPihcInNpbmdsZVwiKTtcclxuICBjb25zdCBbc2VsZWN0ZWRBdHRlbmRhbmNlcywgc2V0U2VsZWN0ZWRBdHRlbmRhbmNlc10gPSB1c2VTdGF0ZTxBdHRlbmRhbmNlUmVjb3JkW10+KFtdKTtcclxuXHJcbiAgLy8gQWRkaXRpb25hbCBkYXRhIGZvciBmb3Jtc1xyXG4gIGNvbnN0IFtzdHVkZW50cywgc2V0U3R1ZGVudHNdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcclxuICBjb25zdCBbY2xhc3Nlcywgc2V0Q2xhc3Nlc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xyXG4gIGNvbnN0IFtzdWJqZWN0cywgc2V0U3ViamVjdHNdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcclxuICBjb25zdCBbc2NoZWR1bGVzLCBzZXRTY2hlZHVsZXNdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcclxuXHJcbiAgLy8gTG9hZGluZyBzdGF0ZXNcclxuICBjb25zdCBbaXNTdWJtaXR0aW5nLCBzZXRJc1N1Ym1pdHRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtjbGVhclNlbGVjdGlvbiwgc2V0Q2xlYXJTZWxlY3Rpb25dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICAvLyBFeHBvcnQgc3RhdGVzXHJcbiAgY29uc3QgW2lzRXhwb3J0aW5nLCBzZXRJc0V4cG9ydGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Nob3dFeHBvcnREcm9wZG93biwgc2V0U2hvd0V4cG9ydERyb3Bkb3duXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgLy8gR2V0IHNjaG9vbCBJRCBmcm9tIHVzZXJcclxuICBjb25zdCBzY2hvb2xJZCA9IHVzZXI/LnNjaG9vbF9pZHM/LlswXSB8fCB1c2VyPy5zY2hvb2xfaWQ7XHJcblxyXG4gIC8vIEZldGNoIGF0dGVuZGFuY2UgZGF0YSBmcm9tIEFQSVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBmZXRjaEF0dGVuZGFuY2VEYXRhID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICBpZiAoIXNjaG9vbElkKSB7XHJcbiAgICAgICAgc2hvd0Vycm9yKFwiRXJyb3JcIiwgXCJObyBzY2hvb2wgSUQgZm91bmRcIik7XHJcbiAgICAgICAgc2V0TG9hZGluZ0RhdGEoZmFsc2UpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBzZXRMb2FkaW5nRGF0YSh0cnVlKTtcclxuXHJcbiAgICAgICAgLy8gQnVpbGQgZmlsdGVyc1xyXG4gICAgICAgIGNvbnN0IGZpbHRlcnM6IGFueSA9IHt9O1xyXG4gICAgICAgIGlmIChzZWxlY3RlZERhdGUpIGZpbHRlcnMuZGF0ZSA9IHNlbGVjdGVkRGF0ZTtcclxuICAgICAgICBpZiAoc2VsZWN0ZWRDbGFzcyAhPT0gJ2FsbCcpIGZpbHRlcnMuY2xhc3NfaWQgPSBzZWxlY3RlZENsYXNzO1xyXG4gICAgICAgIGlmIChzZWxlY3RlZFN0YXR1cyAhPT0gJ2FsbCcpIGZpbHRlcnMuc3RhdHVzID0gc2VsZWN0ZWRTdGF0dXM7XHJcblxyXG4gICAgICAgIC8vIEZldGNoIHJlY29yZHMgYW5kIHN0YXRzIGluIHBhcmFsbGVsXHJcbiAgICAgICAgY29uc3QgW3JlY29yZHNSZXNwb25zZSwgc3RhdHNSZXNwb25zZV0gPSBhd2FpdCBQcm9taXNlLmFsbChbXHJcbiAgICAgICAgICBnZXRBdHRlbmRhbmNlUmVjb3JkcyhzY2hvb2xJZCBhcyBzdHJpbmcsIGZpbHRlcnMpLFxyXG4gICAgICAgICAgZ2V0QXR0ZW5kYW5jZVN0YXRzKHNjaG9vbElkIGFzIHN0cmluZywgc2VsZWN0ZWREYXRlKVxyXG4gICAgICAgIF0pO1xyXG5cclxuICAgICAgICBzZXRBdHRlbmRhbmNlUmVjb3JkcyhyZWNvcmRzUmVzcG9uc2UuYXR0ZW5kYW5jZV9yZWNvcmRzKTtcclxuICAgICAgICBzZXRTdGF0cyhzdGF0c1Jlc3BvbnNlLnN0YXRzKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgYXR0ZW5kYW5jZSBkYXRhOlwiLCBlcnJvcik7XHJcbiAgICAgICAgc2hvd0Vycm9yKFwiRXJyb3JcIiwgXCJGYWlsZWQgdG8gbG9hZCBhdHRlbmRhbmNlIGRhdGFcIik7XHJcbiAgICAgIH0gZmluYWxseSB7XHJcbiAgICAgICAgc2V0TG9hZGluZ0RhdGEoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGZldGNoQXR0ZW5kYW5jZURhdGEoKTtcclxuICB9LCBbc2Nob29sSWQsIHNlbGVjdGVkRGF0ZSwgc2VsZWN0ZWRDbGFzcywgc2VsZWN0ZWRTdGF0dXNdKTtcclxuXHJcbiAgLy8gRmV0Y2ggYWRkaXRpb25hbCBkYXRhIGZvciBtb2RhbHNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgZmV0Y2hBZGRpdGlvbmFsRGF0YSA9IGFzeW5jICgpID0+IHtcclxuICAgICAgaWYgKCFzY2hvb2xJZCkgcmV0dXJuO1xyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICAvLyBGZXRjaCBkYXRhIHdpdGggaW5kaXZpZHVhbCBlcnJvciBoYW5kbGluZ1xyXG4gICAgICAgIGNvbnN0IHJlc3VsdHMgPSBhd2FpdCBQcm9taXNlLmFsbFNldHRsZWQoW1xyXG4gICAgICAgICAgZ2V0U3R1ZGVudHNCeVNjaG9vbChzY2hvb2xJZCBhcyBzdHJpbmcpLFxyXG4gICAgICAgICAgZ2V0Q2xhc3Nlc0J5U2Nob29sKHNjaG9vbElkIGFzIHN0cmluZyksXHJcbiAgICAgICAgICBnZXRTdWJqZWN0c0J5U2Nob29sSWQoc2Nob29sSWQgYXMgc3RyaW5nKSxcclxuICAgICAgICAgIGdldENsYXNzU2NoZWR1bGVzQnlTY2hvb2woc2Nob29sSWQgYXMgc3RyaW5nKVxyXG4gICAgICAgIF0pO1xyXG5cclxuICAgICAgICAvLyBIYW5kbGUgZWFjaCByZXN1bHQgaW5kaXZpZHVhbGx5XHJcbiAgICAgICAgaWYgKHJlc3VsdHNbMF0uc3RhdHVzID09PSAnZnVsZmlsbGVkJykge1xyXG4gICAgICAgICAgc2V0U3R1ZGVudHMocmVzdWx0c1swXS52YWx1ZSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggc3R1ZGVudHM6XCIsIHJlc3VsdHNbMF0ucmVhc29uKTtcclxuICAgICAgICAgIHNldFN0dWRlbnRzKFtdKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmIChyZXN1bHRzWzFdLnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcpIHtcclxuICAgICAgICAgIHNldENsYXNzZXMocmVzdWx0c1sxXS52YWx1ZSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggY2xhc3NlczpcIiwgcmVzdWx0c1sxXS5yZWFzb24pO1xyXG4gICAgICAgICAgc2V0Q2xhc3NlcyhbXSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAocmVzdWx0c1syXS5zdGF0dXMgPT09ICdmdWxmaWxsZWQnKSB7XHJcbiAgICAgICAgICBzZXRTdWJqZWN0cyhyZXN1bHRzWzJdLnZhbHVlKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBmZXRjaCBzdWJqZWN0czpcIiwgcmVzdWx0c1syXS5yZWFzb24pO1xyXG4gICAgICAgICAgc2V0U3ViamVjdHMoW10pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKHJlc3VsdHNbM10uc3RhdHVzID09PSAnZnVsZmlsbGVkJykge1xyXG4gICAgICAgICAgc2V0U2NoZWR1bGVzKHJlc3VsdHNbM10udmFsdWUpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGZldGNoIHNjaGVkdWxlczpcIiwgcmVzdWx0c1szXS5yZWFzb24pO1xyXG4gICAgICAgICAgc2V0U2NoZWR1bGVzKFtdKTtcclxuICAgICAgICAgIC8vIERvbid0IHNob3cgZXJyb3IgZm9yIHNjaGVkdWxlcyBhcyBpdCdzIG5vdCBjcml0aWNhbFxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gT25seSBzaG93IGVycm9yIGlmIGNyaXRpY2FsIGRhdGEgZmFpbGVkIHRvIGxvYWRcclxuICAgICAgICBjb25zdCBjcml0aWNhbERhdGFGYWlsZWQgPSByZXN1bHRzWzBdLnN0YXR1cyA9PT0gJ3JlamVjdGVkJyB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0c1sxXS5zdGF0dXMgPT09ICdyZWplY3RlZCcgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc3VsdHNbMl0uc3RhdHVzID09PSAncmVqZWN0ZWQnO1xyXG5cclxuICAgICAgICBpZiAoY3JpdGljYWxEYXRhRmFpbGVkKSB7XHJcbiAgICAgICAgICBzaG93RXJyb3IoXCJXYXJuaW5nXCIsIFwiU29tZSBmb3JtIGRhdGEgY291bGQgbm90IGJlIGxvYWRlZC4gU29tZSBmZWF0dXJlcyBtYXkgYmUgbGltaXRlZC5cIik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBhZGRpdGlvbmFsIGRhdGE6XCIsIGVycm9yKTtcclxuICAgICAgICBzaG93RXJyb3IoXCJFcnJvclwiLCBcIkZhaWxlZCB0byBsb2FkIGZvcm0gZGF0YVwiKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBmZXRjaEFkZGl0aW9uYWxEYXRhKCk7XHJcbiAgfSwgW3NjaG9vbElkXSk7IC8vIFJlbW92ZWQgc2hvd0Vycm9yIGZyb20gZGVwZW5kZW5jaWVzXHJcblxyXG4gIC8vIENSVUQgRnVuY3Rpb25zXHJcbiAgY29uc3QgaGFuZGxlQ3JlYXRlQXR0ZW5kYW5jZSA9ICgpID0+IHtcclxuICAgIHNldEF0dGVuZGFuY2VUb0VkaXQobnVsbCk7XHJcbiAgICBzZXRJc0F0dGVuZGFuY2VNb2RhbE9wZW4odHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRWRpdEF0dGVuZGFuY2UgPSAoYXR0ZW5kYW5jZTogQXR0ZW5kYW5jZVJlY29yZCkgPT4ge1xyXG4gICAgc2V0QXR0ZW5kYW5jZVRvRWRpdChhdHRlbmRhbmNlKTtcclxuICAgIHNldElzQXR0ZW5kYW5jZU1vZGFsT3Blbih0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVEZWxldGVBdHRlbmRhbmNlID0gKGF0dGVuZGFuY2U6IEF0dGVuZGFuY2VSZWNvcmQpID0+IHtcclxuICAgIHNldEF0dGVuZGFuY2VUb0RlbGV0ZShhdHRlbmRhbmNlKTtcclxuICAgIHNldERlbGV0ZVR5cGUoXCJzaW5nbGVcIik7XHJcbiAgICBzZXRJc0RlbGV0ZU1vZGFsT3Blbih0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVEZWxldGVNdWx0aXBsZSA9ICgpID0+IHtcclxuICAgIHNldERlbGV0ZVR5cGUoXCJtdWx0aXBsZVwiKTtcclxuICAgIHNldElzRGVsZXRlTW9kYWxPcGVuKHRydWUpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNlbGVjdGlvbkNoYW5nZSA9IChzZWxlY3RlZFJvd3M6IEF0dGVuZGFuY2VSZWNvcmRbXSkgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRBdHRlbmRhbmNlcyhzZWxlY3RlZFJvd3MpO1xyXG4gIH07XHJcblxyXG4gIC8vIE1vZGFsIHN1Ym1pc3Npb24gZnVuY3Rpb25zXHJcbiAgY29uc3QgaGFuZGxlQXR0ZW5kYW5jZVN1Ym1pdCA9IGFzeW5jIChkYXRhOiBhbnkpID0+IHtcclxuICAgIGlmICghc2Nob29sSWQpIHtcclxuICAgICAgc2hvd0Vycm9yKFwiRXJyb3JcIiwgXCJObyBzY2hvb2wgSUQgZm91bmRcIik7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBzZXRJc1N1Ym1pdHRpbmcodHJ1ZSk7XHJcbiAgICB0cnkge1xyXG4gICAgICBpZiAoYXR0ZW5kYW5jZVRvRWRpdCkge1xyXG4gICAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyBhdHRlbmRhbmNlXHJcbiAgICAgICAgYXdhaXQgdXBkYXRlQXR0ZW5kYW5jZShhdHRlbmRhbmNlVG9FZGl0Ll9pZCwgZGF0YSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgLy8gQ3JlYXRlIG5ldyBhdHRlbmRhbmNlXHJcbiAgICAgICAgYXdhaXQgY3JlYXRlQXR0ZW5kYW5jZSh7IC4uLmRhdGEsIHNjaG9vbF9pZDogc2Nob29sSWQgfSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFJlZnJlc2ggYXR0ZW5kYW5jZSBsaXN0XHJcbiAgICAgIGNvbnN0IGZpbHRlcnM6IGFueSA9IHt9O1xyXG4gICAgICBpZiAoc2VsZWN0ZWREYXRlKSBmaWx0ZXJzLmRhdGUgPSBzZWxlY3RlZERhdGU7XHJcbiAgICAgIGlmIChzZWxlY3RlZENsYXNzICE9PSAnYWxsJykgZmlsdGVycy5jbGFzc19pZCA9IHNlbGVjdGVkQ2xhc3M7XHJcbiAgICAgIGlmIChzZWxlY3RlZFN0YXR1cyAhPT0gJ2FsbCcpIGZpbHRlcnMuc3RhdHVzID0gc2VsZWN0ZWRTdGF0dXM7XHJcblxyXG4gICAgICBjb25zdCBbcmVjb3Jkc1Jlc3BvbnNlLCBzdGF0c1Jlc3BvbnNlXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcclxuICAgICAgICBnZXRBdHRlbmRhbmNlUmVjb3JkcyhzY2hvb2xJZCBhcyBzdHJpbmcsIGZpbHRlcnMpLFxyXG4gICAgICAgIGdldEF0dGVuZGFuY2VTdGF0cyhzY2hvb2xJZCBhcyBzdHJpbmcsIHNlbGVjdGVkRGF0ZSlcclxuICAgICAgXSk7XHJcblxyXG4gICAgICBzZXRBdHRlbmRhbmNlUmVjb3JkcyhyZWNvcmRzUmVzcG9uc2UuYXR0ZW5kYW5jZV9yZWNvcmRzKTtcclxuICAgICAgc2V0U3RhdHMoc3RhdHNSZXNwb25zZS5zdGF0cyk7XHJcbiAgICAgIHNldElzQXR0ZW5kYW5jZU1vZGFsT3BlbihmYWxzZSk7XHJcbiAgICAgIHNldEF0dGVuZGFuY2VUb0VkaXQobnVsbCk7XHJcblxyXG4gICAgICAvLyBTaG93IHN1Y2Nlc3Mgbm90aWZpY2F0aW9uXHJcbiAgICAgIHNob3dTdWNjZXNzKFxyXG4gICAgICAgIGF0dGVuZGFuY2VUb0VkaXQgPyBcIkF0dGVuZGFuY2UgVXBkYXRlZFwiIDogXCJBdHRlbmRhbmNlIE1hcmtlZFwiLFxyXG4gICAgICAgIGF0dGVuZGFuY2VUb0VkaXQgPyBcIkF0dGVuZGFuY2UgcmVjb3JkIGhhcyBiZWVuIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5LlwiIDogXCJBdHRlbmRhbmNlIGhhcyBiZWVuIG1hcmtlZCBzdWNjZXNzZnVsbHkuXCJcclxuICAgICAgKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzdWJtaXR0aW5nIGF0dGVuZGFuY2U6XCIsIGVycm9yKTtcclxuICAgICAgc2hvd0Vycm9yKFwiRXJyb3JcIiwgXCJGYWlsZWQgdG8gc2F2ZSBhdHRlbmRhbmNlLiBQbGVhc2UgdHJ5IGFnYWluLlwiKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc1N1Ym1pdHRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUNvbmZpcm0gPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIXNjaG9vbElkKSByZXR1cm47XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgaWYgKGRlbGV0ZVR5cGUgPT09IFwic2luZ2xlXCIgJiYgYXR0ZW5kYW5jZVRvRGVsZXRlKSB7XHJcbiAgICAgICAgYXdhaXQgZGVsZXRlQXR0ZW5kYW5jZShhdHRlbmRhbmNlVG9EZWxldGUuX2lkKTtcclxuICAgICAgfSBlbHNlIGlmIChkZWxldGVUeXBlID09PSBcIm11bHRpcGxlXCIpIHtcclxuICAgICAgICBjb25zdCBzZWxlY3RlZElkcyA9IHNlbGVjdGVkQXR0ZW5kYW5jZXMubWFwKGEgPT4gYS5faWQpO1xyXG4gICAgICAgIGF3YWl0IGRlbGV0ZU11bHRpcGxlQXR0ZW5kYW5jZXMoc2VsZWN0ZWRJZHMpO1xyXG4gICAgICAgIHNldENsZWFyU2VsZWN0aW9uKHRydWUpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBSZWZyZXNoIGF0dGVuZGFuY2UgbGlzdFxyXG4gICAgICBjb25zdCBmaWx0ZXJzOiBhbnkgPSB7fTtcclxuICAgICAgaWYgKHNlbGVjdGVkRGF0ZSkgZmlsdGVycy5kYXRlID0gc2VsZWN0ZWREYXRlO1xyXG4gICAgICBpZiAoc2VsZWN0ZWRDbGFzcyAhPT0gJ2FsbCcpIGZpbHRlcnMuY2xhc3NfaWQgPSBzZWxlY3RlZENsYXNzO1xyXG4gICAgICBpZiAoc2VsZWN0ZWRTdGF0dXMgIT09ICdhbGwnKSBmaWx0ZXJzLnN0YXR1cyA9IHNlbGVjdGVkU3RhdHVzO1xyXG5cclxuICAgICAgY29uc3QgW3JlY29yZHNSZXNwb25zZSwgc3RhdHNSZXNwb25zZV0gPSBhd2FpdCBQcm9taXNlLmFsbChbXHJcbiAgICAgICAgZ2V0QXR0ZW5kYW5jZVJlY29yZHMoc2Nob29sSWQgYXMgc3RyaW5nLCBmaWx0ZXJzKSxcclxuICAgICAgICBnZXRBdHRlbmRhbmNlU3RhdHMoc2Nob29sSWQgYXMgc3RyaW5nLCBzZWxlY3RlZERhdGUpXHJcbiAgICAgIF0pO1xyXG5cclxuICAgICAgc2V0QXR0ZW5kYW5jZVJlY29yZHMocmVjb3Jkc1Jlc3BvbnNlLmF0dGVuZGFuY2VfcmVjb3Jkcyk7XHJcbiAgICAgIHNldFN0YXRzKHN0YXRzUmVzcG9uc2Uuc3RhdHMpO1xyXG4gICAgICBzZXRJc0RlbGV0ZU1vZGFsT3BlbihmYWxzZSk7XHJcbiAgICAgIHNldEF0dGVuZGFuY2VUb0RlbGV0ZShudWxsKTtcclxuICAgICAgc2V0U2VsZWN0ZWRBdHRlbmRhbmNlcyhbXSk7XHJcblxyXG4gICAgICAvLyBTaG93IHN1Y2Nlc3Mgbm90aWZpY2F0aW9uXHJcbiAgICAgIGlmIChkZWxldGVUeXBlID09PSBcInNpbmdsZVwiKSB7XHJcbiAgICAgICAgc2hvd1N1Y2Nlc3MoXCJBdHRlbmRhbmNlIERlbGV0ZWRcIiwgXCJBdHRlbmRhbmNlIHJlY29yZCBoYXMgYmVlbiBkZWxldGVkIHN1Y2Nlc3NmdWxseS5cIik7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2hvd1N1Y2Nlc3MoXCJBdHRlbmRhbmNlcyBEZWxldGVkXCIsIGAke3NlbGVjdGVkQXR0ZW5kYW5jZXMubGVuZ3RofSBhdHRlbmRhbmNlIHJlY29yZHMgaGF2ZSBiZWVuIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5LmApO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZGVsZXRpbmcgYXR0ZW5kYW5jZShzKTpcIiwgZXJyb3IpO1xyXG4gICAgICBzaG93RXJyb3IoXCJFcnJvclwiLCBcIkZhaWxlZCB0byBkZWxldGUgYXR0ZW5kYW5jZSByZWNvcmQocykuIFBsZWFzZSB0cnkgYWdhaW4uXCIpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBnZXRTdGF0dXNDb2xvciA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xyXG4gICAgc3dpdGNoIChzdGF0dXMpIHtcclxuICAgICAgY2FzZSAnUHJlc2VudCc6XHJcbiAgICAgICAgcmV0dXJuICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAgZGFyazpiZy1ncmVlbi05MDAvMzAgZGFyazp0ZXh0LWdyZWVuLTMwMCc7XHJcbiAgICAgIGNhc2UgJ0Fic2VudCc6XHJcbiAgICAgICAgcmV0dXJuICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCBkYXJrOmJnLXJlZC05MDAvMzAgZGFyazp0ZXh0LXJlZC0zMDAnO1xyXG4gICAgICBjYXNlICdMYXRlJzpcclxuICAgICAgICByZXR1cm4gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwIGRhcms6YmcteWVsbG93LTkwMC8zMCBkYXJrOnRleHQteWVsbG93LTMwMCc7XHJcbiAgICAgIGNhc2UgJ0V4Y3VzZWQnOlxyXG4gICAgICAgIHJldHVybiAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCBkYXJrOmJnLWJsdWUtOTAwLzMwIGRhcms6dGV4dC1ibHVlLTMwMCc7XHJcbiAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgcmV0dXJuICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwIGRhcms6YmctZ3JheS05MDAvMzAgZGFyazp0ZXh0LWdyYXktMzAwJztcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBFeHBvcnQgZnVuY3Rpb25zXHJcbiAgY29uc3QgaGFuZGxlRXhwb3J0UERGID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKCFzY2hvb2xJZCkge1xyXG4gICAgICBzaG93RXJyb3IoXCJFcnJvclwiLCBcIk5vIHNjaG9vbCBJRCBmb3VuZFwiKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIHNldElzRXhwb3J0aW5nKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgZmlsdGVycyA9IHtcclxuICAgICAgICBjbGFzc19pZDogc2VsZWN0ZWRDbGFzcyAhPT0gJ2FsbCcgPyBzZWxlY3RlZENsYXNzIDogdW5kZWZpbmVkLFxyXG4gICAgICAgIGRhdGU6IHNlbGVjdGVkRGF0ZSxcclxuICAgICAgICBzdGF0dXM6IHNlbGVjdGVkU3RhdHVzICE9PSAnYWxsJyA/IHNlbGVjdGVkU3RhdHVzIDogdW5kZWZpbmVkXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBibG9iID0gYXdhaXQgZXhwb3J0QXR0ZW5kYW5jZVBERihzY2hvb2xJZCBhcyBzdHJpbmcsIGZpbHRlcnMpO1xyXG5cclxuICAgICAgLy8gQ3JlYXRlIGRvd25sb2FkIGxpbmtcclxuICAgICAgY29uc3QgdXJsID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7XHJcbiAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7XHJcbiAgICAgIGxpbmsuaHJlZiA9IHVybDtcclxuICAgICAgbGluay5kb3dubG9hZCA9IGBhdHRlbmRhbmNlLWV4cG9ydC0ke3NlbGVjdGVkRGF0ZX0ucGRmYDtcclxuICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTtcclxuICAgICAgbGluay5jbGljaygpO1xyXG4gICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGxpbmspO1xyXG4gICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTCh1cmwpO1xyXG5cclxuICAgICAgc2hvd1N1Y2Nlc3MoXCJFeHBvcnQgU3VjY2Vzc2Z1bFwiLCBcIkF0dGVuZGFuY2UgcmVjb3JkcyBoYXZlIGJlZW4gZXhwb3J0ZWQgdG8gUERGIHN1Y2Nlc3NmdWxseS5cIik7XHJcbiAgICAgIHNldFNob3dFeHBvcnREcm9wZG93bihmYWxzZSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZXhwb3J0aW5nIHRvIFBERjpcIiwgZXJyb3IpO1xyXG4gICAgICBzaG93RXJyb3IoXCJFeHBvcnQgRmFpbGVkXCIsIFwiRmFpbGVkIHRvIGV4cG9ydCBhdHRlbmRhbmNlIHRvIFBERi4gUGxlYXNlIHRyeSBhZ2Fpbi5cIik7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc0V4cG9ydGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRXhwb3J0RXhjZWwgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIXNjaG9vbElkKSB7XHJcbiAgICAgIHNob3dFcnJvcihcIkVycm9yXCIsIFwiTm8gc2Nob29sIElEIGZvdW5kXCIpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgc2V0SXNFeHBvcnRpbmcodHJ1ZSk7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBmaWx0ZXJzID0ge1xyXG4gICAgICAgIGNsYXNzX2lkOiBzZWxlY3RlZENsYXNzICE9PSAnYWxsJyA/IHNlbGVjdGVkQ2xhc3MgOiB1bmRlZmluZWQsXHJcbiAgICAgICAgZGF0ZTogc2VsZWN0ZWREYXRlLFxyXG4gICAgICAgIHN0YXR1czogc2VsZWN0ZWRTdGF0dXMgIT09ICdhbGwnID8gc2VsZWN0ZWRTdGF0dXMgOiB1bmRlZmluZWRcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IGJsb2IgPSBhd2FpdCBleHBvcnRBdHRlbmRhbmNlRXhjZWwoc2Nob29sSWQgYXMgc3RyaW5nLCBmaWx0ZXJzKTtcclxuXHJcbiAgICAgIC8vIENyZWF0ZSBkb3dubG9hZCBsaW5rXHJcbiAgICAgIGNvbnN0IHVybCA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xyXG4gICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpO1xyXG4gICAgICBsaW5rLmhyZWYgPSB1cmw7XHJcbiAgICAgIGxpbmsuZG93bmxvYWQgPSBgYXR0ZW5kYW5jZS1leHBvcnQtJHtzZWxlY3RlZERhdGV9Lnhsc3hgO1xyXG4gICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGxpbmspO1xyXG4gICAgICBsaW5rLmNsaWNrKCk7XHJcbiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7XHJcbiAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKHVybCk7XHJcblxyXG4gICAgICBzaG93U3VjY2VzcyhcIkV4cG9ydCBTdWNjZXNzZnVsXCIsIFwiQXR0ZW5kYW5jZSByZWNvcmRzIGhhdmUgYmVlbiBleHBvcnRlZCB0byBFeGNlbCBzdWNjZXNzZnVsbHkuXCIpO1xyXG4gICAgICBzZXRTaG93RXhwb3J0RHJvcGRvd24oZmFsc2UpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGV4cG9ydGluZyB0byBFeGNlbDpcIiwgZXJyb3IpO1xyXG4gICAgICBzaG93RXJyb3IoXCJFeHBvcnQgRmFpbGVkXCIsIFwiRmFpbGVkIHRvIGV4cG9ydCBhdHRlbmRhbmNlIHRvIEV4Y2VsLiBQbGVhc2UgdHJ5IGFnYWluLlwiKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzRXhwb3J0aW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBUYWJsZSBjb2x1bW5zXHJcbiAgY29uc3QgY29sdW1ucyA9IFtcclxuICAgIHtcclxuICAgICAgaGVhZGVyOiBcIlN0dWRlbnRcIixcclxuICAgICAgYWNjZXNzb3I6IChyb3c6IEF0dGVuZGFuY2VSZWNvcmQpID0+IChcclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1mb3JlZ3JvdW5kXCI+e3Jvdy5zdHVkZW50X25hbWV9PC9wPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWZvcmVncm91bmQvNjBcIj57cm93LnN0dWRlbnRfaWR9PC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApLFxyXG4gICAgICBzZWFyY2hWYWx1ZTogKHJvdzogQXR0ZW5kYW5jZVJlY29yZCkgPT4gYCR7cm93LnN0dWRlbnRfbmFtZX0gJHtyb3cuc3R1ZGVudF9pZH1gXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBoZWFkZXI6IFwiQ2xhc3NcIixcclxuICAgICAgYWNjZXNzb3I6IChyb3c6IEF0dGVuZGFuY2VSZWNvcmQpID0+IChcclxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+e3Jvdy5jbGFzc19uYW1lfTwvc3Bhbj5cclxuICAgICAgKSxcclxuICAgICAgc2VhcmNoVmFsdWU6IChyb3c6IEF0dGVuZGFuY2VSZWNvcmQpID0+IHJvdy5jbGFzc19uYW1lXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBoZWFkZXI6IFwiU3ViamVjdFwiLFxyXG4gICAgICBhY2Nlc3NvcjogKHJvdzogQXR0ZW5kYW5jZVJlY29yZCkgPT4gKFxyXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj57cm93LnN1YmplY3RfbmFtZX08L3NwYW4+XHJcbiAgICAgICksXHJcbiAgICAgIHNlYXJjaFZhbHVlOiAocm93OiBBdHRlbmRhbmNlUmVjb3JkKSA9PiByb3cuc3ViamVjdF9uYW1lXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBoZWFkZXI6IFwiUGVyaW9kXCIsXHJcbiAgICAgIGFjY2Vzc29yOiAocm93OiBBdHRlbmRhbmNlUmVjb3JkKSA9PiAoXHJcbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPlBlcmlvZCB7cm93LnBlcmlvZF9udW1iZXJ9PC9zcGFuPlxyXG4gICAgICApLFxyXG4gICAgICBzZWFyY2hWYWx1ZTogKHJvdzogQXR0ZW5kYW5jZVJlY29yZCkgPT4gYFBlcmlvZCAke3Jvdy5wZXJpb2RfbnVtYmVyfWBcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGhlYWRlcjogXCJUZWFjaGVyXCIsXHJcbiAgICAgIGFjY2Vzc29yOiAocm93OiBBdHRlbmRhbmNlUmVjb3JkKSA9PiAoXHJcbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPntyb3cudGVhY2hlcl9uYW1lfTwvc3Bhbj5cclxuICAgICAgKSxcclxuICAgICAgc2VhcmNoVmFsdWU6IChyb3c6IEF0dGVuZGFuY2VSZWNvcmQpID0+IHJvdy50ZWFjaGVyX25hbWVcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGhlYWRlcjogXCJTdGF0dXNcIixcclxuICAgICAgYWNjZXNzb3I6IChyb3c6IEF0dGVuZGFuY2VSZWNvcmQpID0+IChcclxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtnZXRTdGF0dXNDb2xvcihyb3cuc3RhdHVzKX1gfT5cclxuICAgICAgICAgIHtyb3cuc3RhdHVzfVxyXG4gICAgICAgIDwvc3Bhbj5cclxuICAgICAgKSxcclxuICAgICAgc2VhcmNoVmFsdWU6IChyb3c6IEF0dGVuZGFuY2VSZWNvcmQpID0+IHJvdy5zdGF0dXNcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGhlYWRlcjogXCJEYXRlXCIsXHJcbiAgICAgIGFjY2Vzc29yOiAocm93OiBBdHRlbmRhbmNlUmVjb3JkKSA9PiAoXHJcbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPntuZXcgRGF0ZShyb3cuZGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9PC9zcGFuPlxyXG4gICAgICApLFxyXG4gICAgICBzZWFyY2hWYWx1ZTogKHJvdzogQXR0ZW5kYW5jZVJlY29yZCkgPT4gbmV3IERhdGUocm93LmRhdGUpLnRvTG9jYWxlRGF0ZVN0cmluZygpXHJcbiAgICB9XHJcbiAgXTtcclxuXHJcbiAgLy8gQWN0aW9ucyBmb3IgdGhlIHRhYmxlXHJcbiAgY29uc3QgYWN0aW9ucyA9IFtcclxuICAgIHtcclxuICAgICAgbGFiZWw6IFwiRWRpdFwiLFxyXG4gICAgICBvbkNsaWNrOiAoYXR0ZW5kYW5jZTogQXR0ZW5kYW5jZVJlY29yZCkgPT4ge1xyXG4gICAgICAgIGhhbmRsZUVkaXRBdHRlbmRhbmNlKGF0dGVuZGFuY2UpO1xyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbGFiZWw6IFwiRGVsZXRlXCIsXHJcbiAgICAgIG9uQ2xpY2s6IChhdHRlbmRhbmNlOiBBdHRlbmRhbmNlUmVjb3JkKSA9PiB7XHJcbiAgICAgICAgaGFuZGxlRGVsZXRlQXR0ZW5kYW5jZShhdHRlbmRhbmNlKTtcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgXTtcclxuICBjb25zb2xlLmxvZyhcImF0dGVuZGFuY2VSZWNvcmRzXCIsIGF0dGVuZGFuY2VSZWNvcmRzKTtcclxuICAvLyBGaWx0ZXIgZGF0YSBiYXNlZCBvbiBzZWxlY3Rpb25zXHJcbiAgY29uc3QgZmlsdGVyZWRSZWNvcmRzID0gYXR0ZW5kYW5jZVJlY29yZHMuZmlsdGVyKHJlY29yZCA9PiB7XHJcbiAgICBpZiAoc2VsZWN0ZWRDbGFzcyAhPT0gJ2FsbCcgJiYgcmVjb3JkLmNsYXNzX25hbWUgIT09IHNlbGVjdGVkQ2xhc3MpIHJldHVybiBmYWxzZTtcclxuICAgIGlmIChzZWxlY3RlZFN0YXR1cyAhPT0gJ2FsbCcgJiYgcmVjb3JkLnN0YXR1cyAhPT0gc2VsZWN0ZWRTdGF0dXMpIHJldHVybiBmYWxzZTtcclxuICAgIHJldHVybiB0cnVlO1xyXG4gIH0pO1xyXG5cclxuICBpZiAobG9hZGluZ0RhdGEpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWJhY2tncm91bmQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICA8Q2lyY3VsYXJMb2FkZXIgc2l6ZT17MzJ9IGNvbG9yPVwidGVhbFwiIC8+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8UHJvdGVjdGVkUm91dGUgYWxsb3dlZFJvbGVzPXtbXCJzY2hvb2xfYWRtaW5cIiwgXCJhZG1pblwiLCBcInN1cGVyXCJdfT5cclxuICAgICAgPFNjaG9vbExheW91dCBuYXZpZ2F0aW9uPXtuYXZpZ2F0aW9ufSBvbkxvZ291dD17bG9nb3V0fT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICAgICAgey8qIEhlYWRlciAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2lkZ2V0IHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1zdHJva2UgcC02XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLXRlYWwgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxGaWxlQ2hlY2syIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC13aGl0ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1mb3JlZ3JvdW5kXCI+QXR0ZW5kYW5jZSBNYW5hZ2VtZW50PC9oMT5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1mb3JlZ3JvdW5kLzYwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgTW9uaXRvciBhbmQgdHJhY2sgc3R1ZGVudCBhdHRlbmRhbmNlIGFjcm9zcyBhbGwgY2xhc3Nlc1xyXG4gICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWZvcmVncm91bmRcIj57c3RhdHMuYXR0ZW5kYW5jZVJhdGV9JTwvcD5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1mb3JlZ3JvdW5kLzYwXCI+VG9kYXkncyBSYXRlPC9wPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBTdGF0cyBDYXJkcyAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtNlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdpZGdldCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItc3Ryb2tlIHAtNlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZm9yZWdyb3VuZC82MFwiPlRvdGFsIFN0dWRlbnRzPC9wPlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1mb3JlZ3JvdW5kXCI+e3N0YXRzLnRvdGFsU3R1ZGVudHN9PC9wPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ibHVlLTUwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtd2hpdGVcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aWRnZXQgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXN0cm9rZSBwLTZcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWZvcmVncm91bmQvNjBcIj5QcmVzZW50IFRvZGF5PC9wPlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj57c3RhdHMucHJlc2VudFRvZGF5fTwvcD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctZ3JlZW4tNTAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXdoaXRlXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2lkZ2V0IHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1zdHJva2UgcC02XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1mb3JlZ3JvdW5kLzYwXCI+QWJzZW50IFRvZGF5PC9wPlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1yZWQtNjAwXCI+e3N0YXRzLmFic2VudFRvZGF5fTwvcD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctcmVkLTUwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtd2hpdGVcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aWRnZXQgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXN0cm9rZSBwLTZcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWZvcmVncm91bmQvNjBcIj5MYXRlIFRvZGF5PC9wPlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC15ZWxsb3ctNjAwXCI+e3N0YXRzLmxhdGVUb2RheX08L3A+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLXllbGxvdy01MDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXdoaXRlXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBGaWx0ZXJzICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aWRnZXQgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXN0cm9rZSBwLTZcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxyXG4gICAgICAgICAgICAgICAgPEZpbHRlciBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZm9yZWdyb3VuZC82MFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZm9yZWdyb3VuZFwiPkZpbHRlcnM6PC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XHJcbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWZvcmVncm91bmQvNzBcIj5EYXRlOjwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWREYXRlfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlbGVjdGVkRGF0ZShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBib3JkZXIgYm9yZGVyLXN0cm9rZSByb3VuZGVkLW1kIHRleHQtc20gZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXRlYWwgYmctd2lkZ2V0IHRleHQtZm9yZWdyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxyXG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1mb3JlZ3JvdW5kLzcwXCI+Q2xhc3M6PC9sYWJlbD5cclxuICAgICAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkQ2xhc3N9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VsZWN0ZWRDbGFzcyhlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBib3JkZXIgYm9yZGVyLXN0cm9rZSByb3VuZGVkLW1kIHRleHQtc20gZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXRlYWwgYmctd2lkZ2V0IHRleHQtZm9yZWdyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhbGxcIj5BbGwgQ2xhc3Nlczwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICB7Y2xhc3Nlcy5tYXAoKGNsYXNzSXRlbSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtjbGFzc0l0ZW0uX2lkfSB2YWx1ZT17Y2xhc3NJdGVtLl9pZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7Y2xhc3NJdGVtLm5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgPC9zZWxlY3Q+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XHJcbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWZvcmVncm91bmQvNzBcIj5TdGF0dXM6PC9sYWJlbD5cclxuICAgICAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkU3RhdHVzfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlbGVjdGVkU3RhdHVzKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIGJvcmRlciBib3JkZXItc3Ryb2tlIHJvdW5kZWQtbWQgdGV4dC1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctdGVhbCBiZy13aWRnZXQgdGV4dC1mb3JlZ3JvdW5kXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFsbFwiPkFsbCBTdGF0dXM8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlByZXNlbnRcIj5QcmVzZW50PC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJBYnNlbnRcIj5BYnNlbnQ8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkxhdGVcIj5MYXRlPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJFeGN1c2VkXCI+RXhjdXNlZDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgPC9zZWxlY3Q+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cclxuICAgICAgICAgICAgICAgIDxtb3Rpb24uYnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDUgfX1cclxuICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0V4cG9ydERyb3Bkb3duKCFzaG93RXhwb3J0RHJvcGRvd24pfVxyXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNFeHBvcnRpbmd9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweC00IHB5LTIgYmctdGVhbCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgaG92ZXI6YmctdGVhbC02MDAgdGV4dC1zbSBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPERvd25sb2FkIHNpemU9ezE0fSAvPlxyXG4gICAgICAgICAgICAgICAgICA8c3Bhbj57aXNFeHBvcnRpbmcgPyAnRXhwb3J0aW5nLi4uJyA6ICdFeHBvcnQnfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cclxuXHJcbiAgICAgICAgICAgICAgICB7c2hvd0V4cG9ydERyb3Bkb3duICYmIChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0wIG10LTIgdy00OCBiZy13aWRnZXQgYm9yZGVyIGJvcmRlci1zdHJva2Ugcm91bmRlZC1tZCBzaGFkb3ctbGcgei0xMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUV4cG9ydFBERn1cclxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0V4cG9ydGluZ31cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWxlZnQgcHgtNCBweS0yIHRleHQtc20gdGV4dC1mb3JlZ3JvdW5kIGhvdmVyOmJnLWdyYXktMTAwIGRhcms6aG92ZXI6YmctZ3JheS04MDAgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgRXhwb3J0IGFzIFBERlxyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUV4cG9ydEV4Y2VsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRXhwb3J0aW5nfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtbGVmdCBweC00IHB5LTIgdGV4dC1zbSB0ZXh0LWZvcmVncm91bmQgaG92ZXI6YmctZ3JheS0xMDAgZGFyazpob3ZlcjpiZy1ncmF5LTgwMCBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICBFeHBvcnQgYXMgRXhjZWxcclxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBBdHRlbmRhbmNlIFRhYmxlICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aWRnZXQgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXN0cm9rZSBwLTZcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxyXG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICBBdHRlbmRhbmNlIFJlY29yZHMgKHtmaWx0ZXJlZFJlY29yZHMubGVuZ3RofSlcclxuICAgICAgICAgICAgICA8L2gyPlxyXG5cclxuICAgICAgICAgICAgICA8bW90aW9uLmJ1dHRvblxyXG4gICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wNSB9fVxyXG4gICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNyZWF0ZUF0dGVuZGFuY2V9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtNCBweS0yIGJnLXRlYWwgdGV4dC13aGl0ZSByb3VuZGVkLW1kIGhvdmVyOmJnLXRlYWwtNjAwXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8UGx1cyBzaXplPXsxNn0gLz5cclxuICAgICAgICAgICAgICAgIDxzcGFuPk1hcmsgQXR0ZW5kYW5jZTwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L21vdGlvbi5idXR0b24+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgPFN1c3BlbnNlIGZhbGxiYWNrPXs8Q2lyY3VsYXJMb2FkZXIgc2l6ZT17MjR9IGNvbG9yPVwidGVhbFwiIC8+fT5cclxuICAgICAgICAgICAgICA8RGF0YVRhYmxlRml4PEF0dGVuZGFuY2VSZWNvcmQ+XHJcbiAgICAgICAgICAgICAgICBkYXRhPXtmaWx0ZXJlZFJlY29yZHN9XHJcbiAgICAgICAgICAgICAgICBjb2x1bW5zPXtjb2x1bW5zfVxyXG4gICAgICAgICAgICAgICAgYWN0aW9ucz17YWN0aW9uc31cclxuICAgICAgICAgICAgICAgIGRlZmF1bHRJdGVtc1BlclBhZ2U9ezE1fVxyXG4gICAgICAgICAgICAgICAgb25TZWxlY3Rpb25DaGFuZ2U9e2hhbmRsZVNlbGVjdGlvbkNoYW5nZX1cclxuICAgICAgICAgICAgICAgIGhhbmRsZURlbGV0ZU11bHRpcGxlPXtoYW5kbGVEZWxldGVNdWx0aXBsZX1cclxuICAgICAgICAgICAgICAgIGNsZWFyU2VsZWN0aW9uPXtjbGVhclNlbGVjdGlvbn1cclxuICAgICAgICAgICAgICAgIG9uU2VsZWN0aW9uQ2xlYXJlZD17KCkgPT4gc2V0Q2xlYXJTZWxlY3Rpb24oZmFsc2UpfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvU3VzcGVuc2U+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qIEF0dGVuZGFuY2UgTW9kYWwgKi99XHJcbiAgICAgICAgPEF0dGVuZGFuY2VNb2RhbFxyXG4gICAgICAgICAgaXNPcGVuPXtpc0F0dGVuZGFuY2VNb2RhbE9wZW59XHJcbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiB7XHJcbiAgICAgICAgICAgIHNldElzQXR0ZW5kYW5jZU1vZGFsT3BlbihmYWxzZSk7XHJcbiAgICAgICAgICAgIHNldEF0dGVuZGFuY2VUb0VkaXQobnVsbCk7XHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgICAgb25TdWJtaXQ9e2hhbmRsZUF0dGVuZGFuY2VTdWJtaXR9XHJcbiAgICAgICAgICBhdHRlbmRhbmNlPXthdHRlbmRhbmNlVG9FZGl0fVxyXG4gICAgICAgICAgc3R1ZGVudHM9e3N0dWRlbnRzfVxyXG4gICAgICAgICAgY2xhc3Nlcz17Y2xhc3Nlc31cclxuICAgICAgICAgIHN1YmplY3RzPXtzdWJqZWN0c31cclxuICAgICAgICAgIHNjaGVkdWxlcz17c2NoZWR1bGVzfVxyXG4gICAgICAgICAgbG9hZGluZz17aXNTdWJtaXR0aW5nfVxyXG4gICAgICAgIC8+XHJcblxyXG4gICAgICAgIHsvKiBEZWxldGUgQ29uZmlybWF0aW9uIE1vZGFsICovfVxyXG4gICAgICAgIDxEZWxldGVDb25maXJtYXRpb25Nb2RhbFxyXG4gICAgICAgICAgaXNPcGVuPXtpc0RlbGV0ZU1vZGFsT3Blbn1cclxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHtcclxuICAgICAgICAgICAgc2V0SXNEZWxldGVNb2RhbE9wZW4oZmFsc2UpO1xyXG4gICAgICAgICAgICBzZXRBdHRlbmRhbmNlVG9EZWxldGUobnVsbCk7XHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgICAgb25Db25maXJtPXtoYW5kbGVEZWxldGVDb25maXJtfVxyXG4gICAgICAgICAgdGl0bGU9e1xyXG4gICAgICAgICAgICBkZWxldGVUeXBlID09PSBcInNpbmdsZVwiXHJcbiAgICAgICAgICAgICAgPyBcIkRlbGV0ZSBBdHRlbmRhbmNlIFJlY29yZFwiXHJcbiAgICAgICAgICAgICAgOiBcIkRlbGV0ZSBTZWxlY3RlZCBBdHRlbmRhbmNlIFJlY29yZHNcIlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgbWVzc2FnZT17XHJcbiAgICAgICAgICAgIGRlbGV0ZVR5cGUgPT09IFwic2luZ2xlXCJcclxuICAgICAgICAgICAgICA/IFwiQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB0aGlzIGF0dGVuZGFuY2UgcmVjb3JkPyBUaGlzIGFjdGlvbiBjYW5ub3QgYmUgdW5kb25lLlwiXHJcbiAgICAgICAgICAgICAgOiBgQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSAke3NlbGVjdGVkQXR0ZW5kYW5jZXMubGVuZ3RofSBzZWxlY3RlZCBhdHRlbmRhbmNlIHJlY29yZHM/IFRoaXMgYWN0aW9uIGNhbm5vdCBiZSB1bmRvbmUuYFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgaXRlbU5hbWU9e1xyXG4gICAgICAgICAgICBkZWxldGVUeXBlID09PSBcInNpbmdsZVwiICYmIGF0dGVuZGFuY2VUb0RlbGV0ZVxyXG4gICAgICAgICAgICAgID8gYCR7YXR0ZW5kYW5jZVRvRGVsZXRlLnN0dWRlbnRfbmFtZX0gLSAke2F0dGVuZGFuY2VUb0RlbGV0ZS5zdWJqZWN0X25hbWV9ICgke25ldyBEYXRlKGF0dGVuZGFuY2VUb0RlbGV0ZS5kYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoKX0pYFxyXG4gICAgICAgICAgICAgIDogdW5kZWZpbmVkXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBpdGVtQ291bnQ9e2RlbGV0ZVR5cGUgPT09IFwibXVsdGlwbGVcIiA/IHNlbGVjdGVkQXR0ZW5kYW5jZXMubGVuZ3RoIDogdW5kZWZpbmVkfVxyXG4gICAgICAgICAgdHlwZT17ZGVsZXRlVHlwZX1cclxuICAgICAgICAvPlxyXG5cclxuICAgICAgICB7LyogVG9hc3QgTm90aWZpY2F0aW9ucyAqL31cclxuICAgICAgICA8VG9hc3RDb250YWluZXIgdG9hc3RzPXt0b2FzdHN9IG9uQ2xvc2U9e3JlbW92ZVRvYXN0fSAvPlxyXG4gICAgICA8L1NjaG9vbExheW91dD5cclxuICAgIDwvUHJvdGVjdGVkUm91dGU+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiRmlsZUNoZWNrMiIsIkNhbGVuZGFyIiwiVXNlcnMiLCJUcmVuZGluZ1VwIiwiRmlsdGVyIiwiRG93bmxvYWQiLCJQbHVzIiwiU2Nob29sTGF5b3V0IiwiU3VzcGVuc2UiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIkRhdGFUYWJsZUZpeCIsIkNpcmN1bGFyTG9hZGVyIiwidXNlQXV0aCIsIlByb3RlY3RlZFJvdXRlIiwibW90aW9uIiwiZ2V0QXR0ZW5kYW5jZVJlY29yZHMiLCJnZXRBdHRlbmRhbmNlU3RhdHMiLCJjcmVhdGVBdHRlbmRhbmNlIiwidXBkYXRlQXR0ZW5kYW5jZSIsImRlbGV0ZUF0dGVuZGFuY2UiLCJkZWxldGVNdWx0aXBsZUF0dGVuZGFuY2VzIiwiZXhwb3J0QXR0ZW5kYW5jZVBERiIsImV4cG9ydEF0dGVuZGFuY2VFeGNlbCIsImdldFN0dWRlbnRzQnlTY2hvb2wiLCJnZXRDbGFzc2VzQnlTY2hvb2wiLCJnZXRTdWJqZWN0c0J5U2Nob29sSWQiLCJnZXRDbGFzc1NjaGVkdWxlc0J5U2Nob29sIiwiQXR0ZW5kYW5jZU1vZGFsIiwiRGVsZXRlQ29uZmlybWF0aW9uTW9kYWwiLCJ1c2VUb2FzdCIsIlRvYXN0Q29udGFpbmVyIiwibmF2aWdhdGlvbiIsImljb24iLCJiYXNlSHJlZiIsInRpdGxlIiwiQXR0ZW5kYW5jZVBhZ2UiLCJ1c2VyIiwibG9nb3V0IiwidG9hc3RzIiwicmVtb3ZlVG9hc3QiLCJzaG93U3VjY2VzcyIsInNob3dFcnJvciIsImF0dGVuZGFuY2VSZWNvcmRzIiwic2V0QXR0ZW5kYW5jZVJlY29yZHMiLCJzdGF0cyIsInNldFN0YXRzIiwidG90YWxTdHVkZW50cyIsInByZXNlbnRUb2RheSIsImFic2VudFRvZGF5IiwibGF0ZVRvZGF5IiwiZXhjdXNlZFRvZGF5IiwiYXR0ZW5kYW5jZVJhdGUiLCJsb2FkaW5nRGF0YSIsInNldExvYWRpbmdEYXRhIiwic2VsZWN0ZWREYXRlIiwic2V0U2VsZWN0ZWREYXRlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJzZWxlY3RlZENsYXNzIiwic2V0U2VsZWN0ZWRDbGFzcyIsInNlbGVjdGVkU3RhdHVzIiwic2V0U2VsZWN0ZWRTdGF0dXMiLCJpc0F0dGVuZGFuY2VNb2RhbE9wZW4iLCJzZXRJc0F0dGVuZGFuY2VNb2RhbE9wZW4iLCJpc0RlbGV0ZU1vZGFsT3BlbiIsInNldElzRGVsZXRlTW9kYWxPcGVuIiwiYXR0ZW5kYW5jZVRvRWRpdCIsInNldEF0dGVuZGFuY2VUb0VkaXQiLCJhdHRlbmRhbmNlVG9EZWxldGUiLCJzZXRBdHRlbmRhbmNlVG9EZWxldGUiLCJkZWxldGVUeXBlIiwic2V0RGVsZXRlVHlwZSIsInNlbGVjdGVkQXR0ZW5kYW5jZXMiLCJzZXRTZWxlY3RlZEF0dGVuZGFuY2VzIiwic3R1ZGVudHMiLCJzZXRTdHVkZW50cyIsImNsYXNzZXMiLCJzZXRDbGFzc2VzIiwic3ViamVjdHMiLCJzZXRTdWJqZWN0cyIsInNjaGVkdWxlcyIsInNldFNjaGVkdWxlcyIsImlzU3VibWl0dGluZyIsInNldElzU3VibWl0dGluZyIsImNsZWFyU2VsZWN0aW9uIiwic2V0Q2xlYXJTZWxlY3Rpb24iLCJpc0V4cG9ydGluZyIsInNldElzRXhwb3J0aW5nIiwic2hvd0V4cG9ydERyb3Bkb3duIiwic2V0U2hvd0V4cG9ydERyb3Bkb3duIiwic2Nob29sSWQiLCJzY2hvb2xfaWRzIiwic2Nob29sX2lkIiwiZmV0Y2hBdHRlbmRhbmNlRGF0YSIsImZpbHRlcnMiLCJkYXRlIiwiY2xhc3NfaWQiLCJzdGF0dXMiLCJyZWNvcmRzUmVzcG9uc2UiLCJzdGF0c1Jlc3BvbnNlIiwiUHJvbWlzZSIsImFsbCIsImF0dGVuZGFuY2VfcmVjb3JkcyIsImVycm9yIiwiY29uc29sZSIsImZldGNoQWRkaXRpb25hbERhdGEiLCJyZXN1bHRzIiwiYWxsU2V0dGxlZCIsInZhbHVlIiwicmVhc29uIiwiY3JpdGljYWxEYXRhRmFpbGVkIiwiaGFuZGxlQ3JlYXRlQXR0ZW5kYW5jZSIsImhhbmRsZUVkaXRBdHRlbmRhbmNlIiwiYXR0ZW5kYW5jZSIsImhhbmRsZURlbGV0ZUF0dGVuZGFuY2UiLCJoYW5kbGVEZWxldGVNdWx0aXBsZSIsImhhbmRsZVNlbGVjdGlvbkNoYW5nZSIsInNlbGVjdGVkUm93cyIsImhhbmRsZUF0dGVuZGFuY2VTdWJtaXQiLCJkYXRhIiwiX2lkIiwiaGFuZGxlRGVsZXRlQ29uZmlybSIsInNlbGVjdGVkSWRzIiwibWFwIiwiYSIsImxlbmd0aCIsImdldFN0YXR1c0NvbG9yIiwiaGFuZGxlRXhwb3J0UERGIiwidW5kZWZpbmVkIiwiYmxvYiIsInVybCIsIndpbmRvdyIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsImxpbmsiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZG93bmxvYWQiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJjbGljayIsInJlbW92ZUNoaWxkIiwicmV2b2tlT2JqZWN0VVJMIiwiaGFuZGxlRXhwb3J0RXhjZWwiLCJjb2x1bW5zIiwiaGVhZGVyIiwiYWNjZXNzb3IiLCJyb3ciLCJkaXYiLCJwIiwiY2xhc3NOYW1lIiwic3R1ZGVudF9uYW1lIiwic3R1ZGVudF9pZCIsInNlYXJjaFZhbHVlIiwic3BhbiIsImNsYXNzX25hbWUiLCJzdWJqZWN0X25hbWUiLCJwZXJpb2RfbnVtYmVyIiwidGVhY2hlcl9uYW1lIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwiYWN0aW9ucyIsImxhYmVsIiwib25DbGljayIsImxvZyIsImZpbHRlcmVkUmVjb3JkcyIsImZpbHRlciIsInJlY29yZCIsInNpemUiLCJjb2xvciIsImFsbG93ZWRSb2xlcyIsIm9uTG9nb3V0IiwiaDEiLCJpbnB1dCIsInR5cGUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJzZWxlY3QiLCJvcHRpb24iLCJjbGFzc0l0ZW0iLCJuYW1lIiwiYnV0dG9uIiwid2hpbGVIb3ZlciIsInNjYWxlIiwid2hpbGVUYXAiLCJkaXNhYmxlZCIsImgyIiwiZmFsbGJhY2siLCJkZWZhdWx0SXRlbXNQZXJQYWdlIiwib25TZWxlY3Rpb25DaGFuZ2UiLCJvblNlbGVjdGlvbkNsZWFyZWQiLCJpc09wZW4iLCJvbkNsb3NlIiwib25TdWJtaXQiLCJsb2FkaW5nIiwib25Db25maXJtIiwibWVzc2FnZSIsIml0ZW1OYW1lIiwiaXRlbUNvdW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/attendance/page.tsx\n"));

/***/ })

});