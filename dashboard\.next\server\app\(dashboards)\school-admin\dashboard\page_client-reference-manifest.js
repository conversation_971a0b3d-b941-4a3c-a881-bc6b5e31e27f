globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(dashboards)/school-admin/dashboard/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/services/AuthContext.tsx":{"*":{"id":"(ssr)/./src/app/services/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/utils/ThemeInitializer.tsx":{"*":{"id":"(ssr)/./src/utils/ThemeInitializer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(auth)/login/page.tsx":{"*":{"id":"(ssr)/./src/app/(auth)/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/super-admin/layout.tsx":{"*":{"id":"(ssr)/./src/app/(dashboards)/super-admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/super-admin/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboards)/super-admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboards)/school-admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboards)/school-admin/grades/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/timetable/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboards)/school-admin/timetable/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/school/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboards)/school-admin/school/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/period/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboards)/school-admin/period/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/attendance/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboards)/school-admin/attendance/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Bricolage_Grotesque\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"700\"]}],\"variableName\":\"bricolage\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Bricolage_Grotesque\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"700\"]}],\"variableName\":\"bricolage\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\services\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/app/services/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\utils\\ThemeInitializer.tsx":{"id":"(app-pages-browser)/./src/utils/ThemeInitializer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\(auth)\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/(auth)/login/page.tsx","name":"*","chunks":[],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\(dashboards)\\super-admin\\layout.tsx":{"id":"(app-pages-browser)/./src/app/(dashboards)/super-admin/layout.tsx","name":"*","chunks":[],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\(dashboards)\\super-admin\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboards)/super-admin/dashboard/page.tsx","name":"*","chunks":[],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\(dashboards)\\school-admin\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboards)/school-admin/dashboard/page.tsx","name":"*","chunks":["app/(dashboards)/school-admin/dashboard/page","static/chunks/app/(dashboards)/school-admin/dashboard/page.js"],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\(dashboards)\\school-admin\\grades\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx","name":"*","chunks":[],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\(dashboards)\\school-admin\\timetable\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboards)/school-admin/timetable/page.tsx","name":"*","chunks":[],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\(dashboards)\\school-admin\\school\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboards)/school-admin/school/page.tsx","name":"*","chunks":[],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\(dashboards)\\school-admin\\teacher-assignment\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx","name":"*","chunks":[],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\(dashboards)\\school-admin\\period\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboards)/school-admin/period/page.tsx","name":"*","chunks":[],"async":false},"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\(dashboards)\\school-admin\\attendance\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboards)/school-admin/attendance/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\":[],"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\page":[],"D:\\elysee\\Projet\\Pers\\scholarify\\dashboard\\src\\app\\(dashboards)\\school-admin\\dashboard\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/services/AuthContext.tsx":{"*":{"id":"(rsc)/./src/app/services/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/utils/ThemeInitializer.tsx":{"*":{"id":"(rsc)/./src/utils/ThemeInitializer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(auth)/login/page.tsx":{"*":{"id":"(rsc)/./src/app/(auth)/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/super-admin/layout.tsx":{"*":{"id":"(rsc)/./src/app/(dashboards)/super-admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/super-admin/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboards)/super-admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboards)/school-admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboards)/school-admin/grades/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/timetable/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboards)/school-admin/timetable/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/school/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboards)/school-admin/school/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/period/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboards)/school-admin/period/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboards)/school-admin/attendance/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboards)/school-admin/attendance/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}