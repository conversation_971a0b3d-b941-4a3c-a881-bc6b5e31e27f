/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs":
/*!***************************************************!*\
  !*** ./node_modules/js-cookie/dist/js.cookie.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ api)\n/* harmony export */ });\n/*! js-cookie v3.0.5 | MIT */\n/* eslint-disable no-var */\nfunction assign (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      target[key] = source[key];\n    }\n  }\n  return target\n}\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\nvar defaultConverter = {\n  read: function (value) {\n    if (value[0] === '\"') {\n      value = value.slice(1, -1);\n    }\n    return value.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent)\n  },\n  write: function (value) {\n    return encodeURIComponent(value).replace(\n      /%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,\n      decodeURIComponent\n    )\n  }\n};\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\n\nfunction init (converter, defaultAttributes) {\n  function set (name, value, attributes) {\n    if (typeof document === 'undefined') {\n      return\n    }\n\n    attributes = assign({}, defaultAttributes, attributes);\n\n    if (typeof attributes.expires === 'number') {\n      attributes.expires = new Date(Date.now() + attributes.expires * 864e5);\n    }\n    if (attributes.expires) {\n      attributes.expires = attributes.expires.toUTCString();\n    }\n\n    name = encodeURIComponent(name)\n      .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n      .replace(/[()]/g, escape);\n\n    var stringifiedAttributes = '';\n    for (var attributeName in attributes) {\n      if (!attributes[attributeName]) {\n        continue\n      }\n\n      stringifiedAttributes += '; ' + attributeName;\n\n      if (attributes[attributeName] === true) {\n        continue\n      }\n\n      // Considers RFC 6265 section 5.2:\n      // ...\n      // 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n      //     character:\n      // Consume the characters of the unparsed-attributes up to,\n      // not including, the first %x3B (\";\") character.\n      // ...\n      stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n    }\n\n    return (document.cookie =\n      name + '=' + converter.write(value, name) + stringifiedAttributes)\n  }\n\n  function get (name) {\n    if (typeof document === 'undefined' || (arguments.length && !name)) {\n      return\n    }\n\n    // To prevent the for loop in the first place assign an empty array\n    // in case there are no cookies at all.\n    var cookies = document.cookie ? document.cookie.split('; ') : [];\n    var jar = {};\n    for (var i = 0; i < cookies.length; i++) {\n      var parts = cookies[i].split('=');\n      var value = parts.slice(1).join('=');\n\n      try {\n        var found = decodeURIComponent(parts[0]);\n        jar[found] = converter.read(value, found);\n\n        if (name === found) {\n          break\n        }\n      } catch (e) {}\n    }\n\n    return name ? jar[name] : jar\n  }\n\n  return Object.create(\n    {\n      set,\n      get,\n      remove: function (name, attributes) {\n        set(\n          name,\n          '',\n          assign({}, attributes, {\n            expires: -1\n          })\n        );\n      },\n      withAttributes: function (attributes) {\n        return init(this.converter, assign({}, this.attributes, attributes))\n      },\n      withConverter: function (converter) {\n        return init(assign({}, this.converter, converter), this.attributes)\n      }\n    },\n    {\n      attributes: { value: Object.freeze(defaultAttributes) },\n      converter: { value: Object.freeze(converter) }\n    }\n  )\n}\n\nvar api = init(defaultConverter, { path: '/' });\n/* eslint-enable no-var */\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js":
/*!****************************************************!*\
  !*** ./node_modules/jwt-decode/build/esm/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidTokenError: () => (/* binding */ InvalidTokenError),\n/* harmony export */   jwtDecode: () => (/* binding */ jwtDecode)\n/* harmony export */ });\nclass InvalidTokenError extends Error {\n}\nInvalidTokenError.prototype.name = \"InvalidTokenError\";\nfunction b64DecodeUnicode(str) {\n    return decodeURIComponent(atob(str).replace(/(.)/g, (m, p) => {\n        let code = p.charCodeAt(0).toString(16).toUpperCase();\n        if (code.length < 2) {\n            code = \"0\" + code;\n        }\n        return \"%\" + code;\n    }));\n}\nfunction base64UrlDecode(str) {\n    let output = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n    switch (output.length % 4) {\n        case 0:\n            break;\n        case 2:\n            output += \"==\";\n            break;\n        case 3:\n            output += \"=\";\n            break;\n        default:\n            throw new Error(\"base64 string is not of the correct length\");\n    }\n    try {\n        return b64DecodeUnicode(output);\n    }\n    catch (err) {\n        return atob(output);\n    }\n}\nfunction jwtDecode(token, options) {\n    if (typeof token !== \"string\") {\n        throw new InvalidTokenError(\"Invalid token specified: must be a string\");\n    }\n    options || (options = {});\n    const pos = options.header === true ? 0 : 1;\n    const part = token.split(\".\")[pos];\n    if (typeof part !== \"string\") {\n        throw new InvalidTokenError(`Invalid token specified: missing part #${pos + 1}`);\n    }\n    let decoded;\n    try {\n        decoded = base64UrlDecode(part);\n    }\n    catch (e) {\n        throw new InvalidTokenError(`Invalid token specified: invalid base64 for part #${pos + 1} (${e.message})`);\n    }\n    try {\n        return JSON.parse(decoded);\n    }\n    catch (e) {\n        throw new InvalidTokenError(`Invalid token specified: invalid json for part #${pos + 1} (${e.message})`);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9qd3QtZGVjb2RlL2J1aWxkL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBO0FBQ0EsOEVBQThFLFFBQVE7QUFDdEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUZBQXlGLFNBQVMsR0FBRyxVQUFVO0FBQy9HO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1RkFBdUYsU0FBUyxHQUFHLFVBQVU7QUFDN0c7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGVseXNlZVxcUHJvamV0XFxQZXJzXFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcand0LWRlY29kZVxcYnVpbGRcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIEludmFsaWRUb2tlbkVycm9yIGV4dGVuZHMgRXJyb3Ige1xufVxuSW52YWxpZFRva2VuRXJyb3IucHJvdG90eXBlLm5hbWUgPSBcIkludmFsaWRUb2tlbkVycm9yXCI7XG5mdW5jdGlvbiBiNjREZWNvZGVVbmljb2RlKHN0cikge1xuICAgIHJldHVybiBkZWNvZGVVUklDb21wb25lbnQoYXRvYihzdHIpLnJlcGxhY2UoLyguKS9nLCAobSwgcCkgPT4ge1xuICAgICAgICBsZXQgY29kZSA9IHAuY2hhckNvZGVBdCgwKS50b1N0cmluZygxNikudG9VcHBlckNhc2UoKTtcbiAgICAgICAgaWYgKGNvZGUubGVuZ3RoIDwgMikge1xuICAgICAgICAgICAgY29kZSA9IFwiMFwiICsgY29kZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gXCIlXCIgKyBjb2RlO1xuICAgIH0pKTtcbn1cbmZ1bmN0aW9uIGJhc2U2NFVybERlY29kZShzdHIpIHtcbiAgICBsZXQgb3V0cHV0ID0gc3RyLnJlcGxhY2UoLy0vZywgXCIrXCIpLnJlcGxhY2UoL18vZywgXCIvXCIpO1xuICAgIHN3aXRjaCAob3V0cHV0Lmxlbmd0aCAlIDQpIHtcbiAgICAgICAgY2FzZSAwOlxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgMjpcbiAgICAgICAgICAgIG91dHB1dCArPSBcIj09XCI7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAzOlxuICAgICAgICAgICAgb3V0cHV0ICs9IFwiPVwiO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJiYXNlNjQgc3RyaW5nIGlzIG5vdCBvZiB0aGUgY29ycmVjdCBsZW5ndGhcIik7XG4gICAgfVxuICAgIHRyeSB7XG4gICAgICAgIHJldHVybiBiNjREZWNvZGVVbmljb2RlKG91dHB1dCk7XG4gICAgfVxuICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgcmV0dXJuIGF0b2Iob3V0cHV0KTtcbiAgICB9XG59XG5leHBvcnQgZnVuY3Rpb24gand0RGVjb2RlKHRva2VuLCBvcHRpb25zKSB7XG4gICAgaWYgKHR5cGVvZiB0b2tlbiAhPT0gXCJzdHJpbmdcIikge1xuICAgICAgICB0aHJvdyBuZXcgSW52YWxpZFRva2VuRXJyb3IoXCJJbnZhbGlkIHRva2VuIHNwZWNpZmllZDogbXVzdCBiZSBhIHN0cmluZ1wiKTtcbiAgICB9XG4gICAgb3B0aW9ucyB8fCAob3B0aW9ucyA9IHt9KTtcbiAgICBjb25zdCBwb3MgPSBvcHRpb25zLmhlYWRlciA9PT0gdHJ1ZSA/IDAgOiAxO1xuICAgIGNvbnN0IHBhcnQgPSB0b2tlbi5zcGxpdChcIi5cIilbcG9zXTtcbiAgICBpZiAodHlwZW9mIHBhcnQgIT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEludmFsaWRUb2tlbkVycm9yKGBJbnZhbGlkIHRva2VuIHNwZWNpZmllZDogbWlzc2luZyBwYXJ0ICMke3BvcyArIDF9YCk7XG4gICAgfVxuICAgIGxldCBkZWNvZGVkO1xuICAgIHRyeSB7XG4gICAgICAgIGRlY29kZWQgPSBiYXNlNjRVcmxEZWNvZGUocGFydCk7XG4gICAgfVxuICAgIGNhdGNoIChlKSB7XG4gICAgICAgIHRocm93IG5ldyBJbnZhbGlkVG9rZW5FcnJvcihgSW52YWxpZCB0b2tlbiBzcGVjaWZpZWQ6IGludmFsaWQgYmFzZTY0IGZvciBwYXJ0ICMke3BvcyArIDF9ICgke2UubWVzc2FnZX0pYCk7XG4gICAgfVxuICAgIHRyeSB7XG4gICAgICAgIHJldHVybiBKU09OLnBhcnNlKGRlY29kZWQpO1xuICAgIH1cbiAgICBjYXRjaCAoZSkge1xuICAgICAgICB0aHJvdyBuZXcgSW52YWxpZFRva2VuRXJyb3IoYEludmFsaWQgdG9rZW4gc3BlY2lmaWVkOiBpbnZhbGlkIGpzb24gZm9yIHBhcnQgIyR7cG9zICsgMX0gKCR7ZS5tZXNzYWdlfSlgKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Bricolage_Grotesque\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"700\"]}],\"variableName\":\"bricolage\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Bricolage_Grotesque\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"bricolage\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/services/AuthContext.tsx */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/utils/ThemeInitializer.tsx */ \"(app-pages-browser)/./src/utils/ThemeInitializer.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={529:(e,r,t)=>{var n=t(191);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},191:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(529);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxlbHlzZWVcXFByb2pldFxcUGVyc1xcc2Nob2xhcmlmeVxcZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Bricolage_Grotesque\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"700\"]}],\"variableName\":\"bricolage\"}":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Bricolage_Grotesque","arguments":[{"subsets":["latin"],"weight":["400","700"]}],"variableName":"bricolage"} ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Bricolage Grotesque', 'Bricolage Grotesque Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_d7a33c\"};\n    if(true) {\n      // 1749807962008\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkJyaWNvbGFnZV9Hcm90ZXNxdWVcIixcImFyZ3VtZW50c1wiOlt7XCJzdWJzZXRzXCI6W1wibGF0aW5cIl0sXCJ3ZWlnaHRcIjpbXCI0MDBcIixcIjcwMFwiXX1dLFwidmFyaWFibGVOYW1lXCI6XCJicmljb2xhZ2VcIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUywwRkFBMEY7QUFDckgsT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQWdJLGNBQWMsc0RBQXNEO0FBQ2xPLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxlbHlzZWVcXFByb2pldFxcUGVyc1xcc2Nob2xhcmlmeVxcZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGZvbnRcXGdvb2dsZVxcdGFyZ2V0LmNzcz97XCJwYXRoXCI6XCJzcmNcXGFwcFxcbGF5b3V0LnRzeFwiLFwiaW1wb3J0XCI6XCJCcmljb2xhZ2VfR3JvdGVzcXVlXCIsXCJhcmd1bWVudHNcIjpbe1wic3Vic2V0c1wiOltcImxhdGluXCJdLFwid2VpZ2h0XCI6W1wiNDAwXCIsXCI3MDBcIl19XSxcInZhcmlhYmxlTmFtZVwiOlwiYnJpY29sYWdlXCJ9fGFwcC1wYWdlcy1icm93c2VyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJzdHlsZVwiOntcImZvbnRGYW1pbHlcIjpcIidCcmljb2xhZ2UgR3JvdGVzcXVlJywgJ0JyaWNvbGFnZSBHcm90ZXNxdWUgRmFsbGJhY2snXCIsXCJmb250U3R5bGVcIjpcIm5vcm1hbFwifSxcImNsYXNzTmFtZVwiOlwiX19jbGFzc05hbWVfZDdhMzNjXCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NDk4MDc5NjIwMDhcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiRDovZWx5c2VlL1Byb2pldC9QZXJzL3NjaG9sYXJpZnkvZGFzaGJvYXJkL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Bricolage_Grotesque\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"700\"]}],\"variableName\":\"bricolage\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1aece5d8faba\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcZWx5c2VlXFxQcm9qZXRcXFBlcnNcXHNjaG9sYXJpZnlcXGRhc2hib2FyZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMWFlY2U1ZDhmYWJhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/AuthContext.tsx":
/*!******************************************!*\
  !*** ./src/app/services/AuthContext.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   BASE_API_URL: () => (/* binding */ BASE_API_URL)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _UserServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(app-pages-browser)/./src/app/utils/httpInterceptor.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ BASE_API_URL,AuthContext,AuthProvider auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst BASE_API_URL = process.env.BASE_API_URL || \"https://scolarify.onrender.com/api\";\n// Create a context for authentication\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(undefined);\n// composant AuthProvider qui fournit le contexte d'authentification\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [redirectAfterLogin, setRedirectAfterLogin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [authCheckInterval, setAuthCheckInterval] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Fonction pour forcer la déconnexion\n    const forceLogout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[forceLogout]\": ()=>{\n            console.warn(\"Force logout triggered\");\n            setUser(null);\n            (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.clearSession)();\n            if (authCheckInterval) {\n                clearInterval(authCheckInterval);\n                setAuthCheckInterval(null);\n            }\n            if (true) {\n                window.location.href = '/login';\n            }\n        }\n    }[\"AuthProvider.useCallback[forceLogout]\"], [\n        authCheckInterval\n    ]);\n    // Fonction pour vérifier le statut d'authentification\n    const checkAuthStatus = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[checkAuthStatus]\": async ()=>{\n            try {\n                // Vérifier si le token existe\n                const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                if (!token) {\n                    forceLogout();\n                    return;\n                }\n                // Vérifier si le token est expiré\n                if ((0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                    console.warn(\"Token expired, logging out\");\n                    forceLogout();\n                    return;\n                }\n                // Vérifier avec le serveur\n                const currentUser = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                if (!currentUser) {\n                    forceLogout();\n                    return;\n                }\n                setUser(currentUser);\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n                forceLogout();\n            }\n        }\n    }[\"AuthProvider.useCallback[checkAuthStatus]\"], [\n        forceLogout\n    ]);\n    // vérifier si un utilisateur est déja connecté\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkUserLoggedIn = {\n                \"AuthProvider.useEffect.checkUserLoggedIn\": async ()=>{\n                    try {\n                        // Vérifier d'abord si le token existe et n'est pas expiré\n                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"idToken\");\n                        if (!token || (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)()) {\n                            setUser(null);\n                            setLoading(false);\n                            return;\n                        }\n                        const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                        if (user) {\n                            setUser(user);\n                            // Démarrer la vérification périodique\n                            const interval = setInterval(checkAuthStatus, 60000); // Vérifier toutes les minutes\n                            setAuthCheckInterval(interval);\n                        } else {\n                            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                            setUser(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Error verifying token:\", error);\n                        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\");\n                        setUser(null);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkUserLoggedIn\"];\n            checkUserLoggedIn();\n            // Cleanup interval on unmount\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    if (authCheckInterval) {\n                        clearInterval(authCheckInterval);\n                    }\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        checkAuthStatus,\n        authCheckInterval\n    ]);\n    const login = async (email, password, rememberMe, redirectUrl)=>{\n        try {\n            const response = await fetch(\"\".concat(BASE_API_URL, \"/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: email,\n                    password: password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"Login error:\", data.message || \"Unknown error\");\n                throw new Error(data.message || \"Login failed\");\n            }\n            const { idToken } = data;\n            if (!idToken) {\n                throw new Error(\"No idToken received\");\n            }\n            // Stocker le token dans les cookies\n            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].set(\"idToken\", idToken, {\n                expires: rememberMe ? 30 : 7\n            }); // Expire dans 7 jours\n            const user = await (0,_UserServices__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)(); // Vérifier si l'utilisateur est connecté à nouveau après la connexion réussie\n            if (user) {\n                setUser(user);\n            }\n            // Si une URL de redirection est fournie, stocke-la\n            if (redirectUrl) {\n                setRedirectAfterLogin(redirectUrl);\n            }\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        setUser(null);\n        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"idToken\"); // Supprimer le token des cookies\n        setRedirectAfterLogin(null); // Réinitialiser l'URL de redirection\n        return Promise.resolve();\n    };\n    const isAuthentiacted = !!user; // Vérifier si l'utilisateur est authentifié\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isAuthenticated: isAuthentiacted,\n            loading,\n            setRedirectAfterLogin,\n            redirectAfterLogin,\n            login,\n            logout,\n            checkAuthStatus,\n            forceLogout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\services\\\\AuthContext.tsx\",\n        lineNumber: 184,\n        columnNumber: 9\n    }, undefined);\n};\n_s(AuthProvider, \"IXehwLkee0KKjuVl2ztGE/oHnEQ=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/UserServices.tsx":
/*!*******************************************!*\
  !*** ./src/app/services/UserServices.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteAllUsers: () => (/* binding */ deleteAllUsers),\n/* harmony export */   deleteMultipleUsers: () => (/* binding */ deleteMultipleUsers),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   forget_password: () => (/* binding */ forget_password),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getMonthlyUserStarts: () => (/* binding */ getMonthlyUserStarts),\n/* harmony export */   getParents: () => (/* binding */ getParents),\n/* harmony export */   getTokenFromCookie: () => (/* binding */ getTokenFromCookie),\n/* harmony export */   getTotalUsers: () => (/* binding */ getTotalUsers),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUserBy_id: () => (/* binding */ getUserBy_id),\n/* harmony export */   getUserCountWithChange: () => (/* binding */ getUserCountWithChange),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   handleUserSearch: () => (/* binding */ handleUserSearch),\n/* harmony export */   registerParent: () => (/* binding */ registerParent),\n/* harmony export */   resend_Code: () => (/* binding */ resend_Code),\n/* harmony export */   resetParentPasswordService: () => (/* binding */ resetParentPasswordService),\n/* harmony export */   reset_password: () => (/* binding */ reset_password),\n/* harmony export */   updateUser: () => (/* binding */ updateUser),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verify_otp: () => (/* binding */ verify_otp)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\");\n/* harmony import */ var _utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/httpInterceptor */ \"(app-pages-browser)/./src/app/utils/httpInterceptor.tsx\");\n\n\n\n\nfunction getTokenFromCookie(name) {\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n    return token;\n}\nasync function getCurrentUser() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        if (!token) {\n            return null;\n        }\n        const decodedUser = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n        const email = decodedUser.email;\n        const response = await (0,_utils_httpInterceptor__WEBPACK_IMPORTED_MODULE_3__.authenticatedGet)(\"/user/get-user-email/\".concat(email));\n        if (!response.ok) {\n            console.error(\"Error fetching user:\", response.statusText);\n            return null;\n        }\n        const user = await response.json();\n        return user;\n    } catch (error) {\n        console.error(\"Error fetching current user:\", error);\n        return null;\n    }\n}\nasync function getParents() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-parents\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching parents:\", response.statusText);\n            throw new Error(\"Failed to fetch parents data\");\n        }\n        const parentsList = await response.json();\n        const parents = parentsList.map((user)=>({\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                student_ids: user.student_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            }));\n        return parents;\n    } catch (error) {\n        console.error(\"Error fetching parents:\", error);\n        throw new Error(\"Failed to fetch parents data\");\n    }\n}\nasync function getUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\"); // Assuming this function gets the token from cookies\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-users\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching users:\", response.statusText);\n            throw new Error(\"Failed to fetch users data\");\n        }\n        const usersList = await response.json();\n        const users = usersList.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error fetching users:\", error);\n        throw new Error(\"Failed to fetch users data\");\n    }\n}\nasync function createUser(userData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/register-user\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to create user data\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                // If parsing the error body fails, use default message\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error creating user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the response data (usually the created user object)\n    } catch (error) {\n        console.error(\"Error creating user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to create user data\");\n    }\n}\nasync function updateUser(user_id, userData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/update-user/\").concat(user_id), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            console.error(\"Error updating user:\", response.statusText);\n            throw new Error(\"Failed to update user data\");\n        }\n        const data = await response.json(); // Parse the response as JSON\n        return data; // Return the updated user data (this could be the user object with updated fields)\n    } catch (error) {\n        console.error(\"Error updating user:\", error);\n        throw new Error(\"Failed to update user data\");\n    }\n}\nasync function getUserById(userId) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-user/\").concat(userId), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function verifyPassword(password, email) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/verify-password\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email: email,\n            password: password\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        return false;\n    }\n    return true;\n}\nasync function deleteUser(user_id) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-user/\").concat(user_id), {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            }\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to delete user\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error deleting user:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const result = await response.json();\n        return result; // Might return a success message or deleted user data\n    } catch (error) {\n        console.error(\"Error deleting user:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to delete user\");\n    }\n}\nasync function getUserBy_id(_id) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/get-user-by-id/\").concat(_id), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error fetching user:\", response.statusText);\n        throw new Error(\"Failed to fetch user data\");\n    }\n    const data = await response.json();\n    const user = {\n        _id: data._id,\n        user_id: data.user_id,\n        name: data.name,\n        email: data.email,\n        phone: data.phone,\n        role: data.role,\n        address: data.address,\n        school_ids: data.school_ids,\n        isVerified: data.isVerified,\n        description: data.description,\n        firebaseUid: data.firebaseUid,\n        createdAt: data.createdAt,\n        updatedAt: data.updatedAt\n    };\n    return user;\n}\nasync function forget_password(email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/forgot-password\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to send reset password email: check your email\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error sending reset password email:\", error);\n        throw new Error(\"Failed to send reset password email\");\n    }\n}\nasync function verify_otp(code, email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/verify-code\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                code: code,\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to verify OTP\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error verifying OTP:\", error);\n        throw new Error(\"Failed to verify OTP\");\n    }\n}\nasync function resend_Code(email) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/resend-code\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to resend code\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resending code:\", error);\n        throw new Error(\"Failed to resend code\");\n    }\n}\nasync function reset_password(newPassword, email, code) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/auth/reset-password\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email: email,\n                code: code,\n                newPassword: newPassword\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to reset password\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error resetting password:\", error);\n        throw new Error(\"Failed to reset password\");\n    }\n}\nasync function handleUserSearch(query) {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/search-users?query=\").concat(encodeURIComponent(query)), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error searching users:\", response.statusText);\n            throw new Error(\"Failed to search users\");\n        }\n        const results = await response.json();\n        const users = results.map((user)=>{\n            return {\n                _id: user._id,\n                user_id: user.user_id,\n                firebaseUid: user.firebaseUid,\n                name: user.name,\n                email: user.email,\n                phone: user.phone,\n                role: user.role,\n                avatar: user.avatar,\n                address: user.address,\n                school_ids: user.school_ids,\n                isVerified: user.isVerified,\n                verificationCode: user.verificationCode,\n                verificationCodeExpires: user.verificationCodeExpires,\n                lastLogin: user.lastLogin,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n            };\n        });\n        return users;\n    } catch (error) {\n        console.error(\"Error searching users:\", error);\n        throw new Error(\"Failed to search users\");\n    }\n}\nasync function registerParent(parentData) {\n    try {\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/register-parent\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n            },\n            body: JSON.stringify(parentData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to register parent\";\n            try {\n                const errorBody = await response.json();\n                errorMessage = (errorBody === null || errorBody === void 0 ? void 0 : errorBody.message) || errorMessage;\n            } catch (parseError) {\n                console.warn(\"Could not parse error response:\", parseError);\n            }\n            console.error(\"Error registering parent:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        return data; // This includes user object and generatedPassword (if new)\n    } catch (error) {\n        console.error(\"Error in registerParent service:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to register parent\");\n    }\n}\nasync function deleteMultipleUsers(userIds) {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-users\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        },\n        body: JSON.stringify({\n            ids: userIds\n        })\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting multiple users:\", response.statusText);\n        throw new Error(\"Failed to delete multiple users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function deleteAllUsers() {\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/delete-all-users\"), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(getTokenFromCookie(\"idToken\"))\n        }\n    });\n    if (!response.ok) {\n        console.error(\"Error deleting all users:\", response.statusText);\n        throw new Error(\"Failed to delete all users\");\n    }\n    const data = await response.json();\n    return data;\n}\nasync function getTotalUsers() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/total-users\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching total users:\", response.statusText);\n            throw new Error(\"Failed to fetch total users\");\n        }\n        const data = await response.json();\n        return data.totalUsers;\n    } catch (error) {\n        console.error(\"Error fetching total users:\", error);\n        throw error;\n    }\n}\nasync function getUserCountWithChange() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/users-count-change\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Error fetching user count change:\", response.statusText);\n            throw new Error(\"Failed to fetch user count change\");\n        }\n        const data = await response.json();\n        return {\n            totalUsersThisMonth: data.totalUsersThisMonth,\n            percentageChange: data.percentageChange\n        };\n    } catch (error) {\n        console.error(\"Error fetching user count change:\", error);\n        throw error;\n    }\n}\nasync function getMonthlyUserStarts() {\n    try {\n        const token = getTokenFromCookie(\"idToken\");\n        const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/monthly-user-starts\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            console.error(\"Failed to fetch monthly user stats:\", response.statusText);\n            throw new Error(\"Failed to fetch monthly user stats\");\n        }\n        const data = await response.json();\n        // data has type { [year: string]: MonthlyUserStat[] }\n        return data;\n    } catch (error) {\n        console.error(\"Error fetching monthly user stats:\", error);\n        throw error;\n    }\n}\nasync function resetParentPasswordService(param) {\n    let { email, phone } = param;\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"idToken\");\n    if (!token) {\n        throw new Error(\"You must be logged in to perform this action.\");\n    }\n    if (!email && !phone) {\n        throw new Error(\"Email or phone number is required to reset password.\");\n    }\n    const payload = {};\n    if (email) payload.email = email;\n    if (phone) payload.phone = phone;\n    const response = await fetch(\"\".concat(_AuthContext__WEBPACK_IMPORTED_MODULE_2__.BASE_API_URL, \"/user/reset-parent-password\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \".concat(token)\n        },\n        body: JSON.stringify(payload)\n    });\n    if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || \"Failed to reset password\");\n    }\n    const result = await response.json();\n    return result;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvc2VydmljZXMvVXNlclNlcnZpY2VzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNnQztBQUNPO0FBQ007QUFFeUU7QUFFL0csU0FBU0ksbUJBQW1CQyxJQUFZO0lBQzNDLE1BQU1DLFFBQVFOLGlEQUFPQSxDQUFDTyxHQUFHLENBQUNGO0lBQzFCLE9BQU9DO0FBQ1g7QUFFTyxlQUFlRTtJQUNsQixJQUFJO1FBQ0EsTUFBTUYsUUFBUUYsbUJBQW1CO1FBQ2pDLElBQUksQ0FBQ0UsT0FBTztZQUNSLE9BQU87UUFDWDtRQUVBLE1BQU1HLGNBQW1CUixxREFBU0EsQ0FBQ0s7UUFDbkMsTUFBTUksUUFBUUQsWUFBWUMsS0FBSztRQUUvQixNQUFNQyxXQUFXLE1BQU1SLHdFQUFnQkEsQ0FBQyx3QkFBOEIsT0FBTk87UUFFaEUsSUFBSSxDQUFDQyxTQUFTQyxFQUFFLEVBQUU7WUFDZEMsUUFBUUMsS0FBSyxDQUFDLHdCQUF3QkgsU0FBU0ksVUFBVTtZQUN6RCxPQUFPO1FBQ1g7UUFFQSxNQUFNQyxPQUFPLE1BQU1MLFNBQVNNLElBQUk7UUFDaEMsT0FBT0Q7SUFDWCxFQUFFLE9BQU9GLE9BQU87UUFDWkQsUUFBUUMsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDOUMsT0FBTztJQUNYO0FBQ0o7QUFFTyxlQUFlSTtJQUNsQixJQUFJO1FBQ0EsTUFBTVosUUFBUUYsbUJBQW1CO1FBQ2pDLE1BQU1PLFdBQVcsTUFBTVEsTUFBTSxHQUFnQixPQUFiakIsc0RBQVlBLEVBQUMsc0JBQW9CO1lBQzdEa0IsUUFBUTtZQUNSQyxTQUFTO2dCQUNMLGdCQUFnQjtnQkFDaEJDLGVBQWUsVUFBZ0IsT0FBTmhCO1lBQzdCO1FBQ0o7UUFFQSxJQUFJLENBQUNLLFNBQVNDLEVBQUUsRUFBRTtZQUNkQyxRQUFRQyxLQUFLLENBQUMsMkJBQTJCSCxTQUFTSSxVQUFVO1lBQzVELE1BQU0sSUFBSVEsTUFBTTtRQUNwQjtRQUVBLE1BQU1DLGNBQWMsTUFBTWIsU0FBU00sSUFBSTtRQUV2QyxNQUFNUSxVQUFVRCxZQUFZRSxHQUFHLENBQUMsQ0FBQ1YsT0FBZTtnQkFDNUNXLEtBQUtYLEtBQUtXLEdBQUc7Z0JBQ2JDLFNBQVNaLEtBQUtZLE9BQU87Z0JBQ3JCQyxhQUFhYixLQUFLYSxXQUFXO2dCQUM3QnhCLE1BQU1XLEtBQUtYLElBQUk7Z0JBQ2ZLLE9BQU9NLEtBQUtOLEtBQUs7Z0JBQ2pCb0IsT0FBT2QsS0FBS2MsS0FBSztnQkFDakJDLE1BQU1mLEtBQUtlLElBQUk7Z0JBQ2ZDLFFBQVFoQixLQUFLZ0IsTUFBTTtnQkFDbkJDLFNBQVNqQixLQUFLaUIsT0FBTztnQkFDckJDLFlBQVlsQixLQUFLa0IsVUFBVTtnQkFDM0JDLGFBQWFuQixLQUFLbUIsV0FBVztnQkFDN0JDLFlBQVlwQixLQUFLb0IsVUFBVTtnQkFDM0JDLGtCQUFrQnJCLEtBQUtxQixnQkFBZ0I7Z0JBQ3ZDQyx5QkFBeUJ0QixLQUFLc0IsdUJBQXVCO2dCQUNyREMsV0FBV3ZCLEtBQUt1QixTQUFTO2dCQUN6QkMsV0FBV3hCLEtBQUt3QixTQUFTO2dCQUN6QkMsV0FBV3pCLEtBQUt5QixTQUFTO1lBQzdCO1FBRUEsT0FBT2hCO0lBRVgsRUFBRSxPQUFPWCxPQUFPO1FBQ1pELFFBQVFDLEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE1BQU0sSUFBSVMsTUFBTTtJQUNwQjtBQUNKO0FBRU8sZUFBZW1CO0lBQ2xCLElBQUk7UUFDQSxNQUFNcEMsUUFBUUYsbUJBQW1CLFlBQVkscURBQXFEO1FBQ2xHLE1BQU1PLFdBQVcsTUFBTVEsTUFBTSxHQUFnQixPQUFiakIsc0RBQVlBLEVBQUMsb0JBQWtCO1lBQzNEa0IsUUFBUTtZQUNSQyxTQUFTO2dCQUNMLGdCQUFnQjtnQkFDaEJDLGVBQWUsVUFBZ0IsT0FBTmhCO1lBQzdCO1FBQ0o7UUFFQSxJQUFJLENBQUNLLFNBQVNDLEVBQUUsRUFBRTtZQUNkQyxRQUFRQyxLQUFLLENBQUMseUJBQXlCSCxTQUFTSSxVQUFVO1lBQzFELE1BQU0sSUFBSVEsTUFBTTtRQUNwQjtRQUVBLE1BQU1vQixZQUFZLE1BQU1oQyxTQUFTTSxJQUFJO1FBQ3JDLE1BQU0yQixRQUFRRCxVQUFVakIsR0FBRyxDQUFDLENBQUNWO1lBQ3pCLE9BQU87Z0JBQ0hXLEtBQUtYLEtBQUtXLEdBQUc7Z0JBQ2JDLFNBQVNaLEtBQUtZLE9BQU87Z0JBQ3JCQyxhQUFhYixLQUFLYSxXQUFXO2dCQUM3QnhCLE1BQU1XLEtBQUtYLElBQUk7Z0JBQ2ZLLE9BQU9NLEtBQUtOLEtBQUs7Z0JBQ2pCb0IsT0FBT2QsS0FBS2MsS0FBSztnQkFDakJDLE1BQU1mLEtBQUtlLElBQUk7Z0JBQ2ZDLFFBQVFoQixLQUFLZ0IsTUFBTTtnQkFDbkJDLFNBQVNqQixLQUFLaUIsT0FBTztnQkFDckJDLFlBQVlsQixLQUFLa0IsVUFBVTtnQkFDM0JFLFlBQVlwQixLQUFLb0IsVUFBVTtnQkFDM0JDLGtCQUFrQnJCLEtBQUtxQixnQkFBZ0I7Z0JBQ3ZDQyx5QkFBeUJ0QixLQUFLc0IsdUJBQXVCO2dCQUNyREMsV0FBV3ZCLEtBQUt1QixTQUFTO2dCQUN6QkMsV0FBV3hCLEtBQUt3QixTQUFTO2dCQUN6QkMsV0FBV3pCLEtBQUt5QixTQUFTO1lBQzdCO1FBQ0o7UUFFQSxPQUFPRztJQUVYLEVBQUUsT0FBTzlCLE9BQU87UUFDWkQsUUFBUUMsS0FBSyxDQUFDLHlCQUF5QkE7UUFDdkMsTUFBTSxJQUFJUyxNQUFNO0lBQ3BCO0FBQ0o7QUFFTyxlQUFlc0IsV0FBV0MsUUFBMEI7SUFDdkQsSUFBSTtRQUNBLE1BQU1uQyxXQUFXLE1BQU1RLE1BQU0sR0FBZ0IsT0FBYmpCLHNEQUFZQSxFQUFDLHdCQUFzQjtZQUMvRGtCLFFBQVE7WUFDUkMsU0FBUztnQkFDTCxnQkFBZ0I7Z0JBQ2hCQyxlQUFlLFVBQXdDLE9BQTlCbEIsbUJBQW1CO1lBQ2hEO1lBQ0EyQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNIO1FBQ3pCO1FBRUEsSUFBSSxDQUFDbkMsU0FBU0MsRUFBRSxFQUFFO1lBQ2QsSUFBSXNDLGVBQWU7WUFFbkIsSUFBSTtnQkFDQSxNQUFNQyxZQUFZLE1BQU14QyxTQUFTTSxJQUFJO2dCQUNyQ2lDLGVBQWVDLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV0MsT0FBTyxLQUFJRjtZQUN6QyxFQUFFLE9BQU9HLFlBQVk7Z0JBQ2pCLHVEQUF1RDtnQkFDdkR4QyxRQUFReUMsSUFBSSxDQUFDLG1DQUFtQ0Q7WUFDcEQ7WUFFQXhDLFFBQVFDLEtBQUssQ0FBQyx3QkFBd0JvQztZQUN0QyxNQUFNLElBQUkzQixNQUFNMkI7UUFDcEI7UUFFQSxNQUFNSyxPQUFPLE1BQU01QyxTQUFTTSxJQUFJLElBQUksNkJBQTZCO1FBQ2pFLE9BQU9zQyxNQUFNLDZEQUE2RDtJQUU5RSxFQUFFLE9BQU96QyxPQUFPO1FBQ1pELFFBQVFDLEtBQUssQ0FBQyx3QkFBd0JBO1FBQ3RDLE1BQU0sSUFBSVMsTUFBTVQsaUJBQWlCUyxRQUFRVCxNQUFNc0MsT0FBTyxHQUFHO0lBQzdEO0FBQ0o7QUFFTyxlQUFlSSxXQUFXNUIsT0FBZSxFQUFFa0IsUUFBMEI7SUFDeEUsSUFBSTtRQUNBLE1BQU1uQyxXQUFXLE1BQU1RLE1BQU0sR0FBb0NTLE9BQWpDMUIsc0RBQVlBLEVBQUMsc0JBQTRCLE9BQVIwQixVQUFXO1lBQ3hFUixRQUFRO1lBQ1JDLFNBQVM7Z0JBQ0wsZ0JBQWdCO2dCQUNoQkMsZUFBZSxVQUF3QyxPQUE5QmxCLG1CQUFtQjtZQUNoRDtZQUNBMkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDSDtRQUN6QjtRQUVBLElBQUksQ0FBQ25DLFNBQVNDLEVBQUUsRUFBRTtZQUNkQyxRQUFRQyxLQUFLLENBQUMsd0JBQXdCSCxTQUFTSSxVQUFVO1lBQ3pELE1BQU0sSUFBSVEsTUFBTTtRQUNwQjtRQUVBLE1BQU1nQyxPQUFPLE1BQU01QyxTQUFTTSxJQUFJLElBQUksNkJBQTZCO1FBQ2pFLE9BQU9zQyxNQUFNLG1GQUFtRjtJQUVwRyxFQUFFLE9BQU96QyxPQUFPO1FBQ1pELFFBQVFDLEtBQUssQ0FBQyx3QkFBd0JBO1FBQ3RDLE1BQU0sSUFBSVMsTUFBTTtJQUNwQjtBQUNKO0FBRU8sZUFBZWtDLFlBQVlDLE1BQWM7SUFDNUMsTUFBTS9DLFdBQVcsTUFBTVEsTUFBTSxHQUFpQ3VDLE9BQTlCeEQsc0RBQVlBLEVBQUMsbUJBQXdCLE9BQVB3RCxTQUFVO1FBQ3BFdEMsUUFBUTtRQUNSQyxTQUFTO1lBQ0wsZ0JBQWdCO1lBQ2hCQyxlQUFlLFVBQXdDLE9BQTlCbEIsbUJBQW1CO1FBQ2hEO0lBQ0o7SUFFQSxJQUFJLENBQUNPLFNBQVNDLEVBQUUsRUFBRTtRQUNkQyxRQUFRQyxLQUFLLENBQUMsd0JBQXdCSCxTQUFTSSxVQUFVO1FBQ3pELE1BQU0sSUFBSVEsTUFBTTtJQUNwQjtJQUVBLE1BQU1nQyxPQUFPLE1BQU01QyxTQUFTTSxJQUFJO0lBRWhDLE1BQU1ELE9BQU87UUFDVFcsS0FBSzRCLEtBQUs1QixHQUFHO1FBQ2JDLFNBQVMyQixLQUFLM0IsT0FBTztRQUNyQnZCLE1BQU1rRCxLQUFLbEQsSUFBSTtRQUNmSyxPQUFPNkMsS0FBSzdDLEtBQUs7UUFDakJvQixPQUFPeUIsS0FBS3pCLEtBQUs7UUFDakJDLE1BQU13QixLQUFLeEIsSUFBSTtRQUNmRSxTQUFTc0IsS0FBS3RCLE9BQU87UUFDckJDLFlBQVlxQixLQUFLckIsVUFBVTtRQUMzQkUsWUFBWW1CLEtBQUtuQixVQUFVO1FBQzNCdUIsYUFBYUosS0FBS0ksV0FBVztRQUM3QjlCLGFBQWEwQixLQUFLMUIsV0FBVztRQUM3QlcsV0FBV2UsS0FBS2YsU0FBUztRQUN6QkMsV0FBV2MsS0FBS2QsU0FBUztJQUM3QjtJQUVBLE9BQU96QjtBQUNYO0FBRU8sZUFBZTRDLGVBQWVDLFFBQWdCLEVBQUVuRCxLQUFhO0lBQ2hFLE1BQU1DLFdBQVcsTUFBTVEsTUFBTSxHQUFnQixPQUFiakIsc0RBQVlBLEVBQUMsMEJBQXdCO1FBQ2pFa0IsUUFBUTtRQUNSQyxTQUFTO1lBQ0wsZ0JBQWdCO1FBQ3BCO1FBQ0EwQixNQUFNQyxLQUFLQyxTQUFTLENBQUM7WUFDakJ2QyxPQUFPQTtZQUNQbUQsVUFBVUE7UUFDZDtJQUNKO0lBRUEsSUFBSSxDQUFDbEQsU0FBU0MsRUFBRSxFQUFFO1FBQ2RDLFFBQVFDLEtBQUssQ0FBQyx3QkFBd0JILFNBQVNJLFVBQVU7UUFDekQsT0FBTztJQUNYO0lBRUEsT0FBTztBQUNYO0FBRU8sZUFBZStDLFdBQVdsQyxPQUFlO0lBQzVDLElBQUk7UUFDQSxNQUFNakIsV0FBVyxNQUFNUSxNQUFNLEdBQW9DUyxPQUFqQzFCLHNEQUFZQSxFQUFDLHNCQUE0QixPQUFSMEIsVUFBVztZQUN4RVIsUUFBUTtZQUNSQyxTQUFTO2dCQUNMLGdCQUFnQjtnQkFDaEJDLGVBQWUsVUFBd0MsT0FBOUJsQixtQkFBbUI7WUFDaEQ7UUFDSjtRQUVBLElBQUksQ0FBQ08sU0FBU0MsRUFBRSxFQUFFO1lBQ2QsSUFBSXNDLGVBQWU7WUFFbkIsSUFBSTtnQkFDQSxNQUFNQyxZQUFZLE1BQU14QyxTQUFTTSxJQUFJO2dCQUNyQ2lDLGVBQWVDLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV0MsT0FBTyxLQUFJRjtZQUN6QyxFQUFFLE9BQU9HLFlBQVk7Z0JBQ2pCeEMsUUFBUXlDLElBQUksQ0FBQyxtQ0FBbUNEO1lBQ3BEO1lBRUF4QyxRQUFRQyxLQUFLLENBQUMsd0JBQXdCb0M7WUFDdEMsTUFBTSxJQUFJM0IsTUFBTTJCO1FBQ3BCO1FBRUEsTUFBTWEsU0FBUyxNQUFNcEQsU0FBU00sSUFBSTtRQUNsQyxPQUFPOEMsUUFBUSxzREFBc0Q7SUFFekUsRUFBRSxPQUFPakQsT0FBTztRQUNaRCxRQUFRQyxLQUFLLENBQUMsd0JBQXdCQTtRQUN0QyxNQUFNLElBQUlTLE1BQU1ULGlCQUFpQlMsUUFBUVQsTUFBTXNDLE9BQU8sR0FBRztJQUM3RDtBQUNKO0FBRU8sZUFBZVksYUFBYXJDLEdBQVc7SUFDMUMsTUFBTWhCLFdBQVcsTUFBTVEsTUFBTSxHQUF1Q1EsT0FBcEN6QixzREFBWUEsRUFBQyx5QkFBMkIsT0FBSnlCLE1BQU87UUFDdkVQLFFBQVE7UUFDUkMsU0FBUztZQUNMLGdCQUFnQjtZQUNoQkMsZUFBZSxVQUF3QyxPQUE5QmxCLG1CQUFtQjtRQUNoRDtJQUNKO0lBRUEsSUFBSSxDQUFDTyxTQUFTQyxFQUFFLEVBQUU7UUFDZEMsUUFBUUMsS0FBSyxDQUFDLHdCQUF3QkgsU0FBU0ksVUFBVTtRQUN6RCxNQUFNLElBQUlRLE1BQU07SUFDcEI7SUFDQSxNQUFNZ0MsT0FBTyxNQUFNNUMsU0FBU00sSUFBSTtJQUVoQyxNQUFNRCxPQUFPO1FBQ1RXLEtBQUs0QixLQUFLNUIsR0FBRztRQUNiQyxTQUFTMkIsS0FBSzNCLE9BQU87UUFDckJ2QixNQUFNa0QsS0FBS2xELElBQUk7UUFDZkssT0FBTzZDLEtBQUs3QyxLQUFLO1FBQ2pCb0IsT0FBT3lCLEtBQUt6QixLQUFLO1FBQ2pCQyxNQUFNd0IsS0FBS3hCLElBQUk7UUFDZkUsU0FBU3NCLEtBQUt0QixPQUFPO1FBQ3JCQyxZQUFZcUIsS0FBS3JCLFVBQVU7UUFDM0JFLFlBQVltQixLQUFLbkIsVUFBVTtRQUMzQnVCLGFBQWFKLEtBQUtJLFdBQVc7UUFDN0I5QixhQUFhMEIsS0FBSzFCLFdBQVc7UUFDN0JXLFdBQVdlLEtBQUtmLFNBQVM7UUFDekJDLFdBQVdjLEtBQUtkLFNBQVM7SUFDN0I7SUFFQSxPQUFPekI7QUFDWDtBQUVPLGVBQWVpRCxnQkFBZ0J2RCxLQUFhO0lBQy9DLElBQUk7UUFDQSxNQUFNQyxXQUFXLE1BQU1RLE1BQU0sR0FBZ0IsT0FBYmpCLHNEQUFZQSxFQUFDLDBCQUF3QjtZQUNqRWtCLFFBQVE7WUFDUkMsU0FBUztnQkFDTCxnQkFBZ0I7WUFDcEI7WUFDQTBCLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnQkFDakJ2QyxPQUFPQTtZQUNYO1FBQ0o7UUFFQSxJQUFJLENBQUNDLFNBQVNDLEVBQUUsRUFBRTtZQUNkLElBQUlzQyxlQUFlO1lBQ25CLElBQUk7Z0JBQ0EsTUFBTUMsWUFBWSxNQUFNeEMsU0FBU00sSUFBSTtnQkFDckNpQyxlQUFlQyxDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdDLE9BQU8sS0FBSUY7WUFDekMsRUFBRSxPQUFPRyxZQUFZO2dCQUNqQnhDLFFBQVF5QyxJQUFJLENBQUMsbUNBQW1DRDtZQUNwRDtZQUNBLE1BQU0sSUFBSTlCLE1BQU0yQjtRQUNwQjtRQUVBLE1BQU1LLE9BQU8sTUFBTTVDLFNBQVNNLElBQUk7UUFDaEMsT0FBT3NDO0lBRVgsRUFBRSxPQUFPekMsT0FBTztRQUNaRCxRQUFRQyxLQUFLLENBQUMsdUNBQXVDQTtRQUNyRCxNQUFNLElBQUlTLE1BQU07SUFDcEI7QUFDSjtBQUVPLGVBQWUyQyxXQUFXQyxJQUFZLEVBQUV6RCxLQUFhO0lBQ3hELElBQUk7UUFDQSxNQUFNQyxXQUFXLE1BQU1RLE1BQU0sR0FBZ0IsT0FBYmpCLHNEQUFZQSxFQUFDLHNCQUFvQjtZQUM3RGtCLFFBQVE7WUFDUkMsU0FBUztnQkFDTCxnQkFBZ0I7WUFDcEI7WUFDQTBCLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnQkFDakJrQixNQUFNQTtnQkFDTnpELE9BQU9BO1lBQ1g7UUFDSjtRQUVBLElBQUksQ0FBQ0MsU0FBU0MsRUFBRSxFQUFFO1lBQ2QsSUFBSXNDLGVBQWU7WUFDbkIsSUFBSTtnQkFDQSxNQUFNQyxZQUFZLE1BQU14QyxTQUFTTSxJQUFJO2dCQUNyQ2lDLGVBQWVDLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV0MsT0FBTyxLQUFJRjtZQUN6QyxFQUFFLE9BQU9HLFlBQVk7Z0JBQ2pCeEMsUUFBUXlDLElBQUksQ0FBQyxtQ0FBbUNEO1lBQ3BEO1lBQ0EsTUFBTSxJQUFJOUIsTUFBTTJCO1FBQ3BCO1FBRUEsTUFBTUssT0FBTyxNQUFNNUMsU0FBU00sSUFBSTtRQUNoQyxPQUFPc0M7SUFFWCxFQUFFLE9BQU96QyxPQUFPO1FBQ1pELFFBQVFDLEtBQUssQ0FBQyx3QkFBd0JBO1FBQ3RDLE1BQU0sSUFBSVMsTUFBTTtJQUNwQjtBQUNKO0FBR08sZUFBZTZDLFlBQVkxRCxLQUFhO0lBQzNDLElBQUk7UUFDQSxNQUFNQyxXQUFXLE1BQU1RLE1BQU0sR0FBZ0IsT0FBYmpCLHNEQUFZQSxFQUFDLHNCQUFvQjtZQUM3RGtCLFFBQVE7WUFDUkMsU0FBUztnQkFDTCxnQkFBZ0I7WUFDcEI7WUFDQTBCLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnQkFDakJ2QyxPQUFPQTtZQUNYO1FBQ0o7UUFFQSxJQUFJLENBQUNDLFNBQVNDLEVBQUUsRUFBRTtZQUNkLElBQUlzQyxlQUFlO1lBQ25CLElBQUk7Z0JBQ0EsTUFBTUMsWUFBWSxNQUFNeEMsU0FBU00sSUFBSTtnQkFDckNpQyxlQUFlQyxDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdDLE9BQU8sS0FBSUY7WUFDekMsRUFBRSxPQUFPRyxZQUFZO2dCQUNqQnhDLFFBQVF5QyxJQUFJLENBQUMsbUNBQW1DRDtZQUNwRDtZQUNBLE1BQU0sSUFBSTlCLE1BQU0yQjtRQUNwQjtRQUVBLE1BQU1LLE9BQU8sTUFBTTVDLFNBQVNNLElBQUk7UUFDaEMsT0FBT3NDO0lBRVgsRUFBRSxPQUFPekMsT0FBTztRQUNaRCxRQUFRQyxLQUFLLENBQUMseUJBQXlCQTtRQUN2QyxNQUFNLElBQUlTLE1BQU07SUFDcEI7QUFDSjtBQUVPLGVBQWU4QyxlQUFlQyxXQUFtQixFQUFFNUQsS0FBYSxFQUFFeUQsSUFBWTtJQUNqRixJQUFJO1FBQ0EsTUFBTXhELFdBQVcsTUFBTVEsTUFBTSxHQUFnQixPQUFiakIsc0RBQVlBLEVBQUMseUJBQXVCO1lBQ2hFa0IsUUFBUTtZQUNSQyxTQUFTO2dCQUNMLGdCQUFnQjtZQUNwQjtZQUNBMEIsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUNqQnZDLE9BQU9BO2dCQUNQeUQsTUFBTUE7Z0JBQ05HLGFBQWFBO1lBQ2pCO1FBQ0o7UUFFQSxJQUFJLENBQUMzRCxTQUFTQyxFQUFFLEVBQUU7WUFDZCxJQUFJc0MsZUFBZTtZQUNuQixJQUFJO2dCQUNBLE1BQU1DLFlBQVksTUFBTXhDLFNBQVNNLElBQUk7Z0JBQ3JDaUMsZUFBZUMsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXQyxPQUFPLEtBQUlGO1lBQ3pDLEVBQUUsT0FBT0csWUFBWTtnQkFDakJ4QyxRQUFReUMsSUFBSSxDQUFDLG1DQUFtQ0Q7WUFDcEQ7WUFDQSxNQUFNLElBQUk5QixNQUFNMkI7UUFDcEI7UUFFQSxNQUFNSyxPQUFPLE1BQU01QyxTQUFTTSxJQUFJO1FBQ2hDLE9BQU9zQztJQUVYLEVBQUUsT0FBT3pDLE9BQU87UUFDWkQsUUFBUUMsS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsTUFBTSxJQUFJUyxNQUFNO0lBQ3BCO0FBQ0o7QUFFTyxlQUFlZ0QsaUJBQWlCQyxLQUFhO0lBQ2hELElBQUk7UUFDQSxNQUFNbEUsUUFBUUYsbUJBQW1CO1FBQ2pDLE1BQU1PLFdBQVcsTUFBTVEsTUFBTSxHQUEyQ3NELE9BQXhDdkUsc0RBQVlBLEVBQUMsNkJBQXFELE9BQTFCdUUsbUJBQW1CRCxTQUFVO1lBQ2pHcEQsUUFBUTtZQUNSQyxTQUFTO2dCQUNMLGdCQUFnQjtnQkFDaEJDLGVBQWUsVUFBZ0IsT0FBTmhCO1lBQzdCO1FBQ0o7UUFFQSxJQUFJLENBQUNLLFNBQVNDLEVBQUUsRUFBRTtZQUNkQyxRQUFRQyxLQUFLLENBQUMsMEJBQTBCSCxTQUFTSSxVQUFVO1lBQzNELE1BQU0sSUFBSVEsTUFBTTtRQUNwQjtRQUVBLE1BQU1tRCxVQUFVLE1BQU0vRCxTQUFTTSxJQUFJO1FBRW5DLE1BQU0yQixRQUFROEIsUUFBUWhELEdBQUcsQ0FBQyxDQUFDVjtZQUN2QixPQUFPO2dCQUNIVyxLQUFLWCxLQUFLVyxHQUFHO2dCQUNiQyxTQUFTWixLQUFLWSxPQUFPO2dCQUNyQkMsYUFBYWIsS0FBS2EsV0FBVztnQkFDN0J4QixNQUFNVyxLQUFLWCxJQUFJO2dCQUNmSyxPQUFPTSxLQUFLTixLQUFLO2dCQUNqQm9CLE9BQU9kLEtBQUtjLEtBQUs7Z0JBQ2pCQyxNQUFNZixLQUFLZSxJQUFJO2dCQUNmQyxRQUFRaEIsS0FBS2dCLE1BQU07Z0JBQ25CQyxTQUFTakIsS0FBS2lCLE9BQU87Z0JBQ3JCQyxZQUFZbEIsS0FBS2tCLFVBQVU7Z0JBQzNCRSxZQUFZcEIsS0FBS29CLFVBQVU7Z0JBQzNCQyxrQkFBa0JyQixLQUFLcUIsZ0JBQWdCO2dCQUN2Q0MseUJBQXlCdEIsS0FBS3NCLHVCQUF1QjtnQkFDckRDLFdBQVd2QixLQUFLdUIsU0FBUztnQkFDekJDLFdBQVd4QixLQUFLd0IsU0FBUztnQkFDekJDLFdBQVd6QixLQUFLeUIsU0FBUztZQUM3QjtRQUNKO1FBRUEsT0FBT0c7SUFDWCxFQUFFLE9BQU85QixPQUFPO1FBQ1pELFFBQVFDLEtBQUssQ0FBQywwQkFBMEJBO1FBQ3hDLE1BQU0sSUFBSVMsTUFBTTtJQUNwQjtBQUNKO0FBRU8sZUFBZW9ELGVBQWVDLFVBQWU7SUFDaEQsSUFBSTtRQUNBLE1BQU1qRSxXQUFXLE1BQU1RLE1BQU0sR0FBZ0IsT0FBYmpCLHNEQUFZQSxFQUFDLDBCQUF3QjtZQUNqRWtCLFFBQVE7WUFDUkMsU0FBUztnQkFDTCxnQkFBZ0I7Z0JBQ2hCQyxlQUFlLFVBQXdDLE9BQTlCbEIsbUJBQW1CO1lBQ2hEO1lBQ0EyQyxNQUFNQyxLQUFLQyxTQUFTLENBQUMyQjtRQUN6QjtRQUVBLElBQUksQ0FBQ2pFLFNBQVNDLEVBQUUsRUFBRTtZQUNkLElBQUlzQyxlQUFlO1lBRW5CLElBQUk7Z0JBQ0EsTUFBTUMsWUFBWSxNQUFNeEMsU0FBU00sSUFBSTtnQkFDckNpQyxlQUFlQyxDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdDLE9BQU8sS0FBSUY7WUFDekMsRUFBRSxPQUFPRyxZQUFZO2dCQUNqQnhDLFFBQVF5QyxJQUFJLENBQUMsbUNBQW1DRDtZQUNwRDtZQUVBeEMsUUFBUUMsS0FBSyxDQUFDLDZCQUE2Qm9DO1lBQzNDLE1BQU0sSUFBSTNCLE1BQU0yQjtRQUNwQjtRQUVBLE1BQU1LLE9BQU8sTUFBTTVDLFNBQVNNLElBQUk7UUFDaEMsT0FBT3NDLE1BQU0sMkRBQTJEO0lBRTVFLEVBQUUsT0FBT3pDLE9BQU87UUFDWkQsUUFBUUMsS0FBSyxDQUFDLG9DQUFvQ0E7UUFDbEQsTUFBTSxJQUFJUyxNQUFNVCxpQkFBaUJTLFFBQVFULE1BQU1zQyxPQUFPLEdBQUc7SUFDN0Q7QUFDSjtBQUVPLGVBQWV5QixvQkFBb0JDLE9BQWlCO0lBQ3ZELE1BQU1uRSxXQUFXLE1BQU1RLE1BQU0sR0FBZ0IsT0FBYmpCLHNEQUFZQSxFQUFDLHVCQUFxQjtRQUM5RGtCLFFBQVE7UUFDUkMsU0FBUztZQUNMLGdCQUFnQjtZQUNoQkMsZUFBZSxVQUF3QyxPQUE5QmxCLG1CQUFtQjtRQUNoRDtRQUNBMkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO1lBQUU4QixLQUFLRDtRQUFRO0lBQ3hDO0lBQ0EsSUFBSSxDQUFDbkUsU0FBU0MsRUFBRSxFQUFFO1FBQ2RDLFFBQVFDLEtBQUssQ0FBQyxrQ0FBa0NILFNBQVNJLFVBQVU7UUFDbkUsTUFBTSxJQUFJUSxNQUFNO0lBQ3BCO0lBQ0EsTUFBTWdDLE9BQU8sTUFBTTVDLFNBQVNNLElBQUk7SUFDaEMsT0FBT3NDO0FBQ1g7QUFFTyxlQUFleUI7SUFDbEIsTUFBTXJFLFdBQVcsTUFBTVEsTUFBTSxHQUFnQixPQUFiakIsc0RBQVlBLEVBQUMsMkJBQXlCO1FBQ2xFa0IsUUFBUTtRQUNSQyxTQUFTO1lBQ0wsZ0JBQWdCO1lBQ2hCQyxlQUFlLFVBQXdDLE9BQTlCbEIsbUJBQW1CO1FBQ2hEO0lBQ0o7SUFDQSxJQUFJLENBQUNPLFNBQVNDLEVBQUUsRUFBRTtRQUNkQyxRQUFRQyxLQUFLLENBQUMsNkJBQTZCSCxTQUFTSSxVQUFVO1FBQzlELE1BQU0sSUFBSVEsTUFBTTtJQUNwQjtJQUNBLE1BQU1nQyxPQUFPLE1BQU01QyxTQUFTTSxJQUFJO0lBQ2hDLE9BQU9zQztBQUNYO0FBR08sZUFBZTBCO0lBQ2xCLElBQUk7UUFDQSxNQUFNM0UsUUFBUUYsbUJBQW1CO1FBQ2pDLE1BQU1PLFdBQVcsTUFBTVEsTUFBTSxHQUFnQixPQUFiakIsc0RBQVlBLEVBQUMsc0JBQW9CO1lBQzdEa0IsUUFBUTtZQUNSQyxTQUFTO2dCQUNMLGdCQUFnQjtnQkFDaEJDLGVBQWUsVUFBZ0IsT0FBTmhCO1lBQzdCO1FBQ0o7UUFFQSxJQUFJLENBQUNLLFNBQVNDLEVBQUUsRUFBRTtZQUNkQyxRQUFRQyxLQUFLLENBQUMsK0JBQStCSCxTQUFTSSxVQUFVO1lBQ2hFLE1BQU0sSUFBSVEsTUFBTTtRQUNwQjtRQUVBLE1BQU1nQyxPQUFPLE1BQU01QyxTQUFTTSxJQUFJO1FBQ2hDLE9BQU9zQyxLQUFLMkIsVUFBVTtJQUMxQixFQUFFLE9BQU9wRSxPQUFPO1FBQ1pELFFBQVFDLEtBQUssQ0FBQywrQkFBK0JBO1FBQzdDLE1BQU1BO0lBQ1Y7QUFDSjtBQUVPLGVBQWVxRTtJQUlsQixJQUFJO1FBQ0EsTUFBTTdFLFFBQVFGLG1CQUFtQjtRQUNqQyxNQUFNTyxXQUFXLE1BQU1RLE1BQU0sR0FBZ0IsT0FBYmpCLHNEQUFZQSxFQUFDLDZCQUEyQjtZQUNwRWtCLFFBQVE7WUFDUkMsU0FBUztnQkFDTCxnQkFBZ0I7Z0JBQ2hCQyxlQUFlLFVBQWdCLE9BQU5oQjtZQUM3QjtRQUNKO1FBRUEsSUFBSSxDQUFDSyxTQUFTQyxFQUFFLEVBQUU7WUFDZEMsUUFBUUMsS0FBSyxDQUFDLHFDQUFxQ0gsU0FBU0ksVUFBVTtZQUN0RSxNQUFNLElBQUlRLE1BQU07UUFDcEI7UUFFQSxNQUFNZ0MsT0FBTyxNQUFNNUMsU0FBU00sSUFBSTtRQUNoQyxPQUFPO1lBQ0htRSxxQkFBcUI3QixLQUFLNkIsbUJBQW1CO1lBQzdDQyxrQkFBa0I5QixLQUFLOEIsZ0JBQWdCO1FBQzNDO0lBQ0osRUFBRSxPQUFPdkUsT0FBTztRQUNaRCxRQUFRQyxLQUFLLENBQUMscUNBQXFDQTtRQUNuRCxNQUFNQTtJQUNWO0FBQ0o7QUFhTyxlQUFld0U7SUFDbEIsSUFBSTtRQUNBLE1BQU1oRixRQUFRRixtQkFBbUI7UUFFakMsTUFBTU8sV0FBVyxNQUFNUSxNQUFNLEdBQWdCLE9BQWJqQixzREFBWUEsRUFBQyw4QkFBNEI7WUFDckVrQixRQUFRO1lBQ1JDLFNBQVM7Z0JBQ0wsZ0JBQWdCO2dCQUNoQkMsZUFBZSxVQUFnQixPQUFOaEI7WUFDN0I7UUFDSjtRQUVBLElBQUksQ0FBQ0ssU0FBU0MsRUFBRSxFQUFFO1lBQ2RDLFFBQVFDLEtBQUssQ0FBQyx1Q0FBdUNILFNBQVNJLFVBQVU7WUFDeEUsTUFBTSxJQUFJUSxNQUFNO1FBQ3BCO1FBRUEsTUFBTWdDLE9BQU8sTUFBTTVDLFNBQVNNLElBQUk7UUFFaEMsc0RBQXNEO1FBQ3RELE9BQU9zQztJQUNYLEVBQUUsT0FBT3pDLE9BQU87UUFDWkQsUUFBUUMsS0FBSyxDQUFDLHNDQUFzQ0E7UUFDcEQsTUFBTUE7SUFDVjtBQUNKO0FBRU8sZUFBZXlFLDJCQUEyQixLQUVoRDtRQUZnRCxFQUFFN0UsS0FBSyxFQUFDb0IsS0FBSyxFQUU3RCxHQUZnRDtJQUkvQyxNQUFNeEIsUUFBUU4saURBQU9BLENBQUNPLEdBQUcsQ0FBQztJQUMxQixJQUFJLENBQUNELE9BQU87UUFDVixNQUFNLElBQUlpQixNQUFNO0lBQ2xCO0lBQ0EsSUFBSSxDQUFDYixTQUFTLENBQUNvQixPQUFPO1FBQ3BCLE1BQU0sSUFBSVAsTUFBTTtJQUNsQjtJQUNBLE1BQU1pRSxVQUFrQyxDQUFDO0lBQ3pDLElBQUk5RSxPQUFPOEUsUUFBUTlFLEtBQUssR0FBR0E7SUFDM0IsSUFBSW9CLE9BQU8wRCxRQUFRMUQsS0FBSyxHQUFHQTtJQUUzQixNQUFNbkIsV0FBVyxNQUFNUSxNQUFNLEdBQWdCLE9BQWJqQixzREFBWUEsRUFBQyxnQ0FBOEI7UUFDekVrQixRQUFRO1FBQ1JDLFNBQVM7WUFDUCxnQkFBZ0I7WUFDaEJDLGVBQWUsVUFBZ0IsT0FBTmhCO1FBQzNCO1FBQ0F5QyxNQUFNQyxLQUFLQyxTQUFTLENBQUN1QztJQUN2QjtJQUVBLElBQUksQ0FBQzdFLFNBQVNDLEVBQUUsRUFBRTtRQUNoQixNQUFNNkUsWUFBWSxNQUFNOUUsU0FBU00sSUFBSTtRQUNyQyxNQUFNLElBQUlNLE1BQU1rRSxVQUFVckMsT0FBTyxJQUFJO0lBQ3ZDO0lBRUEsTUFBTVcsU0FBUyxNQUFNcEQsU0FBU00sSUFBSTtJQUNsQyxPQUFPOEM7QUFDVCIsInNvdXJjZXMiOlsiRDpcXGVseXNlZVxcUHJvamV0XFxQZXJzXFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcYXBwXFxzZXJ2aWNlc1xcVXNlclNlcnZpY2VzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcclxuaW1wb3J0IENvb2tpZXMgZnJvbSBcImpzLWNvb2tpZVwiO1xyXG5pbXBvcnQgeyBqd3REZWNvZGUgfSBmcm9tIFwiand0LWRlY29kZVwiO1xyXG5pbXBvcnQgeyBCQVNFX0FQSV9VUkwgfSBmcm9tIFwiLi9BdXRoQ29udGV4dFwiO1xyXG5pbXBvcnQgeyBVc2VyQ3JlYXRlU2NoZW1hLCBVc2VyU2NoZW1hLCBVc2VyVXBkYXRlU2NoZW1hIH0gZnJvbSBcIi4uL21vZGVscy9Vc2VyTW9kZWxcIjtcclxuaW1wb3J0IHsgYXV0aGVudGljYXRlZEdldCwgYXV0aGVudGljYXRlZFBvc3QsIGF1dGhlbnRpY2F0ZWRQdXQsIGF1dGhlbnRpY2F0ZWREZWxldGUgfSBmcm9tIFwiLi4vdXRpbHMvaHR0cEludGVyY2VwdG9yXCI7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gZ2V0VG9rZW5Gcm9tQ29va2llKG5hbWU6IHN0cmluZykge1xyXG4gICAgY29uc3QgdG9rZW4gPSBDb29raWVzLmdldChuYW1lKTtcclxuICAgIHJldHVybiB0b2tlbjtcclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldEN1cnJlbnRVc2VyKCk6IFByb21pc2U8VXNlclNjaGVtYSB8IG51bGw+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgdG9rZW4gPSBnZXRUb2tlbkZyb21Db29raWUoXCJpZFRva2VuXCIpO1xyXG4gICAgICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBkZWNvZGVkVXNlcjogYW55ID0gand0RGVjb2RlKHRva2VuKTtcclxuICAgICAgICBjb25zdCBlbWFpbCA9IGRlY29kZWRVc2VyLmVtYWlsIGFzIHN0cmluZztcclxuXHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhdXRoZW50aWNhdGVkR2V0KGAvdXNlci9nZXQtdXNlci1lbWFpbC8ke2VtYWlsfWApO1xyXG5cclxuICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyB1c2VyOlwiLCByZXNwb25zZS5zdGF0dXNUZXh0KTtcclxuICAgICAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCB1c2VyID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIHJldHVybiB1c2VyO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgY3VycmVudCB1c2VyOlwiLCBlcnJvcik7XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRQYXJlbnRzKCkge1xyXG4gICAgdHJ5IHtcclxuICAgICAgICBjb25zdCB0b2tlbiA9IGdldFRva2VuRnJvbUNvb2tpZShcImlkVG9rZW5cIik7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtCQVNFX0FQSV9VUkx9L3VzZXIvZ2V0LXBhcmVudHNgLCB7XHJcbiAgICAgICAgICAgIG1ldGhvZDogXCJHRVRcIixcclxuICAgICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgcGFyZW50czpcIiwgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBmZXRjaCBwYXJlbnRzIGRhdGFcIik7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBwYXJlbnRzTGlzdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAgICAgY29uc3QgcGFyZW50cyA9IHBhcmVudHNMaXN0Lm1hcCgodXNlcjogYW55KSA9PiAoe1xyXG4gICAgICAgICAgICBfaWQ6IHVzZXIuX2lkLFxyXG4gICAgICAgICAgICB1c2VyX2lkOiB1c2VyLnVzZXJfaWQsXHJcbiAgICAgICAgICAgIGZpcmViYXNlVWlkOiB1c2VyLmZpcmViYXNlVWlkLFxyXG4gICAgICAgICAgICBuYW1lOiB1c2VyLm5hbWUsXHJcbiAgICAgICAgICAgIGVtYWlsOiB1c2VyLmVtYWlsLFxyXG4gICAgICAgICAgICBwaG9uZTogdXNlci5waG9uZSxcclxuICAgICAgICAgICAgcm9sZTogdXNlci5yb2xlLFxyXG4gICAgICAgICAgICBhdmF0YXI6IHVzZXIuYXZhdGFyLFxyXG4gICAgICAgICAgICBhZGRyZXNzOiB1c2VyLmFkZHJlc3MsXHJcbiAgICAgICAgICAgIHNjaG9vbF9pZHM6IHVzZXIuc2Nob29sX2lkcyxcclxuICAgICAgICAgICAgc3R1ZGVudF9pZHM6IHVzZXIuc3R1ZGVudF9pZHMsIC8vIEFzc3VtaW5nIHBhcmVudHMgY2FuIGhhdmUgYXNzb2NpYXRlZCBzdHVkZW50c1xyXG4gICAgICAgICAgICBpc1ZlcmlmaWVkOiB1c2VyLmlzVmVyaWZpZWQsXHJcbiAgICAgICAgICAgIHZlcmlmaWNhdGlvbkNvZGU6IHVzZXIudmVyaWZpY2F0aW9uQ29kZSxcclxuICAgICAgICAgICAgdmVyaWZpY2F0aW9uQ29kZUV4cGlyZXM6IHVzZXIudmVyaWZpY2F0aW9uQ29kZUV4cGlyZXMsXHJcbiAgICAgICAgICAgIGxhc3RMb2dpbjogdXNlci5sYXN0TG9naW4sXHJcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogdXNlci5jcmVhdGVkQXQsXHJcbiAgICAgICAgICAgIHVwZGF0ZWRBdDogdXNlci51cGRhdGVkQXQsXHJcbiAgICAgICAgfSBhcyBVc2VyU2NoZW1hKSk7XHJcblxyXG4gICAgICAgIHJldHVybiBwYXJlbnRzO1xyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIHBhcmVudHM6XCIsIGVycm9yKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggcGFyZW50cyBkYXRhXCIpO1xyXG4gICAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VXNlcnMoKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHRva2VuID0gZ2V0VG9rZW5Gcm9tQ29va2llKFwiaWRUb2tlblwiKTsgLy8gQXNzdW1pbmcgdGhpcyBmdW5jdGlvbiBnZXRzIHRoZSB0b2tlbiBmcm9tIGNvb2tpZXNcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0JBU0VfQVBJX1VSTH0vdXNlci9nZXQtdXNlcnNgLCB7XHJcbiAgICAgICAgICAgIG1ldGhvZDogXCJHRVRcIixcclxuICAgICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCwgLy8gQXV0aG9yaXphdGlvbiBoZWFkZXIgd2l0aCBCZWFyZXIgdG9rZW5cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgdXNlcnM6XCIsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggdXNlcnMgZGF0YVwiKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IHVzZXJzTGlzdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICBjb25zdCB1c2VycyA9IHVzZXJzTGlzdC5tYXAoKHVzZXI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgX2lkOiB1c2VyLl9pZCxcclxuICAgICAgICAgICAgICAgIHVzZXJfaWQ6IHVzZXIudXNlcl9pZCxcclxuICAgICAgICAgICAgICAgIGZpcmViYXNlVWlkOiB1c2VyLmZpcmViYXNlVWlkLFxyXG4gICAgICAgICAgICAgICAgbmFtZTogdXNlci5uYW1lLFxyXG4gICAgICAgICAgICAgICAgZW1haWw6IHVzZXIuZW1haWwsXHJcbiAgICAgICAgICAgICAgICBwaG9uZTogdXNlci5waG9uZSxcclxuICAgICAgICAgICAgICAgIHJvbGU6IHVzZXIucm9sZSxcclxuICAgICAgICAgICAgICAgIGF2YXRhcjogdXNlci5hdmF0YXIsXHJcbiAgICAgICAgICAgICAgICBhZGRyZXNzOiB1c2VyLmFkZHJlc3MsXHJcbiAgICAgICAgICAgICAgICBzY2hvb2xfaWRzOiB1c2VyLnNjaG9vbF9pZHMsXHJcbiAgICAgICAgICAgICAgICBpc1ZlcmlmaWVkOiB1c2VyLmlzVmVyaWZpZWQsXHJcbiAgICAgICAgICAgICAgICB2ZXJpZmljYXRpb25Db2RlOiB1c2VyLnZlcmlmaWNhdGlvbkNvZGUsXHJcbiAgICAgICAgICAgICAgICB2ZXJpZmljYXRpb25Db2RlRXhwaXJlczogdXNlci52ZXJpZmljYXRpb25Db2RlRXhwaXJlcyxcclxuICAgICAgICAgICAgICAgIGxhc3RMb2dpbjogdXNlci5sYXN0TG9naW4sXHJcbiAgICAgICAgICAgICAgICBjcmVhdGVkQXQ6IHVzZXIuY3JlYXRlZEF0LFxyXG4gICAgICAgICAgICAgICAgdXBkYXRlZEF0OiB1c2VyLnVwZGF0ZWRBdCxcclxuICAgICAgICAgICAgfSBhcyBVc2VyU2NoZW1hO1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICByZXR1cm4gdXNlcnM7XHJcblxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgdXNlcnM6XCIsIGVycm9yKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggdXNlcnMgZGF0YVwiKTtcclxuICAgIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZVVzZXIodXNlckRhdGE6IFVzZXJDcmVhdGVTY2hlbWEpIHtcclxuICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtCQVNFX0FQSV9VUkx9L3VzZXIvcmVnaXN0ZXItdXNlcmAsIHtcclxuICAgICAgICAgICAgbWV0aG9kOiBcIlBPU1RcIixcclxuICAgICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Z2V0VG9rZW5Gcm9tQ29va2llKFwiaWRUb2tlblwiKX1gLCAvLyBBZGQgdGhlIEJlYXJlciB0b2tlbiBmb3IgYXV0aG9yaXphdGlvblxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh1c2VyRGF0YSksIC8vIFNlbmQgdGhlIHVzZXIgZGF0YSBpbiB0aGUgYm9keSBvZiB0aGUgcmVxdWVzdFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSBcIkZhaWxlZCB0byBjcmVhdGUgdXNlciBkYXRhXCI7XHJcblxyXG4gICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZXJyb3JCb2R5ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3JCb2R5Py5tZXNzYWdlIHx8IGVycm9yTWVzc2FnZTtcclxuICAgICAgICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xyXG4gICAgICAgICAgICAgICAgLy8gSWYgcGFyc2luZyB0aGUgZXJyb3IgYm9keSBmYWlscywgdXNlIGRlZmF1bHQgbWVzc2FnZVxyXG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFwiQ291bGQgbm90IHBhcnNlIGVycm9yIHJlc3BvbnNlOlwiLCBwYXJzZUVycm9yKTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGNyZWF0aW5nIHVzZXI6XCIsIGVycm9yTWVzc2FnZSk7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvck1lc3NhZ2UpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTsgLy8gUGFyc2UgdGhlIHJlc3BvbnNlIGFzIEpTT05cclxuICAgICAgICByZXR1cm4gZGF0YTsgLy8gUmV0dXJuIHRoZSByZXNwb25zZSBkYXRhICh1c3VhbGx5IHRoZSBjcmVhdGVkIHVzZXIgb2JqZWN0KVxyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGNyZWF0aW5nIHVzZXI6XCIsIGVycm9yKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBcIkZhaWxlZCB0byBjcmVhdGUgdXNlciBkYXRhXCIpO1xyXG4gICAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdXBkYXRlVXNlcih1c2VyX2lkOiBzdHJpbmcsIHVzZXJEYXRhOiBVc2VyVXBkYXRlU2NoZW1hKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QkFTRV9BUElfVVJMfS91c2VyL3VwZGF0ZS11c2VyLyR7dXNlcl9pZH1gLCB7XHJcbiAgICAgICAgICAgIG1ldGhvZDogXCJQVVRcIiwgLy8gVXNpbmcgUFVUIG1ldGhvZCB0byB1cGRhdGUgdGhlIHVzZXJcclxuICAgICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Z2V0VG9rZW5Gcm9tQ29va2llKFwiaWRUb2tlblwiKX1gLCAvLyBBZGQgQmVhcmVyIHRva2VuIGZvciBhdXRob3JpemF0aW9uXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHVzZXJEYXRhKSwgLy8gU2VuZCB0aGUgdXBkYXRlZCB1c2VyIGRhdGEgaW4gdGhlIGJvZHkgb2YgdGhlIHJlcXVlc3RcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgdXBkYXRpbmcgdXNlcjpcIiwgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byB1cGRhdGUgdXNlciBkYXRhXCIpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTsgLy8gUGFyc2UgdGhlIHJlc3BvbnNlIGFzIEpTT05cclxuICAgICAgICByZXR1cm4gZGF0YTsgLy8gUmV0dXJuIHRoZSB1cGRhdGVkIHVzZXIgZGF0YSAodGhpcyBjb3VsZCBiZSB0aGUgdXNlciBvYmplY3Qgd2l0aCB1cGRhdGVkIGZpZWxkcylcclxuXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciB1cGRhdGluZyB1c2VyOlwiLCBlcnJvcik7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIHVwZGF0ZSB1c2VyIGRhdGFcIik7XHJcbiAgICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRVc2VyQnlJZCh1c2VySWQ6IHN0cmluZykge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtCQVNFX0FQSV9VUkx9L3VzZXIvZ2V0LXVzZXIvJHt1c2VySWR9YCwge1xyXG4gICAgICAgIG1ldGhvZDogXCJHRVRcIixcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Z2V0VG9rZW5Gcm9tQ29va2llKFwiaWRUb2tlblwiKX1gLFxyXG4gICAgICAgIH0sXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIHVzZXI6XCIsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBmZXRjaCB1c2VyIGRhdGFcIik7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICBjb25zdCB1c2VyID0ge1xyXG4gICAgICAgIF9pZDogZGF0YS5faWQsXHJcbiAgICAgICAgdXNlcl9pZDogZGF0YS51c2VyX2lkLFxyXG4gICAgICAgIG5hbWU6IGRhdGEubmFtZSxcclxuICAgICAgICBlbWFpbDogZGF0YS5lbWFpbCxcclxuICAgICAgICBwaG9uZTogZGF0YS5waG9uZSxcclxuICAgICAgICByb2xlOiBkYXRhLnJvbGUsXHJcbiAgICAgICAgYWRkcmVzczogZGF0YS5hZGRyZXNzLFxyXG4gICAgICAgIHNjaG9vbF9pZHM6IGRhdGEuc2Nob29sX2lkcywgLy8gTGlzdCBvZiBzY2hvb2wgSURzIGlmIGFwcGxpY2FibGVcclxuICAgICAgICBpc1ZlcmlmaWVkOiBkYXRhLmlzVmVyaWZpZWQsIC8vIEFzc3VtaW5nIHRoZXJlIGlzIGFuICdpc1ZlcmlmaWVkJyBmaWVsZFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBkYXRhLmRlc2NyaXB0aW9uLCAvLyBBc3N1bWluZyB1c2VycyBjYW4gaGF2ZSBhIGRlc2NyaXB0aW9uXHJcbiAgICAgICAgZmlyZWJhc2VVaWQ6IGRhdGEuZmlyZWJhc2VVaWQsIC8vIEFkZCBmaXJlYmFzZVVpZCBpZiBhdmFpbGFibGVcclxuICAgICAgICBjcmVhdGVkQXQ6IGRhdGEuY3JlYXRlZEF0LCAvLyBBZGQgY3JlYXRlZEF0IGlmIGF2YWlsYWJsZVxyXG4gICAgICAgIHVwZGF0ZWRBdDogZGF0YS51cGRhdGVkQXQsIC8vIEFkZCB1cGRhdGVkQXQgaWYgYXZhaWxhYmxlXHJcbiAgICB9IGFzIFVzZXJTY2hlbWE7XHJcblxyXG4gICAgcmV0dXJuIHVzZXI7XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB2ZXJpZnlQYXNzd29yZChwYXNzd29yZDogc3RyaW5nLCBlbWFpbDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0JBU0VfQVBJX1VSTH0vYXV0aC92ZXJpZnktcGFzc3dvcmRgLCB7XHJcbiAgICAgICAgbWV0aG9kOiBcIlBPU1RcIixcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgICBlbWFpbDogZW1haWwsXHJcbiAgICAgICAgICAgIHBhc3N3b3JkOiBwYXNzd29yZCxcclxuICAgICAgICB9KSxcclxuICAgIH0pO1xyXG5cclxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgdXNlcjpcIiwgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XHJcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiB0cnVlO1xyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZGVsZXRlVXNlcih1c2VyX2lkOiBzdHJpbmcpIHtcclxuICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtCQVNFX0FQSV9VUkx9L3VzZXIvZGVsZXRlLXVzZXIvJHt1c2VyX2lkfWAsIHtcclxuICAgICAgICAgICAgbWV0aG9kOiBcIkRFTEVURVwiLFxyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHtnZXRUb2tlbkZyb21Db29raWUoXCJpZFRva2VuXCIpfWAsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgICAgbGV0IGVycm9yTWVzc2FnZSA9IFwiRmFpbGVkIHRvIGRlbGV0ZSB1c2VyXCI7XHJcblxyXG4gICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZXJyb3JCb2R5ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3JCb2R5Py5tZXNzYWdlIHx8IGVycm9yTWVzc2FnZTtcclxuICAgICAgICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFwiQ291bGQgbm90IHBhcnNlIGVycm9yIHJlc3BvbnNlOlwiLCBwYXJzZUVycm9yKTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGRlbGV0aW5nIHVzZXI6XCIsIGVycm9yTWVzc2FnZSk7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvck1lc3NhZ2UpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIHJldHVybiByZXN1bHQ7IC8vIE1pZ2h0IHJldHVybiBhIHN1Y2Nlc3MgbWVzc2FnZSBvciBkZWxldGVkIHVzZXIgZGF0YVxyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGRlbGV0aW5nIHVzZXI6XCIsIGVycm9yKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBcIkZhaWxlZCB0byBkZWxldGUgdXNlclwiKTtcclxuICAgIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFVzZXJCeV9pZChfaWQ6IHN0cmluZykge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtCQVNFX0FQSV9VUkx9L3VzZXIvZ2V0LXVzZXItYnktaWQvJHtfaWR9YCwge1xyXG4gICAgICAgIG1ldGhvZDogXCJHRVRcIixcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Z2V0VG9rZW5Gcm9tQ29va2llKFwiaWRUb2tlblwiKX1gLFxyXG4gICAgICAgIH0sXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIHVzZXI6XCIsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBmZXRjaCB1c2VyIGRhdGFcIik7XHJcbiAgICB9XHJcbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG5cclxuICAgIGNvbnN0IHVzZXIgPSB7XHJcbiAgICAgICAgX2lkOiBkYXRhLl9pZCxcclxuICAgICAgICB1c2VyX2lkOiBkYXRhLnVzZXJfaWQsXHJcbiAgICAgICAgbmFtZTogZGF0YS5uYW1lLFxyXG4gICAgICAgIGVtYWlsOiBkYXRhLmVtYWlsLFxyXG4gICAgICAgIHBob25lOiBkYXRhLnBob25lLFxyXG4gICAgICAgIHJvbGU6IGRhdGEucm9sZSxcclxuICAgICAgICBhZGRyZXNzOiBkYXRhLmFkZHJlc3MsXHJcbiAgICAgICAgc2Nob29sX2lkczogZGF0YS5zY2hvb2xfaWRzLCAvLyBMaXN0IG9mIHNjaG9vbCBJRHMgaWYgYXBwbGljYWJsZVxyXG4gICAgICAgIGlzVmVyaWZpZWQ6IGRhdGEuaXNWZXJpZmllZCwgLy8gQXNzdW1pbmcgdGhlcmUgaXMgYW4gJ2lzVmVyaWZpZWQnIGZpZWxkXHJcbiAgICAgICAgZGVzY3JpcHRpb246IGRhdGEuZGVzY3JpcHRpb24sIC8vIEFzc3VtaW5nIHVzZXJzIGNhbiBoYXZlIGEgZGVzY3JpcHRpb25cclxuICAgICAgICBmaXJlYmFzZVVpZDogZGF0YS5maXJlYmFzZVVpZCwgLy8gQWRkIGZpcmViYXNlVWlkIGlmIGF2YWlsYWJsZVxyXG4gICAgICAgIGNyZWF0ZWRBdDogZGF0YS5jcmVhdGVkQXQsIC8vIEFkZCBjcmVhdGVkQXQgaWYgYXZhaWxhYmxlXHJcbiAgICAgICAgdXBkYXRlZEF0OiBkYXRhLnVwZGF0ZWRBdCwgLy8gQWRkIHVwZGF0ZWRBdCBpZiBhdmFpbGFibGVcclxuICAgIH0gYXMgVXNlclNjaGVtYTtcclxuXHJcbiAgICByZXR1cm4gdXNlcjtcclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGZvcmdldF9wYXNzd29yZChlbWFpbDogc3RyaW5nKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QkFTRV9BUElfVVJMfS9hdXRoL2ZvcmdvdC1wYXNzd29yZGAsIHtcclxuICAgICAgICAgICAgbWV0aG9kOiBcIlBPU1RcIixcclxuICAgICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICAgICAgICAgIGVtYWlsOiBlbWFpbCxcclxuICAgICAgICAgICAgfSksXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgICAgbGV0IGVycm9yTWVzc2FnZSA9IFwiRmFpbGVkIHRvIHNlbmQgcmVzZXQgcGFzc3dvcmQgZW1haWw6IGNoZWNrIHlvdXIgZW1haWxcIjtcclxuICAgICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yQm9keSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yQm9keT8ubWVzc2FnZSB8fCBlcnJvck1lc3NhZ2U7XHJcbiAgICAgICAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihcIkNvdWxkIG5vdCBwYXJzZSBlcnJvciByZXNwb25zZTpcIiwgcGFyc2VFcnJvcik7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yTWVzc2FnZSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIHJldHVybiBkYXRhO1xyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHNlbmRpbmcgcmVzZXQgcGFzc3dvcmQgZW1haWw6XCIsIGVycm9yKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gc2VuZCByZXNldCBwYXNzd29yZCBlbWFpbFwiKTtcclxuICAgIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHZlcmlmeV9vdHAoY29kZTogc3RyaW5nLCBlbWFpbDogc3RyaW5nKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QkFTRV9BUElfVVJMfS9hdXRoL3ZlcmlmeS1jb2RlYCwge1xyXG4gICAgICAgICAgICBtZXRob2Q6IFwiUE9TVFwiLFxyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgICAgICAgY29kZTogY29kZSxcclxuICAgICAgICAgICAgICAgIGVtYWlsOiBlbWFpbCxcclxuICAgICAgICAgICAgfSksXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgICAgbGV0IGVycm9yTWVzc2FnZSA9IFwiRmFpbGVkIHRvIHZlcmlmeSBPVFBcIjtcclxuICAgICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yQm9keSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yQm9keT8ubWVzc2FnZSB8fCBlcnJvck1lc3NhZ2U7XHJcbiAgICAgICAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihcIkNvdWxkIG5vdCBwYXJzZSBlcnJvciByZXNwb25zZTpcIiwgcGFyc2VFcnJvcik7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yTWVzc2FnZSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIHJldHVybiBkYXRhO1xyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHZlcmlmeWluZyBPVFA6XCIsIGVycm9yKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gdmVyaWZ5IE9UUFwiKTtcclxuICAgIH1cclxufVxyXG5cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiByZXNlbmRfQ29kZShlbWFpbDogc3RyaW5nKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QkFTRV9BUElfVVJMfS9hdXRoL3Jlc2VuZC1jb2RlYCwge1xyXG4gICAgICAgICAgICBtZXRob2Q6IFwiUE9TVFwiLFxyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgICAgICAgZW1haWw6IGVtYWlsLFxyXG4gICAgICAgICAgICB9KSxcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgICBsZXQgZXJyb3JNZXNzYWdlID0gXCJGYWlsZWQgdG8gcmVzZW5kIGNvZGVcIjtcclxuICAgICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yQm9keSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yQm9keT8ubWVzc2FnZSB8fCBlcnJvck1lc3NhZ2U7XHJcbiAgICAgICAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihcIkNvdWxkIG5vdCBwYXJzZSBlcnJvciByZXNwb25zZTpcIiwgcGFyc2VFcnJvcik7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yTWVzc2FnZSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIHJldHVybiBkYXRhO1xyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHJlc2VuZGluZyBjb2RlOlwiLCBlcnJvcik7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIHJlc2VuZCBjb2RlXCIpO1xyXG4gICAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVzZXRfcGFzc3dvcmQobmV3UGFzc3dvcmQ6IHN0cmluZywgZW1haWw6IHN0cmluZywgY29kZTogc3RyaW5nKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QkFTRV9BUElfVVJMfS9hdXRoL3Jlc2V0LXBhc3N3b3JkYCwge1xyXG4gICAgICAgICAgICBtZXRob2Q6IFwiUE9TVFwiLFxyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgICAgICAgZW1haWw6IGVtYWlsLFxyXG4gICAgICAgICAgICAgICAgY29kZTogY29kZSxcclxuICAgICAgICAgICAgICAgIG5ld1Bhc3N3b3JkOiBuZXdQYXNzd29yZCxcclxuICAgICAgICAgICAgfSksXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgICAgbGV0IGVycm9yTWVzc2FnZSA9IFwiRmFpbGVkIHRvIHJlc2V0IHBhc3N3b3JkXCI7XHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBlcnJvckJvZHkgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvckJvZHk/Lm1lc3NhZ2UgfHwgZXJyb3JNZXNzYWdlO1xyXG4gICAgICAgICAgICB9IGNhdGNoIChwYXJzZUVycm9yKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oXCJDb3VsZCBub3QgcGFyc2UgZXJyb3IgcmVzcG9uc2U6XCIsIHBhcnNlRXJyb3IpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvck1lc3NhZ2UpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICByZXR1cm4gZGF0YTtcclxuXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciByZXNldHRpbmcgcGFzc3dvcmQ6XCIsIGVycm9yKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gcmVzZXQgcGFzc3dvcmRcIik7XHJcbiAgICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBoYW5kbGVVc2VyU2VhcmNoKHF1ZXJ5OiBzdHJpbmcpOiBQcm9taXNlPFVzZXJTY2hlbWFbXT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgICBjb25zdCB0b2tlbiA9IGdldFRva2VuRnJvbUNvb2tpZShcImlkVG9rZW5cIik7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtCQVNFX0FQSV9VUkx9L3VzZXIvc2VhcmNoLXVzZXJzP3F1ZXJ5PSR7ZW5jb2RlVVJJQ29tcG9uZW50KHF1ZXJ5KX1gLCB7XHJcbiAgICAgICAgICAgIG1ldGhvZDogXCJHRVRcIixcclxuICAgICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3Igc2VhcmNoaW5nIHVzZXJzOlwiLCByZXNwb25zZS5zdGF0dXNUZXh0KTtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIHNlYXJjaCB1c2Vyc1wiKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IHJlc3VsdHMgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcblxyXG4gICAgICAgIGNvbnN0IHVzZXJzID0gcmVzdWx0cy5tYXAoKHVzZXI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgX2lkOiB1c2VyLl9pZCxcclxuICAgICAgICAgICAgICAgIHVzZXJfaWQ6IHVzZXIudXNlcl9pZCxcclxuICAgICAgICAgICAgICAgIGZpcmViYXNlVWlkOiB1c2VyLmZpcmViYXNlVWlkLFxyXG4gICAgICAgICAgICAgICAgbmFtZTogdXNlci5uYW1lLFxyXG4gICAgICAgICAgICAgICAgZW1haWw6IHVzZXIuZW1haWwsXHJcbiAgICAgICAgICAgICAgICBwaG9uZTogdXNlci5waG9uZSxcclxuICAgICAgICAgICAgICAgIHJvbGU6IHVzZXIucm9sZSxcclxuICAgICAgICAgICAgICAgIGF2YXRhcjogdXNlci5hdmF0YXIsXHJcbiAgICAgICAgICAgICAgICBhZGRyZXNzOiB1c2VyLmFkZHJlc3MsXHJcbiAgICAgICAgICAgICAgICBzY2hvb2xfaWRzOiB1c2VyLnNjaG9vbF9pZHMsXHJcbiAgICAgICAgICAgICAgICBpc1ZlcmlmaWVkOiB1c2VyLmlzVmVyaWZpZWQsXHJcbiAgICAgICAgICAgICAgICB2ZXJpZmljYXRpb25Db2RlOiB1c2VyLnZlcmlmaWNhdGlvbkNvZGUsXHJcbiAgICAgICAgICAgICAgICB2ZXJpZmljYXRpb25Db2RlRXhwaXJlczogdXNlci52ZXJpZmljYXRpb25Db2RlRXhwaXJlcyxcclxuICAgICAgICAgICAgICAgIGxhc3RMb2dpbjogdXNlci5sYXN0TG9naW4sXHJcbiAgICAgICAgICAgICAgICBjcmVhdGVkQXQ6IHVzZXIuY3JlYXRlZEF0LFxyXG4gICAgICAgICAgICAgICAgdXBkYXRlZEF0OiB1c2VyLnVwZGF0ZWRBdCxcclxuICAgICAgICAgICAgfSBhcyBVc2VyU2NoZW1hO1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICByZXR1cm4gdXNlcnM7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzZWFyY2hpbmcgdXNlcnM6XCIsIGVycm9yKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gc2VhcmNoIHVzZXJzXCIpO1xyXG4gICAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVnaXN0ZXJQYXJlbnQocGFyZW50RGF0YTogYW55KSB7XHJcbiAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QkFTRV9BUElfVVJMfS91c2VyL3JlZ2lzdGVyLXBhcmVudGAsIHtcclxuICAgICAgICAgICAgbWV0aG9kOiBcIlBPU1RcIixcclxuICAgICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Z2V0VG9rZW5Gcm9tQ29va2llKFwiaWRUb2tlblwiKX1gLCAvLyBBc3N1bWluZyBGaXJlYmFzZSBvciBzaW1pbGFyIHRva2VuIGF1dGhcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocGFyZW50RGF0YSksXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgICAgbGV0IGVycm9yTWVzc2FnZSA9IFwiRmFpbGVkIHRvIHJlZ2lzdGVyIHBhcmVudFwiO1xyXG5cclxuICAgICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yQm9keSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yQm9keT8ubWVzc2FnZSB8fCBlcnJvck1lc3NhZ2U7XHJcbiAgICAgICAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihcIkNvdWxkIG5vdCBwYXJzZSBlcnJvciByZXNwb25zZTpcIiwgcGFyc2VFcnJvcik7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciByZWdpc3RlcmluZyBwYXJlbnQ6XCIsIGVycm9yTWVzc2FnZSk7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvck1lc3NhZ2UpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICByZXR1cm4gZGF0YTsgLy8gVGhpcyBpbmNsdWRlcyB1c2VyIG9iamVjdCBhbmQgZ2VuZXJhdGVkUGFzc3dvcmQgKGlmIG5ldylcclxuXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBpbiByZWdpc3RlclBhcmVudCBzZXJ2aWNlOlwiLCBlcnJvcik7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogXCJGYWlsZWQgdG8gcmVnaXN0ZXIgcGFyZW50XCIpO1xyXG4gICAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZGVsZXRlTXVsdGlwbGVVc2Vycyh1c2VySWRzOiBzdHJpbmdbXSkge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtCQVNFX0FQSV9VUkx9L3VzZXIvZGVsZXRlLXVzZXJzYCwge1xyXG4gICAgICAgIG1ldGhvZDogXCJERUxFVEVcIixcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Z2V0VG9rZW5Gcm9tQ29va2llKFwiaWRUb2tlblwiKX1gLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBpZHM6IHVzZXJJZHMgfSksXHJcbiAgICB9KTtcclxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZGVsZXRpbmcgbXVsdGlwbGUgdXNlcnM6XCIsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBkZWxldGUgbXVsdGlwbGUgdXNlcnNcIik7XHJcbiAgICB9XHJcbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgcmV0dXJuIGRhdGE7XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBkZWxldGVBbGxVc2VycygpIHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QkFTRV9BUElfVVJMfS91c2VyL2RlbGV0ZS1hbGwtdXNlcnNgLCB7XHJcbiAgICAgICAgbWV0aG9kOiBcIkRFTEVURVwiLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHtnZXRUb2tlbkZyb21Db29raWUoXCJpZFRva2VuXCIpfWAsXHJcbiAgICAgICAgfSxcclxuICAgIH0pO1xyXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBkZWxldGluZyBhbGwgdXNlcnM6XCIsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBkZWxldGUgYWxsIHVzZXJzXCIpO1xyXG4gICAgfVxyXG4gICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgIHJldHVybiBkYXRhO1xyXG59XHJcblxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFRvdGFsVXNlcnMoKTogUHJvbWlzZTxudW1iZXI+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgdG9rZW4gPSBnZXRUb2tlbkZyb21Db29raWUoXCJpZFRva2VuXCIpO1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QkFTRV9BUElfVVJMfS91c2VyL3RvdGFsLXVzZXJzYCwge1xyXG4gICAgICAgICAgICBtZXRob2Q6IFwiR0VUXCIsXHJcbiAgICAgICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIHRvdGFsIHVzZXJzOlwiLCByZXNwb25zZS5zdGF0dXNUZXh0KTtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIGZldGNoIHRvdGFsIHVzZXJzXCIpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICByZXR1cm4gZGF0YS50b3RhbFVzZXJzO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgdG90YWwgdXNlcnM6XCIsIGVycm9yKTtcclxuICAgICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFVzZXJDb3VudFdpdGhDaGFuZ2UoKTogUHJvbWlzZTx7XHJcbiAgICB0b3RhbFVzZXJzVGhpc01vbnRoOiBudW1iZXI7XHJcbiAgICBwZXJjZW50YWdlQ2hhbmdlOiBudW1iZXI7XHJcbn0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgdG9rZW4gPSBnZXRUb2tlbkZyb21Db29raWUoXCJpZFRva2VuXCIpO1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QkFTRV9BUElfVVJMfS91c2VyL3VzZXJzLWNvdW50LWNoYW5nZWAsIHtcclxuICAgICAgICAgICAgbWV0aG9kOiBcIkdFVFwiLFxyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyB1c2VyIGNvdW50IGNoYW5nZTpcIiwgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBmZXRjaCB1c2VyIGNvdW50IGNoYW5nZVwiKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgdG90YWxVc2Vyc1RoaXNNb250aDogZGF0YS50b3RhbFVzZXJzVGhpc01vbnRoLFxyXG4gICAgICAgICAgICBwZXJjZW50YWdlQ2hhbmdlOiBkYXRhLnBlcmNlbnRhZ2VDaGFuZ2UsXHJcbiAgICAgICAgfTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIHVzZXIgY291bnQgY2hhbmdlOlwiLCBlcnJvcik7XHJcbiAgICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgTW9udGhseVVzZXJTdGF0IHtcclxuICAgIG1vbnRoOiBzdHJpbmc7XHJcbiAgICB1c2VyczogbnVtYmVyO1xyXG59XHJcblxyXG4vLyBUaGUgYmFja2VuZCByZXR1cm5zIGFuIG9iamVjdCB3aGVyZSBlYWNoIGtleSBpcyBhIHllYXIgKHN0cmluZykgXHJcbi8vIGFuZCB0aGUgdmFsdWUgaXMgYW4gYXJyYXkgb2YgTW9udGhseVVzZXJTdGF0XHJcbmV4cG9ydCBpbnRlcmZhY2UgTW9udGhseVVzZXJTdGF0c0J5WWVhciB7XHJcbiAgICBbeWVhcjogc3RyaW5nXTogTW9udGhseVVzZXJTdGF0W107XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRNb250aGx5VXNlclN0YXJ0cygpOiBQcm9taXNlPE1vbnRobHlVc2VyU3RhdHNCeVllYXI+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgdG9rZW4gPSBnZXRUb2tlbkZyb21Db29raWUoXCJpZFRva2VuXCIpO1xyXG5cclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0JBU0VfQVBJX1VSTH0vdXNlci9tb250aGx5LXVzZXItc3RhcnRzYCwge1xyXG4gICAgICAgICAgICBtZXRob2Q6IFwiR0VUXCIsXHJcbiAgICAgICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBmZXRjaCBtb250aGx5IHVzZXIgc3RhdHM6XCIsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggbW9udGhseSB1c2VyIHN0YXRzXCIpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAgICAgLy8gZGF0YSBoYXMgdHlwZSB7IFt5ZWFyOiBzdHJpbmddOiBNb250aGx5VXNlclN0YXRbXSB9XHJcbiAgICAgICAgcmV0dXJuIGRhdGEgYXMgTW9udGhseVVzZXJTdGF0c0J5WWVhcjtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIG1vbnRobHkgdXNlciBzdGF0czpcIiwgZXJyb3IpO1xyXG4gICAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVzZXRQYXJlbnRQYXNzd29yZFNlcnZpY2UoeyBlbWFpbCxwaG9uZSxcclxufToge2VtYWlsPzogc3RyaW5nOyBwaG9uZT86IHN0cmluZztcclxufSkge1xyXG5cclxuICBjb25zdCB0b2tlbiA9IENvb2tpZXMuZ2V0KFwiaWRUb2tlblwiKTtcclxuICBpZiAoIXRva2VuKSB7XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJZb3UgbXVzdCBiZSBsb2dnZWQgaW4gdG8gcGVyZm9ybSB0aGlzIGFjdGlvbi5cIik7XHJcbiAgfVxyXG4gIGlmICghZW1haWwgJiYgIXBob25lKSB7XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJFbWFpbCBvciBwaG9uZSBudW1iZXIgaXMgcmVxdWlyZWQgdG8gcmVzZXQgcGFzc3dvcmQuXCIpO1xyXG4gIH1cclxuICBjb25zdCBwYXlsb2FkOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge307XHJcbiAgaWYgKGVtYWlsKSBwYXlsb2FkLmVtYWlsID0gZW1haWw7XHJcbiAgaWYgKHBob25lKSBwYXlsb2FkLnBob25lID0gcGhvbmU7XHJcblxyXG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QkFTRV9BUElfVVJMfS91c2VyL3Jlc2V0LXBhcmVudC1wYXNzd29yZGAsIHtcclxuICAgIG1ldGhvZDogXCJQT1NUXCIsXHJcbiAgICBoZWFkZXJzOiB7XHJcbiAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgIH0sXHJcbiAgICBib2R5OiBKU09OLnN0cmluZ2lmeShwYXlsb2FkKSxcclxuICB9KTtcclxuXHJcbiAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5tZXNzYWdlIHx8IFwiRmFpbGVkIHRvIHJlc2V0IHBhc3N3b3JkXCIpO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gIHJldHVybiByZXN1bHQ7XHJcbn0iXSwibmFtZXMiOlsiQ29va2llcyIsImp3dERlY29kZSIsIkJBU0VfQVBJX1VSTCIsImF1dGhlbnRpY2F0ZWRHZXQiLCJnZXRUb2tlbkZyb21Db29raWUiLCJuYW1lIiwidG9rZW4iLCJnZXQiLCJnZXRDdXJyZW50VXNlciIsImRlY29kZWRVc2VyIiwiZW1haWwiLCJyZXNwb25zZSIsIm9rIiwiY29uc29sZSIsImVycm9yIiwic3RhdHVzVGV4dCIsInVzZXIiLCJqc29uIiwiZ2V0UGFyZW50cyIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsIkF1dGhvcml6YXRpb24iLCJFcnJvciIsInBhcmVudHNMaXN0IiwicGFyZW50cyIsIm1hcCIsIl9pZCIsInVzZXJfaWQiLCJmaXJlYmFzZVVpZCIsInBob25lIiwicm9sZSIsImF2YXRhciIsImFkZHJlc3MiLCJzY2hvb2xfaWRzIiwic3R1ZGVudF9pZHMiLCJpc1ZlcmlmaWVkIiwidmVyaWZpY2F0aW9uQ29kZSIsInZlcmlmaWNhdGlvbkNvZGVFeHBpcmVzIiwibGFzdExvZ2luIiwiY3JlYXRlZEF0IiwidXBkYXRlZEF0IiwiZ2V0VXNlcnMiLCJ1c2Vyc0xpc3QiLCJ1c2VycyIsImNyZWF0ZVVzZXIiLCJ1c2VyRGF0YSIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiZXJyb3JNZXNzYWdlIiwiZXJyb3JCb2R5IiwibWVzc2FnZSIsInBhcnNlRXJyb3IiLCJ3YXJuIiwiZGF0YSIsInVwZGF0ZVVzZXIiLCJnZXRVc2VyQnlJZCIsInVzZXJJZCIsImRlc2NyaXB0aW9uIiwidmVyaWZ5UGFzc3dvcmQiLCJwYXNzd29yZCIsImRlbGV0ZVVzZXIiLCJyZXN1bHQiLCJnZXRVc2VyQnlfaWQiLCJmb3JnZXRfcGFzc3dvcmQiLCJ2ZXJpZnlfb3RwIiwiY29kZSIsInJlc2VuZF9Db2RlIiwicmVzZXRfcGFzc3dvcmQiLCJuZXdQYXNzd29yZCIsImhhbmRsZVVzZXJTZWFyY2giLCJxdWVyeSIsImVuY29kZVVSSUNvbXBvbmVudCIsInJlc3VsdHMiLCJyZWdpc3RlclBhcmVudCIsInBhcmVudERhdGEiLCJkZWxldGVNdWx0aXBsZVVzZXJzIiwidXNlcklkcyIsImlkcyIsImRlbGV0ZUFsbFVzZXJzIiwiZ2V0VG90YWxVc2VycyIsInRvdGFsVXNlcnMiLCJnZXRVc2VyQ291bnRXaXRoQ2hhbmdlIiwidG90YWxVc2Vyc1RoaXNNb250aCIsInBlcmNlbnRhZ2VDaGFuZ2UiLCJnZXRNb250aGx5VXNlclN0YXJ0cyIsInJlc2V0UGFyZW50UGFzc3dvcmRTZXJ2aWNlIiwicGF5bG9hZCIsImVycm9yRGF0YSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/UserServices.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/utils/httpInterceptor.tsx":
/*!*******************************************!*\
  !*** ./src/app/utils/httpInterceptor.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticatedDelete: () => (/* binding */ authenticatedDelete),\n/* harmony export */   authenticatedFetch: () => (/* binding */ authenticatedFetch),\n/* harmony export */   authenticatedGet: () => (/* binding */ authenticatedGet),\n/* harmony export */   authenticatedPost: () => (/* binding */ authenticatedPost),\n/* harmony export */   authenticatedPut: () => (/* binding */ authenticatedPut),\n/* harmony export */   checkAuthStatus: () => (/* binding */ checkAuthStatus),\n/* harmony export */   clearSession: () => (/* binding */ clearSession),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _services_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/AuthContext */ \"(app-pages-browser)/./src/app/services/AuthContext.tsx\");\n\n\n// Fonction pour gérer la déconnexion automatique\nconst handleUnauthorized = ()=>{\n    // Supprimer le token\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"idToken\");\n    // Rediriger vers login\n    if (true) {\n        window.location.href = '/login';\n    }\n};\n// Fonction pour obtenir le token\nconst getAuthToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"idToken\") || null;\n};\n// Intercepteur HTTP personnalisé\nconst authenticatedFetch = async function(url) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const token = getAuthToken();\n    // Si pas de token, rediriger immédiatement\n    if (!token) {\n        handleUnauthorized();\n        throw new Error(\"No authentication token found\");\n    }\n    // Ajouter le token aux headers\n    const headers = {\n        'Content-Type': 'application/json',\n        ...options.headers,\n        'Authorization': \"Bearer \".concat(token)\n    };\n    // Construire l'URL complète si nécessaire\n    const fullUrl = url.startsWith('http') ? url : \"\".concat(_services_AuthContext__WEBPACK_IMPORTED_MODULE_1__.BASE_API_URL).concat(url);\n    try {\n        const response = await fetch(fullUrl, {\n            ...options,\n            headers\n        });\n        // Vérifier si la réponse indique une erreur d'authentification\n        if (response.status === 401) {\n            console.warn(\"Token expired or invalid, redirecting to login\");\n            handleUnauthorized();\n            throw new Error(\"Authentication failed\");\n        }\n        // Vérifier si la réponse indique un token expiré\n        if (response.status === 403) {\n            var _errorData_message, _errorData_message1;\n            const errorData = await response.clone().json().catch(()=>({}));\n            if (((_errorData_message = errorData.message) === null || _errorData_message === void 0 ? void 0 : _errorData_message.includes('token')) || ((_errorData_message1 = errorData.message) === null || _errorData_message1 === void 0 ? void 0 : _errorData_message1.includes('expired'))) {\n                console.warn(\"Token expired, redirecting to login\");\n                handleUnauthorized();\n                throw new Error(\"Token expired\");\n            }\n        }\n        return response;\n    } catch (error) {\n        // Si erreur réseau ou autre, vérifier si c'est lié à l'auth\n        if (error instanceof TypeError && error.message.includes('fetch')) {\n            console.error(\"Network error during authenticated request:\", error);\n        }\n        throw error;\n    }\n};\n// Wrapper pour les requêtes GET\nconst authenticatedGet = async (url)=>{\n    return authenticatedFetch(url, {\n        method: 'GET'\n    });\n};\n// Wrapper pour les requêtes POST\nconst authenticatedPost = async (url, data)=>{\n    return authenticatedFetch(url, {\n        method: 'POST',\n        body: JSON.stringify(data)\n    });\n};\n// Wrapper pour les requêtes PUT\nconst authenticatedPut = async (url, data)=>{\n    return authenticatedFetch(url, {\n        method: 'PUT',\n        body: JSON.stringify(data)\n    });\n};\n// Wrapper pour les requêtes DELETE\nconst authenticatedDelete = async (url, data)=>{\n    const options = {\n        method: 'DELETE'\n    };\n    if (data) {\n        options.body = JSON.stringify(data);\n    }\n    return authenticatedFetch(url, options);\n};\n// Fonction pour vérifier si l'utilisateur est toujours authentifié\nconst checkAuthStatus = async ()=>{\n    const token = getAuthToken();\n    if (!token) {\n        return false;\n    }\n    try {\n        // Faire une requête simple pour vérifier le token\n        const response = await authenticatedGet('/user/check-auth');\n        return response.ok;\n    } catch (error) {\n        console.error(\"Auth check failed:\", error);\n        return false;\n    }\n};\n// Fonction pour décoder et vérifier l'expiration du token JWT\nconst isTokenExpired = ()=>{\n    const token = getAuthToken();\n    if (!token) {\n        return true;\n    }\n    try {\n        // Décoder le token JWT (partie payload)\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const currentTime = Math.floor(Date.now() / 1000);\n        // Vérifier si le token est expiré\n        return payload.exp < currentTime;\n    } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        return true;\n    }\n};\n// Fonction pour nettoyer la session\nconst clearSession = ()=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"idToken\");\n    if (true) {\n        localStorage.clear();\n        sessionStorage.clear();\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/utils/httpInterceptor.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/ThemeInitializer.tsx":
/*!****************************************!*\
  !*** ./src/utils/ThemeInitializer.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeInitializer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\nfunction ThemeInitializer() {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ThemeInitializer.useEffect\": ()=>{\n            if (localStorage.getItem(\"theme\") === \"dark\") {\n                document.documentElement.classList.add(\"dark\");\n            } else {\n                document.documentElement.classList.remove(\"dark\");\n            }\n        }\n    }[\"ThemeInitializer.useEffect\"], []);\n    return null; // This component only runs once to apply theme\n}\n_s(ThemeInitializer, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = ThemeInitializer;\nvar _c;\n$RefreshReg$(_c, \"ThemeInitializer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy91dGlscy9UaGVtZUluaXRpYWxpemVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQ2tDO0FBRW5CLFNBQVNDOztJQUN0QkQsZ0RBQVNBO3NDQUFDO1lBQ1IsSUFBSUUsYUFBYUMsT0FBTyxDQUFDLGFBQWEsUUFBUTtnQkFDNUNDLFNBQVNDLGVBQWUsQ0FBQ0MsU0FBUyxDQUFDQyxHQUFHLENBQUM7WUFDekMsT0FBTztnQkFDTEgsU0FBU0MsZUFBZSxDQUFDQyxTQUFTLENBQUNFLE1BQU0sQ0FBQztZQUM1QztRQUNGO3FDQUFHLEVBQUU7SUFFTCxPQUFPLE1BQU0sK0NBQStDO0FBQzlEO0dBVndCUDtLQUFBQSIsInNvdXJjZXMiOlsiRDpcXGVseXNlZVxcUHJvamV0XFxQZXJzXFxzY2hvbGFyaWZ5XFxkYXNoYm9hcmRcXHNyY1xcdXRpbHNcXFRoZW1lSW5pdGlhbGl6ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRoZW1lSW5pdGlhbGl6ZXIoKSB7XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcInRoZW1lXCIpID09PSBcImRhcmtcIikge1xyXG4gICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LmFkZChcImRhcmtcIik7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LnJlbW92ZShcImRhcmtcIik7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICByZXR1cm4gbnVsbDsgLy8gVGhpcyBjb21wb25lbnQgb25seSBydW5zIG9uY2UgdG8gYXBwbHkgdGhlbWVcclxufVxyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiVGhlbWVJbml0aWFsaXplciIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsImNsYXNzTGlzdCIsImFkZCIsInJlbW92ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/ThemeInitializer.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Bricolage_Grotesque%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22bricolage%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Capp%5C%5Cservices%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Celysee%5C%5CProjet%5C%5CPers%5C%5Cscholarify%5C%5Cdashboard%5C%5Csrc%5C%5Cutils%5C%5CThemeInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);