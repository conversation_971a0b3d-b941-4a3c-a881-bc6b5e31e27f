const Grade = require('../models/Grade'); // Assuming you have a Grade model
const { ensureUniqueId } = require('../utils/generateId'); 
const mongoose = require('mongoose');

const testGradeResponse = (req, res) => {
  res.status(200).json({ message: 'Hi, this is grade' });
};

// // Get all grade records
const getAllGrades = async (req, res) => {
  try {
    const grades = await Grade.find();
    res.json(grades);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// // Create a new grade record
const createGrade = async (req, res) => {
  try {
    // If user is a teacher, verify they can create grades for this class/subject
    if (req.user.role === 'teacher') {
      const { hasPermission } = require('../middleware/teacherMiddleware');

      // Check if teacher has permission to enter/edit grades
      if (!hasPermission(req, 'academic_records', 'enter_edit_grades_assigned_classes')) {
        return res.status(403).json({ message: 'You do not have permission to create grades' });
      }

      // Check if teacher is assigned to the class
      if (!req.teacher || !req.teacher.assigned_class_ids.includes(req.body.class_id)) {
        return res.status(403).json({ message: 'You are not authorized to create grades for this class' });
      }

      // Check if teacher is assigned to the subject (subjects are stored as strings)
      if (!req.teacher.assigned_subjects.includes(req.body.subject)) {
        return res.status(403).json({ message: 'You are not authorized to create grades for this subject' });
      }
    }

    const newGrade = new Grade(req.body);
    await newGrade.save();
    res.status(201).json({ message: 'Grade created successfully', grade: newGrade });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// // Get a grade record by ID
const getGradeById = async (req, res) => {
  const _id = new mongoose.Types.ObjectId(req.params.id);
  try {
    const grade = await Grade.findById(_id);
    if (!grade) {
      return res.status(404).json({ message: 'Grade record not found' });
    }
    res.json(grade);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// // Update grade record by ID
const updateGradeById = async (req, res) => {
  try {
    // First, get the existing grade to check permissions
    const existingGrade = await Grade.findById(req.params.id);
    if (!existingGrade) {
      return res.status(404).json({ message: 'Grade record not found' });
    }

    // If user is a teacher, verify they can update this grade
    if (req.user.role === 'teacher') {
      const { hasPermission } = require('../middleware/teacherMiddleware');

      // Check if teacher has permission to enter/edit grades
      if (!hasPermission(req, 'academic_records', 'enter_edit_grades_assigned_classes')) {
        return res.status(403).json({ message: 'You do not have permission to update grades' });
      }

      // Check if teacher is assigned to the class
      if (!req.teacher || !req.teacher.assigned_class_ids.includes(existingGrade.class_id.toString())) {
        return res.status(403).json({ message: 'You are not authorized to update grades for this class' });
      }

      // Note: Subject verification would need the actual subject name from the grade record
      // This might require populating the subject or storing subject name in the grade
    }

    const updatedGrade = await Grade.findByIdAndUpdate(req.params.id, req.body, { new: true });
    res.json({ message: 'Grade updated successfully', grade: updatedGrade });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// // Delete grade record by ID
const deleteGradeById = async (req, res) => {
  try {
    // First, get the existing grade to check permissions
    const existingGrade = await Grade.findById(req.params.id);
    if (!existingGrade) {
      return res.status(404).json({ message: 'Grade record not found' });
    }

    // If user is a teacher, verify they can delete this grade
    if (req.user.role === 'teacher') {
      const { hasPermission } = require('../middleware/teacherMiddleware');

      // Check if teacher has permission to enter/edit grades (includes delete)
      if (!hasPermission(req, 'academic_records', 'enter_edit_grades_assigned_classes')) {
        return res.status(403).json({ message: 'You do not have permission to delete grades' });
      }

      // Check if teacher is assigned to the class
      if (!req.teacher || !req.teacher.assigned_class_ids.includes(existingGrade.class_id.toString())) {
        return res.status(403).json({ message: 'You are not authorized to delete grades for this class' });
      }

      // Note: Subject verification would need the actual subject name from the grade record
    }

    await Grade.findByIdAndDelete(req.params.id);
    res.json({ message: 'Grade record deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};


// Delete multiple grade records by IDs
const deleteMultipleGrades = async (req, res) => {
  const { ids } = req.body; // Expecting an array of grade IDs in the request body
  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ message: 'Invalid input: ids should be a non-empty array' });
  }

  try {
    // Delete grade records where _id is in the provided array of IDs
    const result = await Grade.deleteMany({ _id: { $in: ids } });
    
    if (result.deletedCount === 0) {
      return res.status(404).json({ message: 'No grade records found for the provided IDs' });
    }
    
    res.json({ message: `${result.deletedCount} grade records deleted successfully` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
const getGradeRecords = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      class_id,
      subject_id,
      term,
      exam_type_id,
      academic_year,
      student_id,
      page = 1,
      limit = 50
    } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Convert school_id to ObjectId for proper matching
    const mongoose = require('mongoose');
    const schoolObjectId = new mongoose.Types.ObjectId(school_id);

    // Build filter object for basic filters
    const filter = { school_id: schoolObjectId };

    // Apply teacher-specific filters using middleware helper
    const { applyTeacherFilters } = require('../middleware/teacherMiddleware');
    applyTeacherFilters(filter, req);

    if (subject_id) filter.subject_id = new mongoose.Types.ObjectId(subject_id);
    if (term) filter.term = term;
    if (exam_type_id) filter.exam_type = new mongoose.Types.ObjectId(exam_type_id);
    if (academic_year) filter.academic_year = academic_year;
    if (student_id) filter.student_id = new mongoose.Types.ObjectId(student_id);

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build aggregation pipeline for class filtering
    let pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'students',
          localField: 'student_id',
          foreignField: '_id',
          as: 'student'
        }
      },
      { $unwind: '$student' },
      {
        $lookup: {
          from: 'classes',
          localField: 'student.class_id',
          foreignField: '_id',
          as: 'class'
        }
      },
      { $unwind: { path: '$class', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'subjects',
          localField: 'subject_id',
          foreignField: '_id',
          as: 'subject'
        }
      },
      { $unwind: { path: '$subject', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'examtypes',
          localField: 'exam_type',
          foreignField: '_id',
          as: 'examType'
        }
      },
      { $unwind: { path: '$examType', preserveNullAndEmptyArrays: true } }
    ];

    // Add class filter if provided
    if (class_id) {
      pipeline.push({
        $match: {
          'student.class_id': new mongoose.Types.ObjectId(class_id)
        }
      });
    }

    // Add sorting and pagination
    pipeline.push(
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: parseInt(limit) }
    );

    // Get grade records with aggregation
    const gradeRecords = await Grade.aggregate(pipeline);

    // Get total count for pagination (separate aggregation without pagination)
    let countPipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'students',
          localField: 'student_id',
          foreignField: '_id',
          as: 'student'
        }
      },
      { $unwind: '$student' }
    ];

    if (class_id) {
      countPipeline.push({
        $match: {
          'student.class_id': new mongoose.Types.ObjectId(class_id)
        }
      });
    }

    countPipeline.push({ $count: 'total' });
    const countResult = await Grade.aggregate(countPipeline);
    const totalRecords = countResult.length > 0 ? countResult[0].total : 0;

    // Format the response
    const formattedRecords = gradeRecords.map(record => ({
      _id: record._id,
      student_name: record.student ?
        `${record.student.first_name} ${record.student.last_name}` : 'Unknown Student',
      student_id: record.student?.student_id || 'N/A',
      class_name: record.class?.name || 'Unknown Class',
      subject_name: record.subject?.name || 'Unknown Subject',
      exam_type: record.examType?.type || 'Unknown Exam Type',
      term: record.term,
      academic_year: record.academic_year,
      score: record.score,
      grade: record.grade,
      comments: record.comments,
      date_entered: record.createdAt
    }));

    console.log('Grade records found:', formattedRecords.length);

    res.status(200).json({
      grade_records: formattedRecords,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(totalRecords / parseInt(limit)),
        total_records: totalRecords,
        per_page: parseInt(limit)
      },
      message: 'Grade records retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching grade records:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get grade statistics for a school
const getGradeStats = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { term, academic_year, class_id, subject_id } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Convert school_id to ObjectId for proper matching
    const mongoose = require('mongoose');
    const schoolObjectId = new mongoose.Types.ObjectId(school_id);

    // Build aggregation pipeline
    let pipeline = [
      {
        $match: {
          school_id: schoolObjectId
        }
      }
    ];

    // Add lookup for student to get class information if class_id filter is needed
    if (class_id) {
      pipeline.push({
        $lookup: {
          from: 'students',
          localField: 'student_id',
          foreignField: '_id',
          as: 'student'
        }
      });
      pipeline.push({
        $match: {
          'student.class_id': new mongoose.Types.ObjectId(class_id)
        }
      });
    }

    // Add additional filters
    const additionalFilters = {};
    if (term) additionalFilters.term = term;
    if (academic_year) additionalFilters.academic_year = academic_year;
    if (subject_id) additionalFilters.subject_id = new mongoose.Types.ObjectId(subject_id);

    if (Object.keys(additionalFilters).length > 0) {
      pipeline.push({
        $match: additionalFilters
      });
    }

    // Apply teacher-specific filters using middleware helper
    const { applyTeacherFilters } = require('../middleware/teacherMiddleware');
    const teacherFilter = {};
    applyTeacherFilters(teacherFilter, req);

    if (Object.keys(teacherFilter).length > 0) {
      pipeline.push({
        $match: teacherFilter
      });
    }

    // Add grouping stage for statistics
    pipeline.push({
      $group: {
        _id: null,
        totalGrades: { $sum: 1 },
        averageScore: { $avg: '$score' },
        highestScore: { $max: '$score' },
        lowestScore: { $min: '$score' },
        passCount: {
          $sum: {
            $cond: [{ $gte: ['$score', 60] }, 1, 0]
          }
        }
      }
    });

    console.log('Grade stats aggregation pipeline:', JSON.stringify(pipeline, null, 2));

    // Get grade statistics
    const gradeStats = await Grade.aggregate(pipeline);

    const stats = gradeStats.length > 0 ? gradeStats[0] : {
      totalGrades: 0,
      averageScore: 0,
      highestScore: 0,
      lowestScore: 0,
      passCount: 0
    };

    // Calculate pass rate
    const passRate = stats.totalGrades > 0 ?
      Math.round((stats.passCount / stats.totalGrades) * 100) : 0;

    // Round average score
    stats.averageScore = Math.round(stats.averageScore || 0);

    console.log('Grade stats result:', stats);

    res.status(200).json({
      stats: {
        totalGrades: stats.totalGrades,
        averageScore: stats.averageScore,
        highestScore: stats.highestScore || 0,
        lowestScore: stats.lowestScore || 0,
        passRate: passRate
      },
      message: 'Grade statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching grade stats:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Export grades to PDF
const exportGradesPDF = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      class_id,
      subject_id,
      term,
      exam_type_id,
      academic_year,
      student_id
    } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Convert school_id to ObjectId for proper matching
    const mongoose = require('mongoose');
    const schoolObjectId = new mongoose.Types.ObjectId(school_id);

    // Build filter object for basic filters
    const filter = { school_id: schoolObjectId };

    // Apply teacher-specific filters using middleware helper
    const { applyTeacherFilters } = require('../middleware/teacherMiddleware');
    applyTeacherFilters(filter, req);

    if (subject_id) filter.subject_id = new mongoose.Types.ObjectId(subject_id);
    if (term) filter.term = term;
    if (exam_type_id) filter.exam_type = new mongoose.Types.ObjectId(exam_type_id);
    if (academic_year) filter.academic_year = academic_year;
    if (student_id) filter.student_id = new mongoose.Types.ObjectId(student_id);

    // Build aggregation pipeline for class filtering
    let pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'students',
          localField: 'student_id',
          foreignField: '_id',
          as: 'student'
        }
      },
      { $unwind: '$student' },
      {
        $lookup: {
          from: 'classes',
          localField: 'student.class_id',
          foreignField: '_id',
          as: 'class'
        }
      },
      { $unwind: { path: '$class', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'subjects',
          localField: 'subject_id',
          foreignField: '_id',
          as: 'subject'
        }
      },
      { $unwind: { path: '$subject', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'examtypes',
          localField: 'exam_type',
          foreignField: '_id',
          as: 'examType'
        }
      },
      { $unwind: { path: '$examType', preserveNullAndEmptyArrays: true } }
    ];

    // Add class filter if provided
    if (class_id) {
      pipeline.push({
        $match: {
          'student.class_id': new mongoose.Types.ObjectId(class_id)
        }
      });
    }

    // Add sorting
    pipeline.push({ $sort: { createdAt: -1 } });

    // Get grade records with aggregation
    const gradeRecords = await Grade.aggregate(pipeline);

    // Format the response
    const formattedRecords = gradeRecords.map(record => ({
      student_name: record.student ?
        `${record.student.first_name} ${record.student.last_name}` : 'Unknown Student',
      student_id: record.student?.student_id || 'N/A',
      class_name: record.class?.name || 'Unknown Class',
      subject_name: record.subject?.name || 'Unknown Subject',
      exam_type: record.examType?.type || 'Unknown Exam Type',
      term: record.term,
      academic_year: record.academic_year,
      score: (record.score * 20 / 100).toFixed(1), // Convert to /20
      grade: record.grade,
      comments: record.comments,
      date_entered: record.createdAt
    }));

    // Create PDF using simple HTML to PDF approach
    const PDFDocument = require('pdfkit');
    const doc = new PDFDocument();

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename=grades-export.pdf');

    // Pipe the PDF to response
    doc.pipe(res);

    // Add content to PDF
    doc.fontSize(20).text('Grades Report', 50, 50);
    doc.fontSize(12).text(`Generated on: ${new Date().toLocaleDateString()}`, 50, 80);

    let yPosition = 120;

    // Add headers
    doc.text('Student', 50, yPosition);
    doc.text('Class', 150, yPosition);
    doc.text('Subject', 220, yPosition);
    doc.text('Score', 320, yPosition);
    doc.text('Grade', 370, yPosition);
    doc.text('Term', 420, yPosition);

    yPosition += 20;

    // Add data
    formattedRecords.forEach(record => {
      if (yPosition > 750) {
        doc.addPage();
        yPosition = 50;
      }

      doc.text(record.student_name.substring(0, 15), 50, yPosition);
      doc.text(record.class_name.substring(0, 10), 150, yPosition);
      doc.text(record.subject_name.substring(0, 12), 220, yPosition);
      doc.text(`${record.score}/20`, 320, yPosition);
      doc.text(record.grade.substring(0, 10), 370, yPosition);
      doc.text(record.term, 420, yPosition);

      yPosition += 15;
    });

    doc.end();
  } catch (error) {
    console.error('Error exporting grades to PDF:', error);
    res.status(500).json({ message: 'Internal server error' , error:error});
  }
};

// Export grades to Excel
const exportGradesExcel = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      class_id,
      subject_id,
      term,
      exam_type_id,
      academic_year,
      student_id
    } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Convert school_id to ObjectId for proper matching
    const mongoose = require('mongoose');
    const schoolObjectId = new mongoose.Types.ObjectId(school_id);

    // Build filter object for basic filters
    const filter = { school_id: schoolObjectId };

    // Apply teacher-specific filters using middleware helper
    const { applyTeacherFilters } = require('../middleware/teacherMiddleware');
    applyTeacherFilters(filter, req);

    if (subject_id) filter.subject_id = new mongoose.Types.ObjectId(subject_id);
    if (term) filter.term = term;
    if (exam_type_id) filter.exam_type = new mongoose.Types.ObjectId(exam_type_id);
    if (academic_year) filter.academic_year = academic_year;
    if (student_id) filter.student_id = new mongoose.Types.ObjectId(student_id);

    // Build aggregation pipeline for class filtering
    let pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'students',
          localField: 'student_id',
          foreignField: '_id',
          as: 'student'
        }
      },
      { $unwind: '$student' },
      {
        $lookup: {
          from: 'classes',
          localField: 'student.class_id',
          foreignField: '_id',
          as: 'class'
        }
      },
      { $unwind: { path: '$class', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'subjects',
          localField: 'subject_id',
          foreignField: '_id',
          as: 'subject'
        }
      },
      { $unwind: { path: '$subject', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'examtypes',
          localField: 'exam_type',
          foreignField: '_id',
          as: 'examType'
        }
      },
      { $unwind: { path: '$examType', preserveNullAndEmptyArrays: true } }
    ];

    // Add class filter if provided
    if (class_id) {
      pipeline.push({
        $match: {
          'student.class_id': new mongoose.Types.ObjectId(class_id)
        }
      });
    }

    // Add sorting
    pipeline.push({ $sort: { createdAt: -1 } });

    // Get grade records with aggregation
    const gradeRecords = await Grade.aggregate(pipeline);

    // Format the response
    const formattedRecords = gradeRecords.map(record => ({
      'Student Name': record.student ?
        `${record.student.first_name} ${record.student.last_name}` : 'Unknown Student',
      'Student ID': record.student?.student_id || 'N/A',
      'Class': record.class?.name || 'Unknown Class',
      'Subject': record.subject?.name || 'Unknown Subject',
      'Exam Type': record.examType?.type || 'Unknown Exam Type',
      'Term': record.term,
      'Academic Year': record.academic_year,
      'Score (/20)': (record.score * 20 / 100).toFixed(1),
      'Grade': record.grade,
      'Comments': record.comments || '',
      'Date Entered': record.createdAt ? new Date(record.createdAt).toLocaleDateString() : ''
    }));

    // Create Excel file using xlsx
    const XLSX = require('xlsx');
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(formattedRecords);

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Grades');

    // Generate buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Set response headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=grades-export.xlsx');

    res.send(buffer);
  } catch (error) {
    console.error('Error exporting grades to Excel:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  testGradeResponse,
  getAllGrades,
  createGrade,
  getGradeById,
  updateGradeById,
  deleteGradeById,
  deleteMultipleGrades,
  getGradeRecords,
  getGradeStats,
  exportGradesPDF,
  exportGradesExcel
};
