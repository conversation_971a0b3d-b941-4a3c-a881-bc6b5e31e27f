import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";

export interface TeacherPermissions {
  students: {
    view_all_students: boolean;
    add_edit_delete_students: boolean;
    generate_id_cards: boolean;
    generate_report_cards: boolean;
  };
  academic_records: {
    view_grades_assigned_classes: boolean;
    enter_edit_grades_assigned_classes: boolean;
    view_all_school_grades: boolean;
    take_attendance_assigned_classes: boolean;
    view_all_attendance: boolean;
  };
  financials: {
    view_student_fee_balances: boolean;
    record_fee_payments: boolean;
    manage_school_credit_balance: boolean;
    view_financial_reports: boolean;
  };
  staff: {
    view_staff_list: boolean;
    add_edit_delete_staff: boolean;
    manage_staff_permissions: boolean;
    reset_staff_passwords: boolean;
  };
  classes: {
    view_all_classes: boolean;
    add_edit_delete_classes: boolean;
    manage_class_schedules: boolean;
    assign_teachers_to_classes: boolean;
  };
  announcements: {
    view_announcements: boolean;
    create_edit_announcements: boolean;
    delete_announcements: boolean;
    publish_announcements: boolean;
  };
  resources: {
    view_resources: boolean;
    add_edit_delete_resources: boolean;
    manage_resource_categories: boolean;
  };
  reports: {
    generate_student_reports: boolean;
    generate_financial_reports: boolean;
    generate_attendance_reports: boolean;
    export_data: boolean;
  };
}

export interface TeacherAssignmentWithPermissions {
  _id: string;
  teacher_id: string;
  school_id: string;
  assigned_classes: Array<{
    _id: string;
    name: string;
    level: string;
  }>;
  assigned_subjects: string[];
  permissions: TeacherPermissions;
  role_template: string;
}

// Get teacher's permissions and assignments for a specific school
export async function getTeacherPermissions(schoolId: string): Promise<TeacherAssignmentWithPermissions> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/teacher/assignments/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching teacher permissions:", response.statusText);
      throw new Error("Failed to fetch teacher permissions");
    }

    const data = await response.json();
    return data as TeacherAssignmentWithPermissions;
  } catch (error) {
    console.error("Fetch teacher permissions error:", error);
    throw new Error("Failed to fetch teacher permissions");
  }
}

// Check if teacher has a specific permission
export function hasPermission(
  permissions: TeacherPermissions,
  module: keyof TeacherPermissions,
  permission: string
): boolean {
  return permissions[module] && (permissions[module] as any)[permission] === true;
}

// Get navigation items based on teacher permissions
export function getTeacherNavigationItems(permissions: TeacherPermissions) {
  const navigationGroups = [];

  // My Classes group - always visible for teachers
  const myClassesGroup = {
    title: "My Classes",
    icon: "BookOpen",
    items: []
  };

  if (hasPermission(permissions, 'classes', 'view_all_classes')) {
    myClassesGroup.items.push({ icon: "BookOpen", name: "Classes", href: "/teacher-dashboard/classes" });
  }

  if (hasPermission(permissions, 'students', 'view_all_students')) {
    myClassesGroup.items.push({ icon: "GraduationCap", name: "Students", href: "/teacher-dashboard/students" });
  }

  if (hasPermission(permissions, 'classes', 'manage_class_schedules')) {
    myClassesGroup.items.push({ icon: "Clock4", name: "Schedule", href: "/teacher-dashboard/timetable" });
  }

  if (myClassesGroup.items.length > 0) {
    navigationGroups.push(myClassesGroup);
  }

  // Academic Tasks group
  const academicTasksGroup = {
    title: "Academic Tasks",
    icon: "ClipboardList",
    items: []
  };

  if (hasPermission(permissions, 'academic_records', 'take_attendance_assigned_classes')) {
    academicTasksGroup.items.push({ icon: "FileCheck2", name: "Attendance", href: "/teacher-dashboard/attendance" });
  }

  if (hasPermission(permissions, 'academic_records', 'view_grades_assigned_classes')) {
    academicTasksGroup.items.push({ icon: "Percent", name: "Grades", href: "/teacher-dashboard/grades" });
  }

  if (academicTasksGroup.items.length > 0) {
    navigationGroups.push(academicTasksGroup);
  }

  // Resources group
  const resourcesGroup = {
    title: "Resources",
    icon: "Megaphone",
    items: []
  };

  if (hasPermission(permissions, 'resources', 'view_resources')) {
    resourcesGroup.items.push({ icon: "BookOpen", name: "Teaching Materials", href: "/teacher-dashboard/resources" });
  }

  if (hasPermission(permissions, 'announcements', 'view_announcements')) {
    resourcesGroup.items.push({ icon: "Megaphone", name: "Announcements", href: "/teacher-dashboard/announcements" });
  }

  if (resourcesGroup.items.length > 0) {
    navigationGroups.push(resourcesGroup);
  }

  // Financial group (if teacher has financial permissions)
  const financialGroup = {
    title: "Financial",
    icon: "DollarSign",
    items: []
  };

  if (hasPermission(permissions, 'financials', 'view_student_fee_balances')) {
    financialGroup.items.push({ icon: "CreditCard", name: "Fee Balances", href: "/teacher-dashboard/fees" });
  }

  if (hasPermission(permissions, 'financials', 'record_fee_payments')) {
    financialGroup.items.push({ icon: "Receipt", name: "Fee Payments", href: "/teacher-dashboard/fee-payments" });
  }

  if (financialGroup.items.length > 0) {
    navigationGroups.push(financialGroup);
  }

  // Reports group
  const reportsGroup = {
    title: "Reports",
    icon: "FileText",
    items: []
  };

  if (hasPermission(permissions, 'reports', 'generate_student_reports')) {
    reportsGroup.items.push({ icon: "FileText", name: "Student Reports", href: "/teacher-dashboard/reports/students" });
  }

  if (hasPermission(permissions, 'reports', 'generate_attendance_reports')) {
    reportsGroup.items.push({ icon: "Calendar", name: "Attendance Reports", href: "/teacher-dashboard/reports/attendance" });
  }

  if (reportsGroup.items.length > 0) {
    navigationGroups.push(reportsGroup);
  }

  return navigationGroups;
}

// Get available actions for a specific module
export function getModuleActions(permissions: TeacherPermissions, module: keyof TeacherPermissions) {
  const modulePermissions = permissions[module];
  const actions = [];

  // Convert permissions to action capabilities
  Object.entries(modulePermissions).forEach(([permission, hasAccess]) => {
    if (hasAccess) {
      actions.push(permission);
    }
  });

  return actions;
}

// Get students in teacher's assigned classes
export async function getTeacherStudents(schoolId: string): Promise<any[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/teacher/students/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching teacher students:", response.statusText);
      throw new Error("Failed to fetch teacher students");
    }

    const data = await response.json();
    return data.students || [];
  } catch (error) {
    console.error("Fetch teacher students error:", error);
    throw new Error("Failed to fetch teacher students");
  }
}
