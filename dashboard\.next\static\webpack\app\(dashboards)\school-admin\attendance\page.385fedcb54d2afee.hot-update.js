"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/attendance/page",{

/***/ "(app-pages-browser)/./src/components/modals/AttendanceModal.tsx":
/*!***************************************************!*\
  !*** ./src/components/modals/AttendanceModal.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttendanceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AttendanceModal(param) {\n    let { isOpen, onClose, onSubmit, attendance, students, classes, subjects, schedules, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id: \"\",\n        schedule_id: \"\",\n        status: \"Present\",\n        date: new Date().toISOString().split('T')[0],\n        academic_year: \"2024-2025\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredStudents, setFilteredStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Multi-selection states\n    const [isMultiMode, setIsMultiMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedStudents, setSelectedStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [studentStatuses, setStudentStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [bulkStatus, setBulkStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Present\");\n    // Search states\n    const [scheduleSearch, setScheduleSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [studentSearch, setStudentSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showScheduleDropdown, setShowScheduleDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStudentDropdown, setShowStudentDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isEditing = !!attendance;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (attendance) {\n                    // Single edit mode\n                    setIsMultiMode(false);\n                    setFormData({\n                        student_id: attendance.student_id || \"\",\n                        schedule_id: attendance.schedule_id || \"\",\n                        status: attendance.status || \"Present\",\n                        date: attendance.date ? new Date(attendance.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n                        academic_year: attendance.academic_year || \"2024-2025\"\n                    });\n                } else {\n                    // New attendance - default to multi mode\n                    setIsMultiMode(true);\n                    setFormData({\n                        student_id: \"\",\n                        schedule_id: \"\",\n                        status: \"Present\",\n                        date: new Date().toISOString().split('T')[0],\n                        academic_year: \"2024-2025\"\n                    });\n                }\n                setErrors({});\n                setSelectedStudents(new Set());\n                setStudentStatuses({});\n                setBulkStatus(\"Present\");\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        isOpen,\n        attendance\n    ]);\n    // Filter students based on selected schedule\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (formData.schedule_id) {\n                const selectedSchedule = schedules.find({\n                    \"AttendanceModal.useEffect.selectedSchedule\": (s)=>s._id === formData.schedule_id\n                }[\"AttendanceModal.useEffect.selectedSchedule\"]);\n                if (selectedSchedule) {\n                    const classStudents = students.filter({\n                        \"AttendanceModal.useEffect.classStudents\": (student)=>student.class_id === selectedSchedule.class_id\n                    }[\"AttendanceModal.useEffect.classStudents\"]);\n                    setFilteredStudents(classStudents);\n                    // Initialize student statuses for multi-mode\n                    if (isMultiMode) {\n                        const initialStatuses = {};\n                        classStudents.forEach({\n                            \"AttendanceModal.useEffect\": (student)=>{\n                                initialStatuses[student._id] = \"Present\";\n                            }\n                        }[\"AttendanceModal.useEffect\"]);\n                        setStudentStatuses(initialStatuses);\n                    }\n                }\n            } else {\n                setFilteredStudents(students);\n                if (isMultiMode) {\n                    const initialStatuses = {};\n                    students.forEach({\n                        \"AttendanceModal.useEffect\": (student)=>{\n                            initialStatuses[student._id] = \"Present\";\n                        }\n                    }[\"AttendanceModal.useEffect\"]);\n                    setStudentStatuses(initialStatuses);\n                }\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        formData.schedule_id,\n        schedules,\n        students,\n        isMultiMode\n    ]);\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"AttendanceModal.useEffect.handleClickOutside\": ()=>{\n                    setShowScheduleDropdown(false);\n                    setShowStudentDropdown(false);\n                }\n            }[\"AttendanceModal.useEffect.handleClickOutside\"];\n            if (showScheduleDropdown || showStudentDropdown) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"AttendanceModal.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"AttendanceModal.useEffect\"];\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        showScheduleDropdown,\n        showStudentDropdown\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!isMultiMode && !formData.student_id) {\n            newErrors.student_id = \"Student is required\";\n        }\n        if (isMultiMode && selectedStudents.size === 0) {\n            newErrors.students = \"At least one student must be selected\";\n        }\n        if (!formData.schedule_id) {\n            newErrors.schedule_id = \"Class schedule is required\";\n        }\n        if (!formData.date) {\n            newErrors.date = \"Date is required\";\n        }\n        if (!formData.academic_year) {\n            newErrors.academic_year = \"Academic year is required\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        console.log('handleSubmit called', {\n            isMultiMode,\n            selectedStudents: selectedStudents.size,\n            formData\n        }); // Debug log\n        if (!validateForm()) {\n            console.log('Validation failed'); // Debug log\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (isMultiMode) {\n                console.log('Multi-mode submission', {\n                    selectedStudents: Array.from(selectedStudents),\n                    studentStatuses\n                }); // Debug log\n                // Submit multiple attendance records\n                const attendancePromises = Array.from(selectedStudents).map((studentId)=>{\n                    const studentStatus = studentStatuses[studentId] || \"Present\";\n                    const submissionData = {\n                        ...formData,\n                        student_id: studentId,\n                        status: studentStatus\n                    };\n                    console.log('Submitting for student:', submissionData); // Debug log\n                    return onSubmit(submissionData);\n                });\n                await Promise.all(attendancePromises);\n                console.log('Multi-mode submission completed'); // Debug log\n            } else {\n                console.log('Single-mode submission', formData); // Debug log\n                // Submit single attendance record\n                await onSubmit(formData);\n                console.log('Single-mode submission completed'); // Debug log\n            }\n            onClose();\n        } catch (error) {\n            console.error(\"Error submitting attendance:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    // Multi-selection functions\n    const toggleStudentSelection = (studentId)=>{\n        const newSelected = new Set(selectedStudents);\n        if (newSelected.has(studentId)) {\n            newSelected.delete(studentId);\n        } else {\n            newSelected.add(studentId);\n        }\n        setSelectedStudents(newSelected);\n    };\n    const selectAllStudents = ()=>{\n        console.log('selectAllStudents called, filteredStudents:', filteredStudents); // Debug log\n        if (filteredStudents && filteredStudents.length > 0) {\n            const allStudentIds = new Set(filteredStudents.map((s)=>s._id));\n            console.log('Setting selected students:', allStudentIds); // Debug log\n            setSelectedStudents(allStudentIds);\n            // Initialize statuses for all selected students\n            const newStatuses = {\n                ...studentStatuses\n            };\n            filteredStudents.forEach((student)=>{\n                if (!newStatuses[student._id]) {\n                    newStatuses[student._id] = \"Present\";\n                }\n            });\n            setStudentStatuses(newStatuses);\n        } else {\n            console.log('No filtered students available'); // Debug log\n        }\n    };\n    const deselectAllStudents = ()=>{\n        setSelectedStudents(new Set());\n    };\n    const applyBulkStatus = ()=>{\n        const newStatuses = {\n            ...studentStatuses\n        };\n        selectedStudents.forEach((studentId)=>{\n            newStatuses[studentId] = bulkStatus;\n        });\n        setStudentStatuses(newStatuses);\n    };\n    const updateStudentStatus = (studentId, status)=>{\n        setStudentStatuses((prev)=>({\n                ...prev,\n                [studentId]: status\n            }));\n    };\n    const getScheduleDisplay = (schedule)=>{\n        const subject = subjects.find((s)=>s._id === schedule.subject_id);\n        const classInfo = classes.find((c)=>c._id === schedule.class_id);\n        return \"\".concat((subject === null || subject === void 0 ? void 0 : subject.name) || 'Unknown Subject', \" - \").concat((classInfo === null || classInfo === void 0 ? void 0 : classInfo.name) || 'Unknown Class', \" (\").concat(schedule.day_of_week, \")\");\n    };\n    // Helper functions for search and selection\n    const getSelectedScheduleName = ()=>{\n        const selectedSchedule = schedules.find((s)=>s._id === formData.schedule_id);\n        return selectedSchedule ? getScheduleDisplay(selectedSchedule) : '';\n    };\n    const getSelectedStudentName = ()=>{\n        const selectedStudent = filteredStudents.find((s)=>s._id === formData.student_id);\n        return selectedStudent ? \"\".concat(selectedStudent.first_name, \" \").concat(selectedStudent.last_name, \" (\").concat(selectedStudent.student_id, \")\") : '';\n    };\n    const filteredSchedules = schedules.filter((schedule)=>getScheduleDisplay(schedule).toLowerCase().includes(scheduleSearch.toLowerCase()));\n    const filteredStudentsForSearch = filteredStudents.filter((student)=>\"\".concat(student.first_name, \" \").concat(student.last_name, \" \").concat(student.student_id).toLowerCase().includes(studentSearch.toLowerCase()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-black bg-opacity-50\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full mx-4 \".concat(isMultiMode ? 'max-w-4xl' : 'max-w-md'),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-lg flex items-center justify-center \".concat(isMultiMode ? 'bg-green-500' : 'bg-blue-500'),\n                                            children: isMultiMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 34\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 77\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                    children: isEditing ? \"Edit Attendance\" : isMultiMode ? \"Mark Multiple Attendance\" : \"Mark Attendance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: isEditing ? \"Update attendance record\" : isMultiMode ? \"Mark attendance for multiple students\" : \"Create new attendance record\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        !isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMultiMode(!isMultiMode),\n                                            className: \"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600\",\n                                            children: isMultiMode ? 'Single Mode' : 'Multi Mode'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"p-6 space-y-4 \".concat(isMultiMode ? 'max-h-96 overflow-y-auto' : ''),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: [\n                                                \"Class Schedule \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 34\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 \".concat(errors.schedule_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setShowScheduleDropdown(!showScheduleDropdown);\n                                                        setShowStudentDropdown(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900 dark:text-white\",\n                                                            children: getSelectedScheduleName() || \"Select class schedule\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showScheduleDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 border-b border-gray-200 dark:border-gray-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        placeholder: \"Search schedules...\",\n                                                                        value: scheduleSearch,\n                                                                        onChange: (e)=>setScheduleSearch(e.target.value),\n                                                                        className: \"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white text-sm\",\n                                                                        onClick: (e)=>e.stopPropagation()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-48 overflow-y-auto\",\n                                                            children: filteredSchedules.length > 0 ? filteredSchedules.map((schedule)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleInputChange(\"schedule_id\", schedule._id);\n                                                                        setShowScheduleDropdown(false);\n                                                                        setScheduleSearch('');\n                                                                    },\n                                                                    children: getScheduleDisplay(schedule)\n                                                                }, schedule._id, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 29\n                                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-3 py-2 text-gray-500 dark:text-gray-400 text-sm\",\n                                                                children: \"No schedules found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.schedule_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.schedule_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this),\n                                !isMultiMode ? // Single student selection with search\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: [\n                                                \"Student \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 29\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 \".concat(errors.student_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\", \" \").concat(!formData.schedule_id ? 'opacity-50 cursor-not-allowed' : ''),\n                                                    onClick: (e)=>{\n                                                        if (!formData.schedule_id) return;\n                                                        e.stopPropagation();\n                                                        setShowStudentDropdown(!showStudentDropdown);\n                                                        setShowScheduleDropdown(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900 dark:text-white\",\n                                                            children: getSelectedStudentName() || (formData.schedule_id ? \"Select student\" : \"Select class schedule first\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 21\n                                                }, this),\n                                                showStudentDropdown && formData.schedule_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 border-b border-gray-200 dark:border-gray-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        placeholder: \"Search students...\",\n                                                                        value: studentSearch,\n                                                                        onChange: (e)=>setStudentSearch(e.target.value),\n                                                                        className: \"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white text-sm\",\n                                                                        onClick: (e)=>e.stopPropagation()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-48 overflow-y-auto\",\n                                                            children: filteredStudentsForSearch.length > 0 ? filteredStudentsForSearch.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleInputChange(\"student_id\", student._id);\n                                                                        setShowStudentDropdown(false);\n                                                                        setStudentSearch('');\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium\",\n                                                                            children: [\n                                                                                student.first_name,\n                                                                                \" \",\n                                                                                student.last_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 458,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                            children: student.student_id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, student._id, true, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 31\n                                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-3 py-2 text-gray-500 dark:text-gray-400 text-sm\",\n                                                                children: \"No students found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 19\n                                        }, this),\n                                        errors.student_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.student_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 17\n                                }, this) : // Multi-student selection\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                    children: [\n                                                        \"Students (\",\n                                                        selectedStudents.size,\n                                                        \" selected)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: selectAllStudents,\n                                                            className: \"text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded\",\n                                                            disabled: !formData.schedule_id,\n                                                            children: \"Select All\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: deselectAllStudents,\n                                                            className: \"text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded\",\n                                                            children: \"Clear\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 19\n                                        }, this),\n                                        selectedStudents.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                        children: \"Apply to selected:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: bulkStatus,\n                                                        onChange: (e)=>setBulkStatus(e.target.value),\n                                                        className: \"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Present\",\n                                                                children: \"Present\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Absent\",\n                                                                children: \"Absent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Late\",\n                                                                children: \"Late\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Excused\",\n                                                                children: \"Excused\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: applyBulkStatus,\n                                                        className: \"px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600\",\n                                                        children: \"Apply\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md\",\n                                            children: filteredStudents.length > 0 ? filteredStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-600 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>toggleStudentSelection(student._id),\n                                                                    className: \"text-blue-500 hover:text-blue-600\",\n                                                                    children: selectedStudents.has(student._id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 544,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                            children: [\n                                                                                student.first_name,\n                                                                                \" \",\n                                                                                student.last_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 550,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                            children: student.student_id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 549,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        selectedStudents.has(student._id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: studentStatuses[student._id] || \"Present\",\n                                                            onChange: (e)=>updateStudentStatus(student._id, e.target.value),\n                                                            className: \"px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Present\",\n                                                                    children: \"Present\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Absent\",\n                                                                    children: \"Absent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Late\",\n                                                                    children: \"Late\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Excused\",\n                                                                    children: \"Excused\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, student._id, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 25\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center text-gray-500 dark:text-gray-400\",\n                                                children: formData.schedule_id ? \"No students found for this class\" : \"Please select a class schedule first\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 19\n                                        }, this),\n                                        errors.students && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.students\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 17\n                                }, this),\n                                !isMultiMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.status,\n                                            onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Present\",\n                                                    children: \"Present\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Absent\",\n                                                    children: \"Absent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Late\",\n                                                    children: \"Late\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Excused\",\n                                                    children: \"Excused\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Date\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.date,\n                                            onChange: (e)=>handleInputChange(\"date\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.date ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.date\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Academic Year\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.academic_year,\n                                            onChange: (e)=>handleInputChange(\"academic_year\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.academic_year ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            placeholder: \"e.g., 2024-2025\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.academic_year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.academic_year\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: onClose,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            disabled: isSubmitting,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                            children: [\n                                                isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isSubmitting ? \"Saving...\" : isEditing ? \"Update\" : isMultiMode ? \"Mark \".concat(selectedStudents.size, \" Students\") : \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n            lineNumber: 279,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n_s(AttendanceModal, \"ujAStAdb6hvNxzWAVhpwmsAnTao=\");\n_c = AttendanceModal;\nvar _c;\n$RefreshReg$(_c, \"AttendanceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/AttendanceModal.tsx\n"));

/***/ })

});