const express = require('express');
const attendanceController = require('../controllers/attendanceController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');

const router = express.Router();

// New routes for school-specific attendance
router.get('/school/:school_id', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher']), attendanceController.getAttendanceRecords);
router.get('/school/:school_id/stats', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher']), attendanceController.getAttendanceStats);

// Export routes
router.get('/school/:school_id/export/pdf', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher']), attendanceController.exportAttendancePDF);
router.get('/school/:school_id/export/excel', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher']), attendanceController.exportAttendanceExcel);

module.exports = router;
