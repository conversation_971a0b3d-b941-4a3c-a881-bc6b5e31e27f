"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/teacher-assignment/page",{

/***/ "(app-pages-browser)/./src/components/modals/TeacherAssignmentModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/modals/TeacherAssignmentModal.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherAssignmentModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst DAYS = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday',\n    'Saturday',\n    'Sunday'\n];\nconst SCHEDULE_TYPES = [\n    'Normal',\n    'Exam',\n    'Event'\n];\nfunction TeacherAssignmentModal(param) {\n    let { isOpen, onClose, onSubmit, assignment, classes, subjects, teachers, periods, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        class_id: '',\n        subject_id: '',\n        teacher_id: '',\n        period_id: '',\n        day_of_week: '',\n        schedule_type: 'Normal'\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Search states\n    const [classSearch, setClassSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [subjectSearch, setSubjectSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [teacherSearch, setTeacherSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showClassDropdown, setShowClassDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSubjectDropdown, setShowSubjectDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTeacherDropdown, setShowTeacherDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Assigned periods for selected class\n    const [assignedPeriods, setAssignedPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Reset form when modal opens/closes or assignment changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherAssignmentModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (assignment) {\n                    // Edit mode - populate form with assignment data\n                    // Extract IDs from objects if they exist, or use direct IDs\n                    const classId = typeof assignment.class_id === 'object' ? assignment.class_id._id : assignment.class_id;\n                    const subjectId = typeof assignment.subject_id === 'object' ? assignment.subject_id._id : assignment.subject_id;\n                    const teacherId = typeof assignment.teacher_id === 'object' ? assignment.teacher_id._id : assignment.teacher_id;\n                    // For period, we might need to find by period_number if the ID doesn't match\n                    let periodId = typeof assignment.period_id === 'object' ? assignment.period_id._id : assignment.period_id;\n                    // If we have period_number but no direct period_id, find the period by number\n                    if (!periodId && assignment.period_number) {\n                        const matchingPeriod = periods.find({\n                            \"TeacherAssignmentModal.useEffect.matchingPeriod\": (p)=>p.period_number === assignment.period_number\n                        }[\"TeacherAssignmentModal.useEffect.matchingPeriod\"]);\n                        periodId = (matchingPeriod === null || matchingPeriod === void 0 ? void 0 : matchingPeriod._id) || '';\n                    }\n                    console.log('Assignment data for editing:', {\n                        assignment,\n                        extractedIds: {\n                            classId,\n                            subjectId,\n                            teacherId,\n                            periodId\n                        }\n                    });\n                    setFormData({\n                        class_id: classId || '',\n                        subject_id: subjectId || '',\n                        teacher_id: teacherId || '',\n                        period_id: periodId || '',\n                        day_of_week: assignment.day_of_week || '',\n                        schedule_type: assignment.schedule_type || 'Normal'\n                    });\n                } else {\n                    // Create mode - reset form\n                    setFormData({\n                        class_id: '',\n                        subject_id: '',\n                        teacher_id: '',\n                        period_id: '',\n                        day_of_week: '',\n                        schedule_type: 'Normal'\n                    });\n                }\n                setErrors({});\n                // Reset search states\n                setClassSearch('');\n                setSubjectSearch('');\n                setTeacherSearch('');\n                setShowClassDropdown(false);\n                setShowSubjectDropdown(false);\n                setShowTeacherDropdown(false);\n            }\n        }\n    }[\"TeacherAssignmentModal.useEffect\"], [\n        isOpen,\n        assignment\n    ]);\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherAssignmentModal.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"TeacherAssignmentModal.useEffect.handleClickOutside\": (event)=>{\n                    const target = event.target;\n                    if (!target.closest('.relative')) {\n                        setShowClassDropdown(false);\n                        setShowSubjectDropdown(false);\n                        setShowTeacherDropdown(false);\n                    }\n                }\n            }[\"TeacherAssignmentModal.useEffect.handleClickOutside\"];\n            if (showClassDropdown || showSubjectDropdown || showTeacherDropdown) {\n                document.addEventListener('mousedown', handleClickOutside);\n                return ({\n                    \"TeacherAssignmentModal.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n                })[\"TeacherAssignmentModal.useEffect\"];\n            }\n        }\n    }[\"TeacherAssignmentModal.useEffect\"], [\n        showClassDropdown,\n        showSubjectDropdown,\n        showTeacherDropdown\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: ''\n                }));\n        }\n    };\n    // Filter functions\n    const filteredClasses = classes.filter((cls)=>cls.name.toLowerCase().includes(classSearch.toLowerCase()));\n    const filteredSubjects = subjects.filter((subject)=>subject.name.toLowerCase().includes(subjectSearch.toLowerCase()));\n    const filteredTeachers = teachers.filter((teacher)=>{\n        const teacherName = teacher.first_name && teacher.last_name ? \"\".concat(teacher.first_name, \" \").concat(teacher.last_name) : teacher.name || 'Unknown Teacher';\n        return teacherName.toLowerCase().includes(teacherSearch.toLowerCase());\n    });\n    // Get selected item names for display\n    const getSelectedClassName = ()=>{\n        const selectedClass = classes.find((cls)=>cls._id === formData.class_id);\n        return selectedClass ? selectedClass.name : '';\n    };\n    const getSelectedSubjectName = ()=>{\n        const selectedSubject = subjects.find((subject)=>subject._id === formData.subject_id);\n        return selectedSubject ? selectedSubject.name : '';\n    };\n    const getSelectedTeacherName = ()=>{\n        const selectedTeacher = teachers.find((teacher)=>teacher._id === formData.teacher_id);\n        if (!selectedTeacher) return '';\n        return selectedTeacher.first_name && selectedTeacher.last_name ? \"\".concat(selectedTeacher.first_name, \" \").concat(selectedTeacher.last_name) : selectedTeacher.name || 'Unknown Teacher';\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.class_id) newErrors.class_id = 'Class is required';\n        if (!formData.subject_id) newErrors.subject_id = 'Subject is required';\n        if (!formData.teacher_id) newErrors.teacher_id = 'Teacher is required';\n        if (!formData.period_id) newErrors.period_id = 'Period is required';\n        if (!formData.day_of_week) newErrors.day_of_week = 'Day of week is required';\n        if (!formData.schedule_type) newErrors.schedule_type = 'Schedule type is required';\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsSubmitting(true);\n        try {\n            await onSubmit(formData);\n            onClose();\n        } catch (error) {\n            console.error('Error submitting assignment:', error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleClose = ()=>{\n        if (!isSubmitting && !loading) {\n            onClose();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.95\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                exit: {\n                    opacity: 0,\n                    scale: 0.95\n                },\n                className: \"bg-widget rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-b border-stroke\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-foreground\",\n                                children: assignment ? 'Edit Teacher Assignment' : 'New Teacher Assignment'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClose,\n                                disabled: isSubmitting || loading,\n                                className: \"p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors disabled:opacity-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5 text-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"p-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Class \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.class_id ? getSelectedClassName() : classSearch,\n                                                        onChange: (e)=>{\n                                                            setClassSearch(e.target.value);\n                                                            if (formData.class_id) {\n                                                                handleInputChange('class_id', '');\n                                                            }\n                                                            setShowClassDropdown(true);\n                                                        },\n                                                        onFocus: ()=>setShowClassDropdown(true),\n                                                        placeholder: \"Search and select a class\",\n                                                        className: \"w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.class_id ? 'border-red-500' : 'border-stroke'),\n                                                        disabled: isSubmitting || loading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    showClassDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute z-10 w-full mt-1 bg-widget border border-stroke rounded-md shadow-lg max-h-60 overflow-y-auto\",\n                                                        children: filteredClasses.length > 0 ? filteredClasses.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                onClick: ()=>{\n                                                                    handleInputChange('class_id', classItem._id);\n                                                                    setClassSearch('');\n                                                                    setShowClassDropdown(false);\n                                                                },\n                                                                className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer text-foreground\",\n                                                                children: classItem.name\n                                                            }, classItem._id, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 27\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 text-foreground/60\",\n                                                            children: \"No classes found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.class_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.class_id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Subject \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.subject_id ? getSelectedSubjectName() : subjectSearch,\n                                                        onChange: (e)=>{\n                                                            setSubjectSearch(e.target.value);\n                                                            if (formData.subject_id) {\n                                                                handleInputChange('subject_id', '');\n                                                            }\n                                                            setShowSubjectDropdown(true);\n                                                        },\n                                                        onFocus: ()=>setShowSubjectDropdown(true),\n                                                        placeholder: \"Search and select a subject\",\n                                                        className: \"w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.subject_id ? 'border-red-500' : 'border-stroke'),\n                                                        disabled: isSubmitting || loading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    showSubjectDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute z-10 w-full mt-1 bg-widget border border-stroke rounded-md shadow-lg max-h-60 overflow-y-auto\",\n                                                        children: filteredSubjects.length > 0 ? filteredSubjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                onClick: ()=>{\n                                                                    handleInputChange('subject_id', subject._id);\n                                                                    setSubjectSearch('');\n                                                                    setShowSubjectDropdown(false);\n                                                                },\n                                                                className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer text-foreground\",\n                                                                children: subject.name\n                                                            }, subject._id, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 27\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 text-foreground/60\",\n                                                            children: \"No subjects found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.subject_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.subject_id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Teacher \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.teacher_id ? getSelectedTeacherName() : teacherSearch,\n                                                        onChange: (e)=>{\n                                                            setTeacherSearch(e.target.value);\n                                                            if (formData.teacher_id) {\n                                                                handleInputChange('teacher_id', '');\n                                                            }\n                                                            setShowTeacherDropdown(true);\n                                                        },\n                                                        onFocus: ()=>setShowTeacherDropdown(true),\n                                                        placeholder: \"Search and select a teacher\",\n                                                        className: \"w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.teacher_id ? 'border-red-500' : 'border-stroke'),\n                                                        disabled: isSubmitting || loading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    showTeacherDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute z-10 w-full mt-1 bg-widget border border-stroke rounded-md shadow-lg max-h-60 overflow-y-auto\",\n                                                        children: filteredTeachers.length > 0 ? filteredTeachers.map((teacher)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                onClick: ()=>{\n                                                                    handleInputChange('teacher_id', teacher._id);\n                                                                    setTeacherSearch('');\n                                                                    setShowTeacherDropdown(false);\n                                                                },\n                                                                className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer text-foreground\",\n                                                                children: teacher.first_name && teacher.last_name ? \"\".concat(teacher.first_name, \" \").concat(teacher.last_name) : teacher.name || 'Unknown Teacher'\n                                                            }, teacher._id, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 27\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 text-foreground/60\",\n                                                            children: \"No teachers found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.teacher_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.teacher_id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Period \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 26\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.period_id,\n                                                onChange: (e)=>handleInputChange('period_id', e.target.value),\n                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.period_id ? 'border-red-500' : 'border-stroke'),\n                                                disabled: isSubmitting || loading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select a period\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    periods.map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: period._id,\n                                                            children: [\n                                                                \"Period \",\n                                                                period.period_number,\n                                                                \" (\",\n                                                                period.start_time.slice(0, 5),\n                                                                \" - \",\n                                                                period.end_time.slice(0, 5),\n                                                                \")\"\n                                                            ]\n                                                        }, period._id, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.period_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.period_id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Day of Week \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.day_of_week,\n                                                onChange: (e)=>handleInputChange('day_of_week', e.target.value),\n                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.day_of_week ? 'border-red-500' : 'border-stroke'),\n                                                disabled: isSubmitting || loading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select a day\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: day,\n                                                            children: day\n                                                        }, day, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.day_of_week && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.day_of_week\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Schedule Type \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.schedule_type,\n                                                onChange: (e)=>handleInputChange('schedule_type', e.target.value),\n                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.schedule_type ? 'border-red-500' : 'border-stroke'),\n                                                disabled: isSubmitting || loading,\n                                                children: SCHEDULE_TYPES.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: type,\n                                                        children: type\n                                                    }, type, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.schedule_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.schedule_type\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end space-x-4 pt-6 border-t border-stroke\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleClose,\n                                        disabled: isSubmitting || loading,\n                                        className: \"px-4 py-2 text-foreground/70 hover:text-foreground transition-colors disabled:opacity-50\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || loading,\n                                        className: \"flex items-center space-x-2 px-6 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors disabled:opacity-50\",\n                                        children: [\n                                            (isSubmitting || loading) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 47\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: assignment ? 'Update Assignment' : 'Create Assignment'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                lineNumber: 218,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n            lineNumber: 217,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherAssignmentModal, \"xXzbU9ZtcAXI0HXLwQih3pROD94=\");\n_c = TeacherAssignmentModal;\nvar _c;\n$RefreshReg$(_c, \"TeacherAssignmentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/TeacherAssignmentModal.tsx\n"));

/***/ })

});