"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/attendance/page",{

/***/ "(app-pages-browser)/./src/components/modals/AttendanceModal.tsx":
/*!***************************************************!*\
  !*** ./src/components/modals/AttendanceModal.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttendanceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FileCheck2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FileCheck2,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_FileCheck2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileCheck2,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_FileCheck2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileCheck2,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AttendanceModal(param) {\n    let { isOpen, onClose, onSubmit, attendance, students, classes, subjects, schedules, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id: \"\",\n        schedule_id: \"\",\n        status: \"Present\",\n        date: new Date().toISOString().split('T')[0],\n        academic_year: \"2024-2025\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredStudents, setFilteredStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Multi-selection states\n    const [isMultiMode, setIsMultiMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedStudents, setSelectedStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [studentStatuses, setStudentStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [bulkStatus, setBulkStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Present\");\n    const isEditing = !!attendance;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (attendance) {\n                    // Single edit mode\n                    setIsMultiMode(false);\n                    setFormData({\n                        student_id: attendance.student_id || \"\",\n                        schedule_id: attendance.schedule_id || \"\",\n                        status: attendance.status || \"Present\",\n                        date: attendance.date ? new Date(attendance.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n                        academic_year: attendance.academic_year || \"2024-2025\"\n                    });\n                } else {\n                    // New attendance - default to multi mode\n                    setIsMultiMode(true);\n                    setFormData({\n                        student_id: \"\",\n                        schedule_id: \"\",\n                        status: \"Present\",\n                        date: new Date().toISOString().split('T')[0],\n                        academic_year: \"2024-2025\"\n                    });\n                }\n                setErrors({});\n                setSelectedStudents(new Set());\n                setStudentStatuses({});\n                setBulkStatus(\"Present\");\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        isOpen,\n        attendance\n    ]);\n    // Filter students based on selected schedule\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (formData.schedule_id) {\n                const selectedSchedule = schedules.find({\n                    \"AttendanceModal.useEffect.selectedSchedule\": (s)=>s._id === formData.schedule_id\n                }[\"AttendanceModal.useEffect.selectedSchedule\"]);\n                if (selectedSchedule) {\n                    const classStudents = students.filter({\n                        \"AttendanceModal.useEffect.classStudents\": (student)=>student.class_id === selectedSchedule.class_id\n                    }[\"AttendanceModal.useEffect.classStudents\"]);\n                    setFilteredStudents(classStudents);\n                    // Initialize student statuses for multi-mode\n                    if (isMultiMode) {\n                        const initialStatuses = {};\n                        classStudents.forEach({\n                            \"AttendanceModal.useEffect\": (student)=>{\n                                initialStatuses[student._id] = \"Present\";\n                            }\n                        }[\"AttendanceModal.useEffect\"]);\n                        setStudentStatuses(initialStatuses);\n                    }\n                }\n            } else {\n                setFilteredStudents(students);\n                if (isMultiMode) {\n                    const initialStatuses = {};\n                    students.forEach({\n                        \"AttendanceModal.useEffect\": (student)=>{\n                            initialStatuses[student._id] = \"Present\";\n                        }\n                    }[\"AttendanceModal.useEffect\"]);\n                    setStudentStatuses(initialStatuses);\n                }\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        formData.schedule_id,\n        schedules,\n        students,\n        isMultiMode\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!isMultiMode && !formData.student_id) {\n            newErrors.student_id = \"Student is required\";\n        }\n        if (isMultiMode && selectedStudents.size === 0) {\n            newErrors.students = \"At least one student must be selected\";\n        }\n        if (!formData.schedule_id) {\n            newErrors.schedule_id = \"Class schedule is required\";\n        }\n        if (!formData.date) {\n            newErrors.date = \"Date is required\";\n        }\n        if (!formData.academic_year) {\n            newErrors.academic_year = \"Academic year is required\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsSubmitting(true);\n        try {\n            await onSubmit(formData);\n            onClose();\n        } catch (error) {\n            console.error(\"Error submitting attendance:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    // Multi-selection functions\n    const toggleStudentSelection = (studentId)=>{\n        const newSelected = new Set(selectedStudents);\n        if (newSelected.has(studentId)) {\n            newSelected.delete(studentId);\n        } else {\n            newSelected.add(studentId);\n        }\n        setSelectedStudents(newSelected);\n    };\n    const selectAllStudents = ()=>{\n        const allStudentIds = new Set(filteredStudents.map((s)=>s._id));\n        setSelectedStudents(allStudentIds);\n    };\n    const deselectAllStudents = ()=>{\n        setSelectedStudents(new Set());\n    };\n    const applyBulkStatus = ()=>{\n        const newStatuses = {\n            ...studentStatuses\n        };\n        selectedStudents.forEach((studentId)=>{\n            newStatuses[studentId] = bulkStatus;\n        });\n        setStudentStatuses(newStatuses);\n    };\n    const updateStudentStatus = (studentId, status)=>{\n        setStudentStatuses((prev)=>({\n                ...prev,\n                [studentId]: status\n            }));\n    };\n    const getScheduleDisplay = (schedule)=>{\n        const subject = subjects.find((s)=>s._id === schedule.subject_id);\n        const classInfo = classes.find((c)=>c._id === schedule.class_id);\n        return \"\".concat((subject === null || subject === void 0 ? void 0 : subject.name) || 'Unknown Subject', \" - \").concat((classInfo === null || classInfo === void 0 ? void 0 : classInfo.name) || 'Unknown Class', \" (\").concat(schedule.day_of_week, \")\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-black bg-opacity-50\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileCheck2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                    children: isEditing ? \"Edit Attendance\" : \"Mark Attendance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: isEditing ? \"Update attendance record\" : \"Create new attendance record\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileCheck2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"p-6 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Class Schedule\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.schedule_id,\n                                            onChange: (e)=>handleInputChange(\"schedule_id\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.schedule_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select class schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this),\n                                                schedules.map((schedule)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: schedule._id,\n                                                        children: getScheduleDisplay(schedule)\n                                                    }, schedule._id, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.schedule_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.schedule_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Student\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.student_id,\n                                            onChange: (e)=>handleInputChange(\"student_id\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.student_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            disabled: !formData.schedule_id,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select student\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                filteredStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: student._id,\n                                                        children: [\n                                                            student.first_name,\n                                                            \" \",\n                                                            student.last_name,\n                                                            \" (\",\n                                                            student.student_id,\n                                                            \")\"\n                                                        ]\n                                                    }, student._id, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.student_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.student_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.status,\n                                            onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Present\",\n                                                    children: \"Present\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Absent\",\n                                                    children: \"Absent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Late\",\n                                                    children: \"Late\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Excused\",\n                                                    children: \"Excused\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Date\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.date,\n                                            onChange: (e)=>handleInputChange(\"date\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.date ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.date\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Academic Year\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.academic_year,\n                                            onChange: (e)=>handleInputChange(\"academic_year\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.academic_year ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            placeholder: \"e.g., 2024-2025\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.academic_year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.academic_year\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: onClose,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            disabled: isSubmitting,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                            children: [\n                                                isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileCheck2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isSubmitting ? \"Saving...\" : isEditing ? \"Update\" : \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n            lineNumber: 200,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, this);\n}\n_s(AttendanceModal, \"3tsxPmqIApOmqHrWSp1PJC8s8Qw=\");\n_c = AttendanceModal;\nvar _c;\n$RefreshReg$(_c, \"AttendanceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/AttendanceModal.tsx\n"));

/***/ })

});