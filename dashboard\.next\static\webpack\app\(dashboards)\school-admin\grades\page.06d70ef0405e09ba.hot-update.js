"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/grades/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/grades/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GradesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/GradeServices */ \"(app-pages-browser)/./src/app/services/GradeServices.tsx\");\n/* harmony import */ var _app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/StudentServices */ \"(app-pages-browser)/./src/app/services/StudentServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/ExamTypeServices */ \"(app-pages-browser)/./src/app/services/ExamTypeServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_UserServices__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/services/UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/GradeModal */ \"(app-pages-browser)/./src/components/modals/GradeModal.tsx\");\n/* harmony import */ var _components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/PasswordConfirmDeleteModal */ \"(app-pages-browser)/./src/components/modals/PasswordConfirmDeleteModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    baseHref: \"/school-admin/grades\",\n    title: \"Grades\"\n};\nfunction GradesPage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    // State management\n    const [gradeRecords, setGradeRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalGrades: 0,\n        averageScore: 0,\n        highestScore: 0,\n        lowestScore: 0,\n        passRate: 0\n    });\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedTerm, setSelectedTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedExamType, setSelectedExamType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isGradeModalOpen, setIsGradeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gradeToEdit, setGradeToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gradeToDelete, setGradeToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedGrades, setSelectedGrades] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [examTypes, setExamTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch grade data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchGradeData = {\n                \"GradesPage.useEffect.fetchGradeData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n                        if (selectedTerm !== 'all') filters.term = selectedTerm;\n                        if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n                        // Fetch records and stats in parallel\n                        const [recordsResponse, statsResponse] = await Promise.all([\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n                        ]);\n                        setGradeRecords(recordsResponse.grade_records);\n                        setStats(statsResponse.stats);\n                    } catch (error) {\n                        console.error(\"Error fetching grade data:\", error);\n                        showError(\"Error\", \"Failed to load grade data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchGradeData\"];\n            fetchGradeData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId,\n        selectedClass,\n        selectedSubject,\n        selectedTerm,\n        selectedExamType\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"GradesPage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__.getStudentsBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__.getExamTypes)(),\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_11__.getClassesBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setStudents(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch students:\", results[0].reason);\n                            setStudents([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setExamTypes(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch exam types:\", results[2].reason);\n                            setExamTypes([]);\n                        }\n                        if (results[3].status === 'fulfilled') {\n                            setClasses(results[3].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[3].reason);\n                            setClasses([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateGrade = ()=>{\n        setGradeToEdit(null);\n        setIsGradeModalOpen(true);\n    };\n    const handleEditGrade = (grade)=>{\n        setGradeToEdit(grade);\n        setIsGradeModalOpen(true);\n    };\n    const handleDeleteGrade = (grade)=>{\n        setGradeToDelete(grade);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedGrades(selectedRows);\n    };\n    // Modal submission functions\n    const handleGradeSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (gradeToEdit) {\n                // Update existing grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.updateGrade)(gradeToEdit._id, data);\n            } else {\n                // Create new grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.createGrade)({\n                    ...data,\n                    school_id: schoolId\n                });\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsGradeModalOpen(false);\n            setGradeToEdit(null);\n            // Show success notification\n            showSuccess(gradeToEdit ? \"Grade Updated\" : \"Grade Added\", gradeToEdit ? \"Grade has been updated successfully.\" : \"New grade has been added successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting grade:\", error);\n            showError(\"Error\", \"Failed to save grade. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteConfirm = async (password)=>{\n        if (!schoolId || !user) return;\n        try {\n            // Verify password\n            const passwordVerified = await (0,_app_services_UserServices__WEBPACK_IMPORTED_MODULE_12__.verifyPassword)(password, user.email);\n            if (!passwordVerified) {\n                showError(\"Error\", \"Invalid password. Please try again.\");\n                throw new Error(\"Invalid password\");\n            }\n            if (deleteType === \"single\" && gradeToDelete) {\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteGrade)(gradeToDelete._id);\n            } else if (deleteType === \"multiple\") {\n                const selectedIds = selectedGrades.map((g)=>g._id);\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteMultipleGrades)(selectedIds);\n                setClearSelection(true);\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsDeleteModalOpen(false);\n            setGradeToDelete(null);\n            setSelectedGrades([]);\n            // Show success notification\n            if (deleteType === \"single\") {\n                showSuccess(\"Grade Deleted\", \"Grade record has been deleted successfully.\");\n            } else {\n                showSuccess(\"Grades Deleted\", \"\".concat(selectedGrades.length, \" grade records have been deleted successfully.\"));\n            }\n        } catch (error) {\n            console.error(\"Error deleting grade(s):\", error);\n            if (error.message !== \"Invalid password\") {\n                showError(\"Error\", \"Failed to delete grade record(s). Please try again.\");\n            }\n            throw error;\n        }\n    };\n    const getGradeColor = (grade)=>{\n        switch(grade){\n            case 'A+':\n            case 'A':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n            case 'B+':\n            case 'B':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n            case 'C+':\n            case 'C':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n            case 'D':\n            case 'F':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n        }\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 90) return 'text-green-600 font-semibold';\n        if (score >= 80) return 'text-blue-600 font-semibold';\n        if (score >= 70) return 'text-yellow-600 font-semibold';\n        if (score >= 60) return 'text-orange-600 font-semibold';\n        return 'text-red-600 font-semibold';\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Student\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-foreground\",\n                            children: row.student_name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-foreground/60\",\n                            children: row.student_id\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Exam Type\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.exam_type\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Score\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-bold \".concat(getScoreColor(row.score)),\n                    children: [\n                        row.score,\n                        \"%\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Grade\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-sm font-bold \".concat(getGradeColor(row.grade)),\n                    children: row.grade\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Term\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.term\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (grade)=>{\n                handleEditGrade(grade);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (grade)=>{\n                handleDeleteGrade(grade);\n            }\n        }\n    ];\n    // Filter data based on selections - Note: Backend filtering is primary, this is just for display consistency\n    const filteredRecords = gradeRecords;\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                lineNumber: 408,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 407,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Grades Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Monitor and manage student grades across all subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    stats.averageScore,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Average Score\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-5 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Grades\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: stats.totalGrades\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Average Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            stats.averageScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-teal rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Highest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: [\n                                                            stats.highestScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Lowest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: [\n                                                            stats.lowestScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Pass Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            stats.passRate,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((cls)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: cls._id,\n                                                            children: cls.name\n                                                        }, cls._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Subject:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedSubject,\n                                                onChange: (e)=>setSelectedSubject(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: subject._id,\n                                                            children: subject.name\n                                                        }, subject._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Term:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedTerm,\n                                                onChange: (e)=>setSelectedTerm(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Terms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"First Term\",\n                                                        children: \"First Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Second Term\",\n                                                        children: \"Second Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Third Term\",\n                                                        children: \"Third Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Exam Type:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedExamType,\n                                                onChange: (e)=>setSelectedExamType(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    examTypes.map((examType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: examType._id,\n                                                            children: examType.type\n                                                        }, examType._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Grade Records (\",\n                                                filteredRecords.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateGrade,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Grade\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredRecords,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    isOpen: isGradeModalOpen,\n                    onClose: ()=>{\n                        setIsGradeModalOpen(false);\n                        setGradeToEdit(null);\n                    },\n                    onSubmit: handleGradeSubmit,\n                    grade: gradeToEdit,\n                    students: students,\n                    subjects: subjects,\n                    examTypes: examTypes,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 617,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setGradeToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Grade Record\" : \"Delete Selected Grade Records\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this grade record? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedGrades.length, \" selected grade records? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && gradeToDelete ? \"\".concat(gradeToDelete.student_name, \" - \").concat(gradeToDelete.subject_name, \" (\").concat(gradeToDelete.exam_type, \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedGrades.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 659,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 415,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n        lineNumber: 414,\n        columnNumber: 5\n    }, this);\n}\n_s(GradesPage, \"Unbmq46oVbBsrl3g2oGxiFvVnHU=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = GradesPage;\nvar _c;\n$RefreshReg$(_c, \"GradesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx\n"));

/***/ })

});