const express = require('express');
const { authenticate, authorize } = require('../middleware/middleware');
const { checkTeacherSchoolAccess } = require('../middleware/teacherMiddleware');
const User = require('../models/User');
const StaffPermission = require('../models/StaffPermission');
const Student = require('../models/Student');

const router = express.Router();

// Get teacher's assignments (classes and subjects) for a specific school
router.get('/assignments/:school_id',
  authenticate,
  authorize(['teacher']),
  checkTeacherSchoolAccess,
  async (req, res) => {
    try {
      const { school_id } = req.params;

      // Teacher info is already available from middleware
      if (!req.teacher) {
        return res.status(404).json({ message: 'Teacher assignments not found' });
      }

      // Get class details
      const Class = require('../models/Class');
      const classDetails = await Class.find({
        _id: { $in: req.teacher.assigned_class_ids }
      });

      res.json({
        _id: req.teacher.user_id,
        teacher_id: req.teacher.user_id,
        school_id: school_id,
        assigned_classes: classDetails.map(cls => ({
          _id: cls._id,
          name: cls.name,
          level: cls.level || cls.grade_level
        })),
        assigned_subjects: req.teacher.assigned_subjects.map(subject => ({
          _id: subject, // Subject is stored as string
          name: subject,
          code: subject
        })),
        permissions: req.teacher.permissions,
        role_template: req.teacher.role_template
      });
    } catch (error) {
      console.error('Error fetching teacher assignments:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
);

// Get students in teacher's assigned classes
router.get('/students/:school_id', 
  authenticate, 
  authorize(['teacher']), 
  checkTeacherSchoolAccess,
  async (req, res) => {
    try {
      const { school_id } = req.params;
      
      // Get teacher's assigned classes from middleware
      const assignedClassIds = req.teacher.assigned_class_ids;

      if (assignedClassIds.length === 0) {
        return res.json({ students: [] });
      }

      // Find students in teacher's assigned classes
      const students = await Student.find({
        school_id: school_id,
        class_id: { $in: assignedClassIds }
      }).populate('class_id', 'name');

      const formattedStudents = students.map(student => ({
        _id: student._id,
        first_name: student.first_name,
        last_name: student.last_name,
        email: student.email,
        class_id: student.class_id._id,
        class_name: student.class_id.name
      }));

      res.json({ students: formattedStudents });
    } catch (error) {
      console.error('Error fetching teacher students:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
);

module.exports = router;
