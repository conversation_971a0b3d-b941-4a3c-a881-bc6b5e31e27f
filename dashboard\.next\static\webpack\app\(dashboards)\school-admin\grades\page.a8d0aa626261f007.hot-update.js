"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/grades/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/grades/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GradesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/GradeServices */ \"(app-pages-browser)/./src/app/services/GradeServices.tsx\");\n/* harmony import */ var _app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/StudentServices */ \"(app-pages-browser)/./src/app/services/StudentServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/ExamTypeServices */ \"(app-pages-browser)/./src/app/services/ExamTypeServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_UserServices__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/services/UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/GradeModal */ \"(app-pages-browser)/./src/components/modals/GradeModal.tsx\");\n/* harmony import */ var _components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/PasswordConfirmDeleteModal */ \"(app-pages-browser)/./src/components/modals/PasswordConfirmDeleteModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    baseHref: \"/school-admin/grades\",\n    title: \"Grades\"\n};\nfunction GradesPage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    // State management\n    const [gradeRecords, setGradeRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalGrades: 0,\n        averageScore: 0,\n        highestScore: 0,\n        lowestScore: 0,\n        passRate: 0\n    });\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedTerm, setSelectedTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedExamType, setSelectedExamType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isGradeModalOpen, setIsGradeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gradeToEdit, setGradeToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gradeToDelete, setGradeToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedGrades, setSelectedGrades] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [examTypes, setExamTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Export states\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showExportDropdown, setShowExportDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch grade data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchGradeData = {\n                \"GradesPage.useEffect.fetchGradeData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n                        if (selectedTerm !== 'all') filters.term = selectedTerm;\n                        if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n                        // Fetch records and stats in parallel\n                        const [recordsResponse, statsResponse] = await Promise.all([\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n                        ]);\n                        setGradeRecords(recordsResponse.grade_records);\n                        setStats(statsResponse.stats);\n                    } catch (error) {\n                        console.error(\"Error fetching grade data:\", error);\n                        showError(\"Error\", \"Failed to load grade data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchGradeData\"];\n            fetchGradeData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId,\n        selectedClass,\n        selectedSubject,\n        selectedTerm,\n        selectedExamType\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"GradesPage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__.getStudentsBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__.getExamTypes)(),\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_11__.getClassesBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setStudents(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch students:\", results[0].reason);\n                            setStudents([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setExamTypes(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch exam types:\", results[2].reason);\n                            setExamTypes([]);\n                        }\n                        if (results[3].status === 'fulfilled') {\n                            setClasses(results[3].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[3].reason);\n                            setClasses([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateGrade = ()=>{\n        setGradeToEdit(null);\n        setIsGradeModalOpen(true);\n    };\n    const handleEditGrade = (grade)=>{\n        setGradeToEdit(grade);\n        setIsGradeModalOpen(true);\n    };\n    const handleDeleteGrade = (grade)=>{\n        setGradeToDelete(grade);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedGrades(selectedRows);\n    };\n    // Modal submission functions\n    const handleGradeSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (gradeToEdit) {\n                // Update existing grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.updateGrade)(gradeToEdit._id, data);\n            } else {\n                // Create new grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.createGrade)({\n                    ...data,\n                    school_id: schoolId\n                });\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsGradeModalOpen(false);\n            setGradeToEdit(null);\n            // Show success notification\n            showSuccess(gradeToEdit ? \"Grade Updated\" : \"Grade Added\", gradeToEdit ? \"Grade has been updated successfully.\" : \"New grade has been added successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting grade:\", error);\n            showError(\"Error\", \"Failed to save grade. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteConfirm = async (password)=>{\n        if (!schoolId || !user) return;\n        try {\n            // Verify password\n            const passwordVerified = await (0,_app_services_UserServices__WEBPACK_IMPORTED_MODULE_12__.verifyPassword)(password, user.email);\n            if (!passwordVerified) {\n                showError(\"Error\", \"Invalid password. Please try again.\");\n                throw new Error(\"Invalid password\");\n            }\n            if (deleteType === \"single\" && gradeToDelete) {\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteGrade)(gradeToDelete._id);\n            } else if (deleteType === \"multiple\") {\n                const selectedIds = selectedGrades.map((g)=>g._id);\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteMultipleGrades)(selectedIds);\n                setClearSelection(true);\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsDeleteModalOpen(false);\n            setGradeToDelete(null);\n            setSelectedGrades([]);\n            // Show success notification\n            if (deleteType === \"single\") {\n                showSuccess(\"Grade Deleted\", \"Grade record has been deleted successfully.\");\n            } else {\n                showSuccess(\"Grades Deleted\", \"\".concat(selectedGrades.length, \" grade records have been deleted successfully.\"));\n            }\n        } catch (error) {\n            console.error(\"Error deleting grade(s):\", error);\n            if (error.message !== \"Invalid password\") {\n                showError(\"Error\", \"Failed to delete grade record(s). Please try again.\");\n            }\n            throw error;\n        }\n    };\n    const getGradeColor = (grade)=>{\n        // Handle both old format (A+, A, B+, etc.) and new format (A+/Excellent, etc.)\n        const gradePrefix = grade.split('/')[0];\n        switch(gradePrefix){\n            case 'A+':\n            case 'A':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n            case 'B+':\n            case 'B':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n            case 'C+':\n            case 'C':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n            case 'D+':\n            case 'D':\n            case 'F':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n        }\n    };\n    const getScoreColor = (score)=>{\n        // Convert percentage to /20 scale for color determination\n        const scoreOn20 = score * 20 / 100;\n        if (scoreOn20 >= 18) return 'text-green-600 font-semibold'; // 18-20/20 (90-100%)\n        if (scoreOn20 >= 16) return 'text-blue-600 font-semibold'; // 16-17.9/20 (80-89%)\n        if (scoreOn20 >= 14) return 'text-yellow-600 font-semibold'; // 14-15.9/20 (70-79%)\n        if (scoreOn20 >= 12) return 'text-orange-600 font-semibold'; // 12-13.9/20 (60-69%)\n        return 'text-red-600 font-semibold'; // <12/20 (<60%)\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Student\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-foreground\",\n                            children: row.student_name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-foreground/60\",\n                            children: row.student_id\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>\"\".concat(row.student_name, \" \").concat(row.student_id)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.class_name\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.subject_name\n        },\n        {\n            header: \"Exam Type\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.exam_type\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.exam_type\n        },\n        {\n            header: \"Score\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-bold \".concat(getScoreColor(row.score)),\n                    children: [\n                        (row.score * 20 / 100).toFixed(1),\n                        \"/20\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.score.toString()\n        },\n        {\n            header: \"Grade\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-sm font-bold \".concat(getGradeColor(row.grade)),\n                    children: row.grade\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.grade\n        },\n        {\n            header: \"Term\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.term\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.term\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (grade)=>{\n                handleEditGrade(grade);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (grade)=>{\n                handleDeleteGrade(grade);\n            }\n        }\n    ];\n    // Filter data based on selections - Note: Backend filtering is primary, this is just for display consistency\n    const filteredRecords = gradeRecords;\n    // Export functions\n    const handleExportPDF = async ()=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsExporting(true);\n        try {\n            const filters = {\n                class_id: selectedClass !== 'all' ? selectedClass : undefined,\n                subject_id: selectedSubject !== 'all' ? selectedSubject : undefined,\n                term: selectedTerm !== 'all' ? selectedTerm : undefined,\n                exam_type_id: selectedExamType !== 'all' ? selectedExamType : undefined\n            };\n            const blob = await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.exportGradesPDF)(schoolId, filters);\n            // Create download link\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"grades-export-\".concat(new Date().toISOString().split('T')[0], \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            showSuccess(\"Export Successful\", \"Grades have been exported to PDF successfully.\");\n            setShowExportDropdown(false);\n        } catch (error) {\n            console.error(\"Error exporting to PDF:\", error);\n            showError(\"Export Failed\", \"Failed to export grades to PDF. Please try again.\");\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const handleExportExcel = async ()=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsExporting(true);\n        try {\n            const filters = {\n                class_id: selectedClass !== 'all' ? selectedClass : undefined,\n                subject_id: selectedSubject !== 'all' ? selectedSubject : undefined,\n                term: selectedTerm !== 'all' ? selectedTerm : undefined,\n                exam_type_id: selectedExamType !== 'all' ? selectedExamType : undefined\n            };\n            const blob = await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.exportGradesExcel)(schoolId, filters);\n            // Create download link\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"grades-export-\".concat(new Date().toISOString().split('T')[0], \".xlsx\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            showSuccess(\"Export Successful\", \"Grades have been exported to Excel successfully.\");\n            setShowExportDropdown(false);\n        } catch (error) {\n            console.error(\"Error exporting to Excel:\", error);\n            showError(\"Export Failed\", \"Failed to export grades to Excel. Please try again.\");\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 501,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Grades Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Monitor and manage student grades across all subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    (stats.averageScore * 20 / 100).toFixed(1),\n                                                    \"/20\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Average Score\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-5 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Grades\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: stats.totalGrades\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Average Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            (stats.averageScore * 20 / 100).toFixed(1),\n                                                            \"/20\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-teal rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Highest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: [\n                                                            (stats.highestScore * 20 / 100).toFixed(1),\n                                                            \"/20\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Lowest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: [\n                                                            (stats.lowestScore * 20 / 100).toFixed(1),\n                                                            \"/20\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Pass Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            stats.passRate,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((cls)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: cls._id,\n                                                            children: cls.name\n                                                        }, cls._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Subject:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedSubject,\n                                                onChange: (e)=>setSelectedSubject(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: subject._id,\n                                                            children: subject.name\n                                                        }, subject._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Term:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedTerm,\n                                                onChange: (e)=>setSelectedTerm(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Terms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"First Term\",\n                                                        children: \"First Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Second Term\",\n                                                        children: \"Second Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Third Term\",\n                                                        children: \"Third Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Exam Type:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedExamType,\n                                                onChange: (e)=>setSelectedExamType(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    examTypes.map((examType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: examType._id,\n                                                            children: examType.type\n                                                        }, examType._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                onClick: ()=>setShowExportDropdown(!showExportDropdown),\n                                                disabled: isExporting,\n                                                className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        size: 14\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isExporting ? 'Exporting...' : 'Export'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 17\n                                            }, this),\n                                            showExportDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-widget border border-stroke rounded-md shadow-lg z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleExportPDF,\n                                                        disabled: isExporting,\n                                                        className: \"w-full text-left px-4 py-2 text-sm text-foreground hover:bg-gray-100 dark:hover:bg-gray-800 disabled:opacity-50\",\n                                                        children: \"Export as PDF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleExportExcel,\n                                                        disabled: isExporting,\n                                                        className: \"w-full text-left px-4 py-2 text-sm text-foreground hover:bg-gray-100 dark:hover:bg-gray-800 disabled:opacity-50\",\n                                                        children: \"Export as Excel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 597,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Grade Records (\",\n                                                filteredRecords.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateGrade,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Grade\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 718,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredRecords,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 701,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 510,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    isOpen: isGradeModalOpen,\n                    onClose: ()=>{\n                        setIsGradeModalOpen(false);\n                        setGradeToEdit(null);\n                    },\n                    onSubmit: handleGradeSubmit,\n                    grade: gradeToEdit,\n                    students: students,\n                    subjects: subjects,\n                    examTypes: examTypes,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 734,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setGradeToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Grade Record\" : \"Delete Selected Grade Records\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this grade record? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedGrades.length, \" selected grade records? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && gradeToDelete ? \"\".concat(gradeToDelete.student_name, \" - \").concat(gradeToDelete.subject_name, \" (\").concat(gradeToDelete.exam_type, \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedGrades.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 749,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 776,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 509,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n        lineNumber: 508,\n        columnNumber: 5\n    }, this);\n}\n_s(GradesPage, \"sCkfk1/74HB4i9qZ2zMbxx1B86M=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = GradesPage;\nvar _c;\n$RefreshReg$(_c, \"GradesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx\n"));

/***/ })

});