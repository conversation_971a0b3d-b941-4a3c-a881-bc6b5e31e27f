"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/attendance/page",{

/***/ "(app-pages-browser)/./src/components/modals/AttendanceModal.tsx":
/*!***************************************************!*\
  !*** ./src/components/modals/AttendanceModal.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttendanceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AttendanceModal(param) {\n    let { isOpen, onClose, onSubmit, attendance, students, classes, subjects, schedules, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id: \"\",\n        schedule_id: \"\",\n        status: \"Present\",\n        date: new Date().toISOString().split('T')[0],\n        academic_year: \"2024-2025\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredStudents, setFilteredStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Multi-selection states\n    const [isMultiMode, setIsMultiMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedStudents, setSelectedStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [studentStatuses, setStudentStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [bulkStatus, setBulkStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Present\");\n    // Search states\n    const [scheduleSearch, setScheduleSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [studentSearch, setStudentSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showScheduleDropdown, setShowScheduleDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStudentDropdown, setShowStudentDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isEditing = !!attendance;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (attendance) {\n                    // Single edit mode\n                    setIsMultiMode(false);\n                    setFormData({\n                        student_id: attendance.student_id || \"\",\n                        schedule_id: attendance.schedule_id || \"\",\n                        status: attendance.status || \"Present\",\n                        date: attendance.date ? new Date(attendance.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n                        academic_year: attendance.academic_year || \"2024-2025\"\n                    });\n                } else {\n                    // New attendance - default to multi mode\n                    setIsMultiMode(true);\n                    setFormData({\n                        student_id: \"\",\n                        schedule_id: \"\",\n                        status: \"Present\",\n                        date: new Date().toISOString().split('T')[0],\n                        academic_year: \"2024-2025\"\n                    });\n                }\n                setErrors({});\n                setSelectedStudents(new Set());\n                setStudentStatuses({});\n                setBulkStatus(\"Present\");\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        isOpen,\n        attendance\n    ]);\n    // Filter students based on selected schedule\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (formData.schedule_id) {\n                const selectedSchedule = schedules.find({\n                    \"AttendanceModal.useEffect.selectedSchedule\": (s)=>s._id === formData.schedule_id\n                }[\"AttendanceModal.useEffect.selectedSchedule\"]);\n                if (selectedSchedule) {\n                    const classStudents = students.filter({\n                        \"AttendanceModal.useEffect.classStudents\": (student)=>student.class_id === selectedSchedule.class_id\n                    }[\"AttendanceModal.useEffect.classStudents\"]);\n                    setFilteredStudents(classStudents);\n                    // Initialize student statuses for multi-mode\n                    if (isMultiMode) {\n                        const initialStatuses = {};\n                        classStudents.forEach({\n                            \"AttendanceModal.useEffect\": (student)=>{\n                                initialStatuses[student._id] = \"Present\";\n                            }\n                        }[\"AttendanceModal.useEffect\"]);\n                        setStudentStatuses(initialStatuses);\n                    }\n                }\n            } else {\n                setFilteredStudents(students);\n                if (isMultiMode) {\n                    const initialStatuses = {};\n                    students.forEach({\n                        \"AttendanceModal.useEffect\": (student)=>{\n                            initialStatuses[student._id] = \"Present\";\n                        }\n                    }[\"AttendanceModal.useEffect\"]);\n                    setStudentStatuses(initialStatuses);\n                }\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        formData.schedule_id,\n        schedules,\n        students,\n        isMultiMode\n    ]);\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"AttendanceModal.useEffect.handleClickOutside\": ()=>{\n                    setShowScheduleDropdown(false);\n                    setShowStudentDropdown(false);\n                }\n            }[\"AttendanceModal.useEffect.handleClickOutside\"];\n            if (showScheduleDropdown || showStudentDropdown) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"AttendanceModal.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"AttendanceModal.useEffect\"];\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        showScheduleDropdown,\n        showStudentDropdown\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!isMultiMode && !formData.student_id) {\n            newErrors.student_id = \"Student is required\";\n        }\n        if (isMultiMode && selectedStudents.size === 0) {\n            newErrors.students = \"At least one student must be selected\";\n        }\n        if (!formData.schedule_id) {\n            newErrors.schedule_id = \"Class schedule is required\";\n        }\n        if (!formData.date) {\n            newErrors.date = \"Date is required\";\n        }\n        if (!formData.academic_year) {\n            newErrors.academic_year = \"Academic year is required\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        console.log('handleSubmit called', {\n            isMultiMode,\n            selectedStudents: selectedStudents.size,\n            formData\n        }); // Debug log\n        if (!validateForm()) {\n            console.log('Validation failed'); // Debug log\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (isMultiMode) {\n                console.log('Multi-mode submission', {\n                    selectedStudents: Array.from(selectedStudents),\n                    studentStatuses\n                }); // Debug log\n                // Submit multiple attendance records\n                const attendancePromises = Array.from(selectedStudents).map((studentId)=>{\n                    const studentStatus = studentStatuses[studentId] || \"Present\";\n                    const submissionData = {\n                        ...formData,\n                        student_id: studentId,\n                        status: studentStatus\n                    };\n                    console.log('Submitting for student:', submissionData); // Debug log\n                    return onSubmit(submissionData);\n                });\n                await Promise.all(attendancePromises);\n                console.log('Multi-mode submission completed'); // Debug log\n            } else {\n                console.log('Single-mode submission', formData); // Debug log\n                // Submit single attendance record\n                await onSubmit(formData);\n                console.log('Single-mode submission completed'); // Debug log\n            }\n            onClose();\n        } catch (error) {\n            console.error(\"Error submitting attendance:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    // Multi-selection functions\n    const toggleStudentSelection = (studentId)=>{\n        const newSelected = new Set(selectedStudents);\n        if (newSelected.has(studentId)) {\n            newSelected.delete(studentId);\n        } else {\n            newSelected.add(studentId);\n        }\n        setSelectedStudents(newSelected);\n    };\n    const selectAllStudents = ()=>{\n        console.log('selectAllStudents called, filteredStudents:', filteredStudents); // Debug log\n        if (filteredStudents && filteredStudents.length > 0) {\n            const allStudentIds = new Set(filteredStudents.map((s)=>s._id));\n            console.log('Setting selected students:', allStudentIds); // Debug log\n            setSelectedStudents(allStudentIds);\n            // Initialize statuses for all selected students\n            const newStatuses = {\n                ...studentStatuses\n            };\n            filteredStudents.forEach((student)=>{\n                if (!newStatuses[student._id]) {\n                    newStatuses[student._id] = \"Present\";\n                }\n            });\n            setStudentStatuses(newStatuses);\n        } else {\n            console.log('No filtered students available'); // Debug log\n        }\n    };\n    const deselectAllStudents = ()=>{\n        setSelectedStudents(new Set());\n    };\n    const applyBulkStatus = ()=>{\n        const newStatuses = {\n            ...studentStatuses\n        };\n        selectedStudents.forEach((studentId)=>{\n            newStatuses[studentId] = bulkStatus;\n        });\n        setStudentStatuses(newStatuses);\n    };\n    const updateStudentStatus = (studentId, status)=>{\n        setStudentStatuses((prev)=>({\n                ...prev,\n                [studentId]: status\n            }));\n    };\n    const getScheduleDisplay = (schedule)=>{\n        const subject = subjects.find((s)=>s._id === schedule.subject_id);\n        const classInfo = classes.find((c)=>c._id === schedule.class_id);\n        return \"\".concat((subject === null || subject === void 0 ? void 0 : subject.name) || 'Unknown Subject', \" - \").concat((classInfo === null || classInfo === void 0 ? void 0 : classInfo.name) || 'Unknown Class', \" (\").concat(schedule.day_of_week, \")\");\n    };\n    // Helper functions for search and selection\n    const getSelectedScheduleName = ()=>{\n        const selectedSchedule = schedules.find((s)=>s._id === formData.schedule_id);\n        return selectedSchedule ? getScheduleDisplay(selectedSchedule) : '';\n    };\n    const getSelectedStudentName = ()=>{\n        const selectedStudent = filteredStudents.find((s)=>s._id === formData.student_id);\n        return selectedStudent ? \"\".concat(selectedStudent.first_name, \" \").concat(selectedStudent.last_name, \" (\").concat(selectedStudent.student_id, \")\") : '';\n    };\n    const filteredSchedules = schedules.filter((schedule)=>getScheduleDisplay(schedule).toLowerCase().includes(scheduleSearch.toLowerCase()));\n    const filteredStudentsForSearch = filteredStudents.filter((student)=>\"\".concat(student.first_name, \" \").concat(student.last_name, \" \").concat(student.student_id).toLowerCase().includes(studentSearch.toLowerCase()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-black bg-opacity-50\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full mx-4 \".concat(isMultiMode ? 'max-w-4xl' : 'max-w-md'),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-lg flex items-center justify-center \".concat(isMultiMode ? 'bg-green-500' : 'bg-blue-500'),\n                                            children: isMultiMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 34\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 77\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                    children: isEditing ? \"Edit Attendance\" : isMultiMode ? \"Mark Multiple Attendance\" : \"Mark Attendance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: isEditing ? \"Update attendance record\" : isMultiMode ? \"Mark attendance for multiple students\" : \"Create new attendance record\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        !isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMultiMode(!isMultiMode),\n                                            className: \"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600\",\n                                            children: isMultiMode ? 'Single Mode' : 'Multi Mode'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"p-6 space-y-4 \".concat(isMultiMode ? 'max-h-96 overflow-y-auto' : ''),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: [\n                                                \"Class Schedule \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 34\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 \".concat(errors.schedule_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setShowScheduleDropdown(!showScheduleDropdown);\n                                                        setShowStudentDropdown(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900 dark:text-white\",\n                                                            children: getSelectedScheduleName() || \"Select class schedule\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showScheduleDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 border-b border-gray-200 dark:border-gray-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        placeholder: \"Search schedules...\",\n                                                                        value: scheduleSearch,\n                                                                        onChange: (e)=>setScheduleSearch(e.target.value),\n                                                                        className: \"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white text-sm\",\n                                                                        onClick: (e)=>e.stopPropagation()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-48 overflow-y-auto\",\n                                                            children: filteredSchedules.length > 0 ? filteredSchedules.map((schedule)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleInputChange(\"schedule_id\", schedule._id);\n                                                                        setShowScheduleDropdown(false);\n                                                                        setScheduleSearch('');\n                                                                    },\n                                                                    children: getScheduleDisplay(schedule)\n                                                                }, schedule._id, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 29\n                                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-3 py-2 text-gray-500 dark:text-gray-400 text-sm\",\n                                                                children: \"No schedules found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.schedule_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.schedule_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this),\n                                !isMultiMode ? // Single student selection with search\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: [\n                                                \"Student \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 29\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 \".concat(errors.student_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\", \" \").concat(!formData.schedule_id ? 'opacity-50 cursor-not-allowed' : ''),\n                                                    onClick: (e)=>{\n                                                        if (!formData.schedule_id) return;\n                                                        e.stopPropagation();\n                                                        setShowStudentDropdown(!showStudentDropdown);\n                                                        setShowScheduleDropdown(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900 dark:text-white\",\n                                                            children: getSelectedStudentName() || (formData.schedule_id ? \"Select student\" : \"Select class schedule first\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 21\n                                                }, this),\n                                                showStudentDropdown && formData.schedule_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 border-b border-gray-200 dark:border-gray-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        placeholder: \"Search students...\",\n                                                                        value: studentSearch,\n                                                                        onChange: (e)=>setStudentSearch(e.target.value),\n                                                                        className: \"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white text-sm\",\n                                                                        onClick: (e)=>e.stopPropagation()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-48 overflow-y-auto\",\n                                                            children: filteredStudentsForSearch.length > 0 ? filteredStudentsForSearch.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleInputChange(\"student_id\", student._id);\n                                                                        setShowStudentDropdown(false);\n                                                                        setStudentSearch('');\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium\",\n                                                                            children: [\n                                                                                student.first_name,\n                                                                                \" \",\n                                                                                student.last_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 458,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                            children: student.student_id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, student._id, true, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 31\n                                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-3 py-2 text-gray-500 dark:text-gray-400 text-sm\",\n                                                                children: \"No students found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 19\n                                        }, this),\n                                        errors.student_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.student_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 17\n                                }, this) : // Multi-student selection\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                    children: [\n                                                        \"Students (\",\n                                                        selectedStudents.size,\n                                                        \" selected)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: selectAllStudents,\n                                                            className: \"text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded\",\n                                                            disabled: !formData.schedule_id,\n                                                            children: \"Select All\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: deselectAllStudents,\n                                                            className: \"text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded\",\n                                                            children: \"Clear\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 19\n                                        }, this),\n                                        selectedStudents.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                        children: \"Apply to selected:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: bulkStatus,\n                                                                onChange: (e)=>setBulkStatus(e.target.value),\n                                                                className: \"px-2 py-1 pr-8 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white appearance-none\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Present\",\n                                                                        children: \"Present\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Absent\",\n                                                                        children: \"Absent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Late\",\n                                                                        children: \"Late\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 516,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Excused\",\n                                                                        children: \"Excused\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400 pointer-events-none\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: applyBulkStatus,\n                                                        className: \"px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600\",\n                                                        children: \"Apply\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md\",\n                                            children: filteredStudents.length > 0 ? filteredStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-600 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>toggleStudentSelection(student._id),\n                                                                    className: \"text-blue-500 hover:text-blue-600\",\n                                                                    children: selectedStudents.has(student._id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 547,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                            children: [\n                                                                                student.first_name,\n                                                                                \" \",\n                                                                                student.last_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                            children: student.student_id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        selectedStudents.has(student._id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: studentStatuses[student._id] || \"Present\",\n                                                            onChange: (e)=>updateStudentStatus(student._id, e.target.value),\n                                                            className: \"px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Present\",\n                                                                    children: \"Present\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Absent\",\n                                                                    children: \"Absent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Late\",\n                                                                    children: \"Late\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Excused\",\n                                                                    children: \"Excused\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, student._id, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 25\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center text-gray-500 dark:text-gray-400\",\n                                                children: formData.schedule_id ? \"No students found for this class\" : \"Please select a class schedule first\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 19\n                                        }, this),\n                                        errors.students && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.students\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 17\n                                }, this),\n                                !isMultiMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.status,\n                                            onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Present\",\n                                                    children: \"Present\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Absent\",\n                                                    children: \"Absent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Late\",\n                                                    children: \"Late\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Excused\",\n                                                    children: \"Excused\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Date\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.date,\n                                            onChange: (e)=>handleInputChange(\"date\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.date ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.date\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Academic Year\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.academic_year,\n                                            onChange: (e)=>handleInputChange(\"academic_year\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.academic_year ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            placeholder: \"e.g., 2024-2025\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.academic_year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.academic_year\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: onClose,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            disabled: isSubmitting,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                            children: [\n                                                isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isSubmitting ? \"Saving...\" : isEditing ? \"Update\" : isMultiMode ? \"Mark \".concat(selectedStudents.size, \" Students\") : \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n            lineNumber: 279,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n_s(AttendanceModal, \"ujAStAdb6hvNxzWAVhpwmsAnTao=\");\n_c = AttendanceModal;\nvar _c;\n$RefreshReg$(_c, \"AttendanceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/AttendanceModal.tsx\n"));

/***/ })

});