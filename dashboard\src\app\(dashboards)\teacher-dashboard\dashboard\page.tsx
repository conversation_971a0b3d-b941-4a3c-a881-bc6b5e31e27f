"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { LayoutDashboard, School, Users, BookOpen, Calendar, BarChart3, Clock } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";
import { motion } from "framer-motion";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

const navigation = {
  icon: LayoutDashboard,
  baseHref: "/teacher-dashboard/dashboard",
  title: "Teacher Dashboard"
};

export default function TeacherDashboardMainPage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    studentCount: 0,
    classCount: 0,
    todayScheduleCount: 0,
    pendingGrades: 0
  });

  useEffect(() => {
    // Check if user is a teacher
    if (user && user.role !== "teacher") {
      router.push("/dashboard");
      return;
    }

    // Get selected school from localStorage
    const storedSchool = localStorage.getItem("teacher_selected_school");
    if (storedSchool) {
      try {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
        // Load dashboard data
        loadDashboardData(school.school_id);
      } catch (error) {
        console.error("Error parsing stored school:", error);
        router.push("/teacher-dashboard");
      }
    } else {
      // No school selected, redirect to school selection
      router.push("/teacher-dashboard");
    }

    setLoading(false);
  }, [user, router]);

  const loadDashboardData = async (schoolId: string) => {
    try {
      const { getTeacherPermissions, getTeacherStudents } = await import("@/app/services/TeacherPermissionServices");
      const [teacherData, studentsData] = await Promise.all([
        getTeacherPermissions(schoolId),
        getTeacherStudents(schoolId)
      ]);

      setDashboardData({
        studentCount: studentsData.length,
        classCount: teacherData.assigned_classes.length,
        todayScheduleCount: 0, // TODO: Calculate from actual schedule
        pendingGrades: 0 // TODO: Calculate from actual grades
      });
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      setDashboardData({
        studentCount: 0,
        classCount: 0,
        todayScheduleCount: 0,
        pendingGrades: 0
      });
    }
  };

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const dashboardStats = [
    {
      title: "My Students",
      value: dashboardData.studentCount.toString(),
      icon: Users,
      color: "bg-blue-500",
      href: "/teacher-dashboard/students"
    },
    {
      title: "My Classes",
      value: dashboardData.classCount.toString(),
      icon: BookOpen,
      color: "bg-green-500",
      href: "/teacher-dashboard/classes"
    },
    {
      title: "Today's Schedule",
      value: dashboardData.todayScheduleCount.toString(),
      icon: Calendar,
      color: "bg-purple-500",
      href: "/teacher-dashboard/timetable"
    },
    {
      title: "Pending Grades",
      value: dashboardData.pendingGrades.toString(),
      icon: BarChart3,
      color: "bg-orange-500",
      href: "/teacher-dashboard/grades"
    }
  ];

  const quickActions = [
    {
      title: "Take Attendance",
      description: "Mark attendance for your classes",
      icon: Clock,
      href: "/teacher-dashboard/attendance",
      color: "bg-teal"
    },
    {
      title: "Enter Grades",
      description: "Add or update student grades",
      icon: BarChart3,
      href: "/teacher-dashboard/grades",
      color: "bg-blue-500"
    },
    {
      title: "View Schedule",
      description: "Check your teaching schedule",
      icon: Calendar,
      href: "/teacher-dashboard/timetable",
      color: "bg-purple-500"
    },
    {
      title: "Manage Resources",
      description: "Upload and organize teaching materials",
      icon: BookOpen,
      href: "/teacher-dashboard/resources",
      color: "bg-green-500"
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Welcome Section */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-teal rounded-full flex items-center justify-center">
                <School className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">
                  Welcome back, {user?.first_name || user?.name}!
                </h1>
                <p className="text-foreground/60">
                  {selectedSchool?.school_name} • Teacher Dashboard
                </p>
              </div>
            </div>
            
            <div className="bg-teal/10 border border-teal/20 rounded-lg p-4">
              <p className="text-sm text-foreground/70">
                <strong>Current School:</strong> {selectedSchool?.school_name}
              </p>
              <p className="text-sm text-foreground/70">
                <strong>Access Granted:</strong> {selectedSchool ? new Date(selectedSchool.access_granted_at).toLocaleDateString() : 'N/A'}
              </p>
            </div>
          </div>

          {/* Dashboard Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {dashboardStats.map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                onClick={() => router.push(stat.href)}
                className="bg-widget rounded-lg border border-stroke p-6 cursor-pointer hover:shadow-lg transition-all"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-foreground/60">{stat.title}</p>
                    <p className="text-2xl font-bold text-foreground">{stat.value}</p>
                  </div>
                  <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <h2 className="text-lg font-semibold text-foreground mb-4">Quick Actions</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <motion.div
                  key={action.title}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  onClick={() => router.push(action.href)}
                  className="flex items-center space-x-4 p-4 border border-stroke rounded-lg cursor-pointer hover:border-teal/50 hover:bg-teal/5 transition-all"
                >
                  <div className={`w-10 h-10 ${action.color} rounded-lg flex items-center justify-center`}>
                    <action.icon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium text-foreground">{action.title}</h3>
                    <p className="text-sm text-foreground/60">{action.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <h2 className="text-lg font-semibold text-foreground mb-4">Recent Activity</h2>
            
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
              <p className="text-foreground/60">No recent activity</p>
              <p className="text-sm text-foreground/50">
                Your recent teaching activities will appear here
              </p>
            </div>
          </div>

          {/* Today's Schedule Preview */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-foreground">Today's Schedule</h2>
              <button
                onClick={() => router.push("/teacher-dashboard/timetable")}
                className="text-sm text-teal hover:text-teal-600 font-medium"
              >
                View Full Schedule
              </button>
            </div>
            
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
              <p className="text-foreground/60">No classes scheduled for today</p>
              <p className="text-sm text-foreground/50">
                Your daily schedule will appear here
              </p>
            </div>
          </div>
        </div>
      </TeacherLayout>
    </ProtectedRoute>
  );
}
