"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/attendance/page",{

/***/ "(app-pages-browser)/./src/components/modals/AttendanceModal.tsx":
/*!***************************************************!*\
  !*** ./src/components/modals/AttendanceModal.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttendanceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckSquare_FileCheck2_Save_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,FileCheck2,Save,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_FileCheck2_Save_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,FileCheck2,Save,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_FileCheck2_Save_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,FileCheck2,Save,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_FileCheck2_Save_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,FileCheck2,Save,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_FileCheck2_Save_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,FileCheck2,Save,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_FileCheck2_Save_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,FileCheck2,Save,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AttendanceModal(param) {\n    let { isOpen, onClose, onSubmit, attendance, students, classes, subjects, schedules, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id: \"\",\n        schedule_id: \"\",\n        status: \"Present\",\n        date: new Date().toISOString().split('T')[0],\n        academic_year: \"2024-2025\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredStudents, setFilteredStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Multi-selection states\n    const [isMultiMode, setIsMultiMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedStudents, setSelectedStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [studentStatuses, setStudentStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [bulkStatus, setBulkStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Present\");\n    // Search states\n    const [scheduleSearch, setScheduleSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [studentSearch, setStudentSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showScheduleDropdown, setShowScheduleDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStudentDropdown, setShowStudentDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isEditing = !!attendance;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (attendance) {\n                    // Single edit mode\n                    setIsMultiMode(false);\n                    setFormData({\n                        student_id: attendance.student_id || \"\",\n                        schedule_id: attendance.schedule_id || \"\",\n                        status: attendance.status || \"Present\",\n                        date: attendance.date ? new Date(attendance.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n                        academic_year: attendance.academic_year || \"2024-2025\"\n                    });\n                } else {\n                    // New attendance - default to multi mode\n                    setIsMultiMode(true);\n                    setFormData({\n                        student_id: \"\",\n                        schedule_id: \"\",\n                        status: \"Present\",\n                        date: new Date().toISOString().split('T')[0],\n                        academic_year: \"2024-2025\"\n                    });\n                }\n                setErrors({});\n                setSelectedStudents(new Set());\n                setStudentStatuses({});\n                setBulkStatus(\"Present\");\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        isOpen,\n        attendance\n    ]);\n    // Filter students based on selected schedule\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (formData.schedule_id) {\n                const selectedSchedule = schedules.find({\n                    \"AttendanceModal.useEffect.selectedSchedule\": (s)=>s._id === formData.schedule_id\n                }[\"AttendanceModal.useEffect.selectedSchedule\"]);\n                if (selectedSchedule) {\n                    const classStudents = students.filter({\n                        \"AttendanceModal.useEffect.classStudents\": (student)=>student.class_id === selectedSchedule.class_id\n                    }[\"AttendanceModal.useEffect.classStudents\"]);\n                    setFilteredStudents(classStudents);\n                    // Initialize student statuses for multi-mode\n                    if (isMultiMode) {\n                        const initialStatuses = {};\n                        classStudents.forEach({\n                            \"AttendanceModal.useEffect\": (student)=>{\n                                initialStatuses[student._id] = \"Present\";\n                            }\n                        }[\"AttendanceModal.useEffect\"]);\n                        setStudentStatuses(initialStatuses);\n                    }\n                }\n            } else {\n                setFilteredStudents(students);\n                if (isMultiMode) {\n                    const initialStatuses = {};\n                    students.forEach({\n                        \"AttendanceModal.useEffect\": (student)=>{\n                            initialStatuses[student._id] = \"Present\";\n                        }\n                    }[\"AttendanceModal.useEffect\"]);\n                    setStudentStatuses(initialStatuses);\n                }\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        formData.schedule_id,\n        schedules,\n        students,\n        isMultiMode\n    ]);\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"AttendanceModal.useEffect.handleClickOutside\": ()=>{\n                    setShowScheduleDropdown(false);\n                    setShowStudentDropdown(false);\n                }\n            }[\"AttendanceModal.useEffect.handleClickOutside\"];\n            if (showScheduleDropdown || showStudentDropdown) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"AttendanceModal.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"AttendanceModal.useEffect\"];\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        showScheduleDropdown,\n        showStudentDropdown\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!isMultiMode && !formData.student_id) {\n            newErrors.student_id = \"Student is required\";\n        }\n        if (isMultiMode && selectedStudents.size === 0) {\n            newErrors.students = \"At least one student must be selected\";\n        }\n        if (!formData.schedule_id) {\n            newErrors.schedule_id = \"Class schedule is required\";\n        }\n        if (!formData.date) {\n            newErrors.date = \"Date is required\";\n        }\n        if (!formData.academic_year) {\n            newErrors.academic_year = \"Academic year is required\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsSubmitting(true);\n        try {\n            if (isMultiMode) {\n                // Submit multiple attendance records\n                const attendancePromises = Array.from(selectedStudents).map((studentId)=>{\n                    const studentStatus = studentStatuses[studentId] || \"Present\";\n                    return onSubmit({\n                        ...formData,\n                        student_id: studentId,\n                        status: studentStatus\n                    });\n                });\n                await Promise.all(attendancePromises);\n            } else {\n                // Submit single attendance record\n                await onSubmit(formData);\n            }\n            onClose();\n        } catch (error) {\n            console.error(\"Error submitting attendance:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    // Multi-selection functions\n    const toggleStudentSelection = (studentId)=>{\n        const newSelected = new Set(selectedStudents);\n        if (newSelected.has(studentId)) {\n            newSelected.delete(studentId);\n        } else {\n            newSelected.add(studentId);\n        }\n        setSelectedStudents(newSelected);\n    };\n    const selectAllStudents = ()=>{\n        const allStudentIds = new Set(filteredStudents.map((s)=>s._id));\n        setSelectedStudents(allStudentIds);\n    };\n    const deselectAllStudents = ()=>{\n        setSelectedStudents(new Set());\n    };\n    const applyBulkStatus = ()=>{\n        const newStatuses = {\n            ...studentStatuses\n        };\n        selectedStudents.forEach((studentId)=>{\n            newStatuses[studentId] = bulkStatus;\n        });\n        setStudentStatuses(newStatuses);\n    };\n    const updateStudentStatus = (studentId, status)=>{\n        setStudentStatuses((prev)=>({\n                ...prev,\n                [studentId]: status\n            }));\n    };\n    const getScheduleDisplay = (schedule)=>{\n        const subject = subjects.find((s)=>s._id === schedule.subject_id);\n        const classInfo = classes.find((c)=>c._id === schedule.class_id);\n        return \"\".concat((subject === null || subject === void 0 ? void 0 : subject.name) || 'Unknown Subject', \" - \").concat((classInfo === null || classInfo === void 0 ? void 0 : classInfo.name) || 'Unknown Class', \" (\").concat(schedule.day_of_week, \")\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-black bg-opacity-50\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full mx-4 \".concat(isMultiMode ? 'max-w-4xl' : 'max-w-md'),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-lg flex items-center justify-center \".concat(isMultiMode ? 'bg-green-500' : 'bg-blue-500'),\n                                            children: isMultiMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_FileCheck2_Save_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 34\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_FileCheck2_Save_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 77\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                    children: isEditing ? \"Edit Attendance\" : isMultiMode ? \"Mark Multiple Attendance\" : \"Mark Attendance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: isEditing ? \"Update attendance record\" : isMultiMode ? \"Mark attendance for multiple students\" : \"Create new attendance record\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        !isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMultiMode(!isMultiMode),\n                                            className: \"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600\",\n                                            children: isMultiMode ? 'Single Mode' : 'Multi Mode'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_FileCheck2_Save_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"p-6 space-y-4 \".concat(isMultiMode ? 'max-h-96 overflow-y-auto' : ''),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Class Schedule\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.schedule_id,\n                                            onChange: (e)=>handleInputChange(\"schedule_id\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.schedule_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select class schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this),\n                                                schedules.map((schedule)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: schedule._id,\n                                                        children: getScheduleDisplay(schedule)\n                                                    }, schedule._id, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.schedule_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.schedule_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this),\n                                !isMultiMode ? // Single student selection\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Student\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.student_id,\n                                            onChange: (e)=>handleInputChange(\"student_id\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.student_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            disabled: !formData.schedule_id,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select student\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 21\n                                                }, this),\n                                                filteredStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: student._id,\n                                                        children: [\n                                                            student.first_name,\n                                                            \" \",\n                                                            student.last_name,\n                                                            \" (\",\n                                                            student.student_id,\n                                                            \")\"\n                                                        ]\n                                                    }, student._id, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 19\n                                        }, this),\n                                        errors.student_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.student_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 17\n                                }, this) : // Multi-student selection\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                    children: [\n                                                        \"Students (\",\n                                                        selectedStudents.size,\n                                                        \" selected)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: selectAllStudents,\n                                                            className: \"text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded\",\n                                                            disabled: !formData.schedule_id,\n                                                            children: \"Select All\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: deselectAllStudents,\n                                                            className: \"text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded\",\n                                                            children: \"Clear\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 19\n                                        }, this),\n                                        selectedStudents.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                        children: \"Apply to selected:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: bulkStatus,\n                                                        onChange: (e)=>setBulkStatus(e.target.value),\n                                                        className: \"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Present\",\n                                                                children: \"Present\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Absent\",\n                                                                children: \"Absent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Late\",\n                                                                children: \"Late\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Excused\",\n                                                                children: \"Excused\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: applyBulkStatus,\n                                                        className: \"px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600\",\n                                                        children: \"Apply\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md\",\n                                            children: filteredStudents.length > 0 ? filteredStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-600 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>toggleStudentSelection(student._id),\n                                                                    className: \"text-blue-500 hover:text-blue-600\",\n                                                                    children: selectedStudents.has(student._id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_FileCheck2_Save_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_FileCheck2_Save_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                            children: [\n                                                                                student.first_name,\n                                                                                \" \",\n                                                                                student.last_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                            children: student.student_id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        selectedStudents.has(student._id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: studentStatuses[student._id] || \"Present\",\n                                                            onChange: (e)=>updateStudentStatus(student._id, e.target.value),\n                                                            className: \"px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Present\",\n                                                                    children: \"Present\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Absent\",\n                                                                    children: \"Absent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Late\",\n                                                                    children: \"Late\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Excused\",\n                                                                    children: \"Excused\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, student._id, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 25\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center text-gray-500 dark:text-gray-400\",\n                                                children: formData.schedule_id ? \"No students found for this class\" : \"Please select a class schedule first\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 19\n                                        }, this),\n                                        errors.students && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.students\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 17\n                                }, this),\n                                !isMultiMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.status,\n                                            onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Present\",\n                                                    children: \"Present\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Absent\",\n                                                    children: \"Absent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Late\",\n                                                    children: \"Late\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Excused\",\n                                                    children: \"Excused\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Date\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.date,\n                                            onChange: (e)=>handleInputChange(\"date\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.date ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.date\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Academic Year\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.academic_year,\n                                            onChange: (e)=>handleInputChange(\"academic_year\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.academic_year ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            placeholder: \"e.g., 2024-2025\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.academic_year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.academic_year\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: onClose,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            disabled: isSubmitting,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                            children: [\n                                                isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_FileCheck2_Save_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isSubmitting ? \"Saving...\" : isEditing ? \"Update\" : isMultiMode ? \"Mark \".concat(selectedStudents.size, \" Students\") : \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n            lineNumber: 234,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n        lineNumber: 232,\n        columnNumber: 5\n    }, this);\n}\n_s(AttendanceModal, \"ujAStAdb6hvNxzWAVhpwmsAnTao=\");\n_c = AttendanceModal;\nvar _c;\n$RefreshReg$(_c, \"AttendanceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/AttendanceModal.tsx\n"));

/***/ })

});