"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/staff/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/staff/components/CreateStaffModal.tsx":
/*!*********************************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/staff/components/CreateStaffModal.tsx ***!
  \*********************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateStaffModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Search,UserCheck,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Search,UserCheck,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Search,UserCheck,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Search,UserCheck,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Search,UserCheck,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Search,UserCheck,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _app_services_StaffServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/StaffServices */ \"(app-pages-browser)/./src/app/services/StaffServices.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CreateStaffModal(param) {\n    let { onClose, onSave, initialData, schoolId } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"role_selection\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTeacher, setSelectedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        first_name: \"\",\n        last_name: \"\",\n        email: \"\",\n        phone: \"\",\n        role_template: \"teacher\",\n        school_id: schoolId,\n        permissions: undefined,\n        assigned_classes: [],\n        is_existing_teacher: false,\n        teacher_id: undefined\n    });\n    // Populate form with initial data if editing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateStaffModal.useEffect\": ()=>{\n            if (initialData) {\n                var _initialData_permissions, _initialData_permissions1, _initialData_permissions2;\n                setFormData({\n                    first_name: initialData.first_name,\n                    last_name: initialData.last_name,\n                    email: initialData.email,\n                    phone: initialData.phone || \"\",\n                    role_template: ((_initialData_permissions = initialData.permissions) === null || _initialData_permissions === void 0 ? void 0 : _initialData_permissions.role_template) || \"custom\",\n                    school_id: schoolId,\n                    permissions: (_initialData_permissions1 = initialData.permissions) === null || _initialData_permissions1 === void 0 ? void 0 : _initialData_permissions1.permissions,\n                    assigned_classes: ((_initialData_permissions2 = initialData.permissions) === null || _initialData_permissions2 === void 0 ? void 0 : _initialData_permissions2.assigned_classes) || [],\n                    is_existing_teacher: false\n                });\n                setCurrentStep(\"personal_info\");\n            }\n        }\n    }[\"CreateStaffModal.useEffect\"], [\n        initialData,\n        schoolId\n    ]);\n    // Initialize permissions when role changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateStaffModal.useEffect\": ()=>{\n            if (formData.role_template && !initialData && !formData.permissions) {\n                const defaultPermissions = getDefaultPermissionsForRole(formData.role_template);\n                setFormData({\n                    \"CreateStaffModal.useEffect\": (prev)=>({\n                            ...prev,\n                            permissions: defaultPermissions\n                        })\n                }[\"CreateStaffModal.useEffect\"]);\n            }\n        }\n    }[\"CreateStaffModal.useEffect\"], [\n        formData.role_template,\n        initialData,\n        formData.permissions\n    ]);\n    // Handle teacher search\n    const handleTeacherSearch = async (query)=>{\n        if (query.length < 2) {\n            setSearchResults([]);\n            return;\n        }\n        setIsSearching(true);\n        try {\n            const results = await (0,_app_services_StaffServices__WEBPACK_IMPORTED_MODULE_2__.searchTeachers)(query);\n            setSearchResults(results);\n        } catch (error) {\n            console.error(\"Error searching teachers:\", error);\n            setSearchResults([]);\n        } finally{\n            setIsSearching(false);\n        }\n    };\n    // Handle teacher selection\n    const handleTeacherSelect = (teacher)=>{\n        setSelectedTeacher(teacher);\n        setFormData((prev)=>({\n                ...prev,\n                first_name: teacher.first_name,\n                last_name: teacher.last_name,\n                email: teacher.email,\n                phone: teacher.phone || \"\",\n                is_existing_teacher: true,\n                teacher_id: teacher._id\n            }));\n        setCurrentStep(\"permissions\");\n    };\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        setIsSubmitting(true);\n        try {\n            if (initialData) {\n                // Update existing staff\n                const result = await (0,_app_services_StaffServices__WEBPACK_IMPORTED_MODULE_2__.updateStaff)(initialData.staff_id || initialData._id, {\n                    first_name: formData.first_name,\n                    last_name: formData.last_name,\n                    email: formData.email,\n                    phone: formData.phone,\n                    role_template: formData.role_template,\n                    permissions: formData.permissions,\n                    assigned_classes: formData.assigned_classes,\n                    school_id: schoolId\n                });\n                onSave(result.user, true);\n            } else {\n                // Create new staff\n                const result = await (0,_app_services_StaffServices__WEBPACK_IMPORTED_MODULE_2__.createStaff)(formData);\n                onSave(result.user, false);\n            }\n            onClose();\n        } catch (error) {\n            console.error(\"Error saving staff:\", error);\n            alert(\"Failed to save staff member. Please try again.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Handle input change\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Get default permissions for a role\n    const getDefaultPermissionsForRole = (role)=>{\n        const defaultPermissions = {\n            school_admin: {\n                students: {\n                    view_all_students: true,\n                    add_edit_delete_students: true,\n                    generate_id_cards: true,\n                    generate_report_cards: true\n                },\n                academic_records: {\n                    view_grades_assigned_classes: true,\n                    enter_edit_grades_assigned_classes: true,\n                    view_all_school_grades: true,\n                    take_attendance_assigned_classes: true,\n                    view_all_attendance: true\n                },\n                staff: {\n                    view_staff_list: true,\n                    add_edit_delete_staff: true,\n                    manage_staff_permissions: true,\n                    reset_staff_passwords: true\n                },\n                announcements: {\n                    view_announcements: true,\n                    create_edit_announcements: true,\n                    delete_announcements: true,\n                    publish_announcements: true\n                }\n            },\n            teacher: {\n                students: {\n                    view_all_students: true,\n                    add_edit_delete_students: false,\n                    generate_id_cards: false,\n                    generate_report_cards: true\n                },\n                academic_records: {\n                    view_grades_assigned_classes: true,\n                    enter_edit_grades_assigned_classes: true,\n                    view_all_school_grades: false,\n                    take_attendance_assigned_classes: true,\n                    view_all_attendance: false\n                },\n                staff: {\n                    view_staff_list: true,\n                    add_edit_delete_staff: false,\n                    manage_staff_permissions: false,\n                    reset_staff_passwords: false\n                },\n                announcements: {\n                    view_announcements: true,\n                    create_edit_announcements: false,\n                    delete_announcements: false,\n                    publish_announcements: false\n                }\n            },\n            bursar: {\n                students: {\n                    view_all_students: true,\n                    add_edit_delete_students: false,\n                    generate_id_cards: true,\n                    generate_report_cards: false\n                },\n                academic_records: {\n                    view_grades_assigned_classes: false,\n                    enter_edit_grades_assigned_classes: false,\n                    view_all_school_grades: false,\n                    take_attendance_assigned_classes: false,\n                    view_all_attendance: false\n                },\n                staff: {\n                    view_staff_list: true,\n                    add_edit_delete_staff: false,\n                    manage_staff_permissions: false,\n                    reset_staff_passwords: false\n                },\n                announcements: {\n                    view_announcements: true,\n                    create_edit_announcements: false,\n                    delete_announcements: false,\n                    publish_announcements: false\n                }\n            },\n            dean_of_studies: {\n                students: {\n                    view_all_students: true,\n                    add_edit_delete_students: true,\n                    generate_id_cards: true,\n                    generate_report_cards: true\n                },\n                academic_records: {\n                    view_grades_assigned_classes: true,\n                    enter_edit_grades_assigned_classes: true,\n                    view_all_school_grades: true,\n                    take_attendance_assigned_classes: true,\n                    view_all_attendance: true\n                },\n                staff: {\n                    view_staff_list: true,\n                    add_edit_delete_staff: false,\n                    manage_staff_permissions: false,\n                    reset_staff_passwords: false\n                },\n                announcements: {\n                    view_announcements: true,\n                    create_edit_announcements: true,\n                    delete_announcements: false,\n                    publish_announcements: true\n                }\n            }\n        };\n        return defaultPermissions[role] || {};\n    };\n    // Get step title\n    const getStepTitle = ()=>{\n        switch(currentStep){\n            case \"role_selection\":\n                return \"Select Staff Type\";\n            case \"teacher_search\":\n                return \"Search for Teacher\";\n            case \"personal_info\":\n                return \"Personal Information\";\n            case \"permissions\":\n                return \"Role & Permissions\";\n            case \"assignments\":\n                return \"Class Assignments\";\n            default:\n                return \"Add Staff Member\";\n        }\n    };\n    // Handle next step\n    const handleNext = ()=>{\n        switch(currentStep){\n            case \"role_selection\":\n                if (formData.role_template === \"teacher\") {\n                    setCurrentStep(\"teacher_search\");\n                } else {\n                    setCurrentStep(\"personal_info\");\n                }\n                break;\n            case \"teacher_search\":\n                if (selectedTeacher) {\n                    setCurrentStep(\"permissions\");\n                } else {\n                    setCurrentStep(\"personal_info\");\n                }\n                break;\n            case \"personal_info\":\n                setCurrentStep(\"permissions\");\n                break;\n            case \"permissions\":\n                var _formData_assigned_classes;\n                if (formData.role_template === \"teacher\" && ((_formData_assigned_classes = formData.assigned_classes) === null || _formData_assigned_classes === void 0 ? void 0 : _formData_assigned_classes.length) === 0) {\n                    setCurrentStep(\"assignments\");\n                } else {\n                    handleSubmit();\n                }\n                break;\n            case \"assignments\":\n                handleSubmit();\n                break;\n        }\n    };\n    // Handle previous step\n    const handlePrevious = ()=>{\n        switch(currentStep){\n            case \"teacher_search\":\n                setCurrentStep(\"role_selection\");\n                break;\n            case \"personal_info\":\n                if (formData.role_template === \"teacher\") {\n                    setCurrentStep(\"teacher_search\");\n                } else {\n                    setCurrentStep(\"role_selection\");\n                }\n                break;\n            case \"permissions\":\n                setCurrentStep(\"personal_info\");\n                break;\n            case \"assignments\":\n                setCurrentStep(\"permissions\");\n                break;\n        }\n    };\n    // Check if current step is valid\n    const isStepValid = ()=>{\n        switch(currentStep){\n            case \"role_selection\":\n                return formData.role_template !== \"\";\n            case \"teacher_search\":\n                return true; // Always allow to proceed, whether teacher is found or not\n            case \"personal_info\":\n                return formData.first_name && formData.last_name && formData.email;\n            case \"permissions\":\n                return true; // Always valid, permissions are optional\n            case \"assignments\":\n                return true; // Always valid, assignments are optional\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-h-svh overflow-y-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-4xl mx-4 sm:mx-6 md:mx-0 p-6 relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-foreground\",\n                            children: initialData ? \"Edit Staff Member\" : getStepTitle()\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-[400px]\",\n                    children: [\n                        currentStep === \"role_selection\" && !initialData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-foreground/70\",\n                                    children: \"First, select the type of staff member you want to add:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            onClick: ()=>handleInputChange(\"role_template\", \"teacher\"),\n                                            className: \"p-4 border-2 rounded-lg cursor-pointer transition-all \".concat(formData.role_template === \"teacher\" ? \"border-teal bg-teal/10\" : \"border-stroke hover:border-teal/50\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-8 w-8 text-teal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: \"Teacher\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-foreground/60\",\n                                                                children: \"Can teach multiple schools and needs access codes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 17\n                                        }, this),\n                                        [\n                                            \"school_admin\",\n                                            \"bursar\",\n                                            \"dean_of_studies\"\n                                        ].map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                whileHover: {\n                                                    scale: 1.02\n                                                },\n                                                whileTap: {\n                                                    scale: 0.98\n                                                },\n                                                onClick: ()=>handleInputChange(\"role_template\", role),\n                                                className: \"p-4 border-2 rounded-lg cursor-pointer transition-all \".concat(formData.role_template === role ? \"border-teal bg-teal/10\" : \"border-stroke hover:border-teal/50\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-8 w-8 text-teal\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-foreground\",\n                                                                    children: role === \"school_admin\" ? \"School Admin\" : role === \"dean_of_studies\" ? \"Dean of Studies\" : \"Bursar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-foreground/60\",\n                                                                    children: role === \"school_admin\" ? \"Full administrative access to school\" : role === \"dean_of_studies\" ? \"Academic oversight and management\" : \"Financial management and operations\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, role, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this),\n                        currentStep === \"teacher_search\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-foreground/70 mb-4\",\n                                            children: \"Search for an existing teacher or create a new one:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/40\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search by name, email, or phone...\",\n                                                    value: searchQuery,\n                                                    onChange: (e)=>{\n                                                        setSearchQuery(e.target.value);\n                                                        handleTeacherSearch(e.target.value);\n                                                    },\n                                                    className: \"w-full pl-10 pr-4 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isSearching && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        size: 16,\n                                                        color: \"teal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, this),\n                                searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-foreground\",\n                                            children: \"Existing Teachers:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-60 overflow-y-auto space-y-2\",\n                                            children: searchResults.map((teacher)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.01\n                                                    },\n                                                    onClick: ()=>handleTeacherSelect(teacher),\n                                                    className: \"p-3 border rounded-lg cursor-pointer transition-all \".concat((selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher._id) === teacher._id ? \"border-teal bg-teal/10\" : \"border-stroke hover:border-teal/50\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-foreground\",\n                                                                        children: [\n                                                                            teacher.first_name,\n                                                                            \" \",\n                                                                            teacher.last_name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-foreground/60\",\n                                                                        children: teacher.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                                        lineNumber: 485,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    teacher.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-foreground/60\",\n                                                                        children: teacher.phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            (selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher._id) === teacher._id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-5 w-5 text-teal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, teacher._id, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 17\n                                }, this),\n                                searchQuery.length >= 2 && searchResults.length === 0 && !isSearching && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-12 w-12 text-foreground/30 mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-foreground/60\",\n                                            children: \"No teachers found. Continue to create a new teacher.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 13\n                        }, this),\n                        currentStep === \"personal_info\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-foreground\",\n                                                    children: [\n                                                        \"First Name \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 32\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.first_name,\n                                                    onChange: (e)=>handleInputChange(\"first_name\", e.target.value),\n                                                    placeholder: \"Enter first name\",\n                                                    required: true,\n                                                    className: \"w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-foreground\",\n                                                    children: [\n                                                        \"Last Name \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.last_name,\n                                                    onChange: (e)=>handleInputChange(\"last_name\", e.target.value),\n                                                    placeholder: \"Enter last name\",\n                                                    required: true,\n                                                    className: \"w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-foreground\",\n                                            children: [\n                                                \"Email \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: formData.email,\n                                            onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                            placeholder: \"Enter email address\",\n                                            required: true,\n                                            className: \"w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-foreground\",\n                                            children: \"Phone Number\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"tel\",\n                                            value: formData.phone,\n                                            onChange: (e)=>handleInputChange(\"phone\", e.target.value),\n                                            placeholder: \"Enter phone number\",\n                                            className: \"w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 13\n                        }, this),\n                        currentStep === \"permissions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-foreground mb-2\",\n                                            children: \"Role & Permissions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-foreground/60\",\n                                            children: \"Configure the permissions for this staff member. You can customize individual permissions or use role defaults.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-foreground/70\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Selected Role:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            formData.role_template === \"school_admin\" ? \"School Admin\" : formData.role_template === \"dean_of_studies\" ? \"Dean of Studies\" : formData.role_template.charAt(0).toUpperCase() + formData.role_template.slice(1)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 max-h-96 overflow-y-auto\",\n                                    children: [\n                                        {\n                                            category: 'students',\n                                            title: 'Students Management',\n                                            permissions: [\n                                                {\n                                                    key: 'view_all_students',\n                                                    label: 'View all students'\n                                                },\n                                                {\n                                                    key: 'add_edit_delete_students',\n                                                    label: 'Add, edit & delete students'\n                                                },\n                                                {\n                                                    key: 'generate_id_cards',\n                                                    label: 'Generate ID cards'\n                                                },\n                                                {\n                                                    key: 'generate_report_cards',\n                                                    label: 'Generate report cards'\n                                                }\n                                            ]\n                                        },\n                                        {\n                                            category: 'academic_records',\n                                            title: 'Academic Records',\n                                            permissions: [\n                                                {\n                                                    key: 'view_grades_assigned_classes',\n                                                    label: 'View grades (assigned classes)'\n                                                },\n                                                {\n                                                    key: 'enter_edit_grades_assigned_classes',\n                                                    label: 'Enter/edit grades (assigned classes)'\n                                                },\n                                                {\n                                                    key: 'view_all_school_grades',\n                                                    label: 'View all school grades'\n                                                },\n                                                {\n                                                    key: 'take_attendance_assigned_classes',\n                                                    label: 'Take attendance (assigned classes)'\n                                                },\n                                                {\n                                                    key: 'view_all_attendance',\n                                                    label: 'View all attendance records'\n                                                }\n                                            ]\n                                        },\n                                        {\n                                            category: 'staff',\n                                            title: 'Staff Management',\n                                            permissions: [\n                                                {\n                                                    key: 'view_staff_list',\n                                                    label: 'View staff list'\n                                                },\n                                                {\n                                                    key: 'add_edit_delete_staff',\n                                                    label: 'Add, edit & delete staff'\n                                                },\n                                                {\n                                                    key: 'manage_staff_permissions',\n                                                    label: 'Manage staff permissions'\n                                                },\n                                                {\n                                                    key: 'reset_staff_passwords',\n                                                    label: 'Reset staff passwords'\n                                                }\n                                            ]\n                                        },\n                                        {\n                                            category: 'announcements',\n                                            title: 'Announcements',\n                                            permissions: [\n                                                {\n                                                    key: 'view_announcements',\n                                                    label: 'View announcements'\n                                                },\n                                                {\n                                                    key: 'create_edit_announcements',\n                                                    label: 'Create & edit announcements'\n                                                },\n                                                {\n                                                    key: 'delete_announcements',\n                                                    label: 'Delete announcements'\n                                                },\n                                                {\n                                                    key: 'publish_announcements',\n                                                    label: 'Publish announcements'\n                                                }\n                                            ]\n                                        }\n                                    ].map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-stroke rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-foreground mb-3\",\n                                                    children: section.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: section.permissions.map((permission)=>{\n                                                        var _formData_permissions_section_category, _formData_permissions;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center space-x-2 cursor-pointer\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: ((_formData_permissions = formData.permissions) === null || _formData_permissions === void 0 ? void 0 : (_formData_permissions_section_category = _formData_permissions[section.category]) === null || _formData_permissions_section_category === void 0 ? void 0 : _formData_permissions_section_category[permission.key]) || false,\n                                                                    onChange: (e)=>{\n                                                                        var _formData_permissions;\n                                                                        const newPermissions = {\n                                                                            ...formData.permissions,\n                                                                            [section.category]: {\n                                                                                ...(_formData_permissions = formData.permissions) === null || _formData_permissions === void 0 ? void 0 : _formData_permissions[section.category],\n                                                                                [permission.key]: e.target.checked\n                                                                            }\n                                                                        };\n                                                                        handleInputChange(\"permissions\", newPermissions);\n                                                                    },\n                                                                    className: \"w-4 h-4 text-teal border-gray-300 rounded focus:ring-teal focus:ring-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-foreground\",\n                                                                    children: permission.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, permission.key, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, section.category, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                // Apply default permissions for role\n                                                const defaultPerms = getDefaultPermissionsForRole(formData.role_template);\n                                                handleInputChange(\"permissions\", defaultPerms);\n                                            },\n                                            className: \"px-3 py-2 text-sm bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: \"Apply Role Defaults\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>handleInputChange(\"permissions\", {}),\n                                            className: \"px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-foreground rounded-md hover:bg-gray-200 dark:hover:bg-gray-600\",\n                                            children: \"Clear All\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                            lineNumber: 573,\n                            columnNumber: 13\n                        }, this),\n                        currentStep === \"assignments\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-foreground mb-2\",\n                                            children: \"Class Assignments\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-foreground/60\",\n                                            children: \"Assign classes and subjects to this teacher. You can add assignments later.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-foreground/70\",\n                                        children: \"Class assignments can be configured after the teacher is created through the staff management interface.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                            lineNumber: 690,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mt-6 pt-4 border-t border-stroke\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: handlePrevious,\n                            disabled: currentStep === \"role_selection\" || currentStep === \"personal_info\" && initialData,\n                            className: \"flex items-center space-x-2 px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                            lineNumber: 709,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600\",\n                                    disabled: isSubmitting,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    onClick: handleNext,\n                                    disabled: !isStepValid() || isSubmitting,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: [\n                                        isSubmitting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            size: 16,\n                                            color: \"white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 32\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: currentStep === \"assignments\" || currentStep === \"permissions\" && formData.role_template !== \"teacher\" ? initialData ? \"Update\" : \"Create\" : \"Next\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isSubmitting && currentStep !== \"assignments\" && !(currentStep === \"permissions\" && formData.role_template !== \"teacher\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Search_UserCheck_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                            lineNumber: 719,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n                    lineNumber: 708,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n            lineNumber: 362,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\staff\\\\components\\\\CreateStaffModal.tsx\",\n        lineNumber: 361,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateStaffModal, \"7EliUxKCU0MbGfzKweTRZ9i9B2c=\");\n_c = CreateStaffModal;\nvar _c;\n$RefreshReg$(_c, \"CreateStaffModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/staff/components/CreateStaffModal.tsx\n"));

/***/ })

});