"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/attendance/page",{

/***/ "(app-pages-browser)/./src/components/modals/AttendanceModal.tsx":
/*!***************************************************!*\
  !*** ./src/components/modals/AttendanceModal.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttendanceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,ChevronDown,FileCheck2,Save,Search,Square,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AttendanceModal(param) {\n    let { isOpen, onClose, onSubmit, attendance, students, classes, subjects, schedules, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id: \"\",\n        schedule_id: \"\",\n        status: \"Present\",\n        date: new Date().toISOString().split('T')[0],\n        academic_year: \"2024-2025\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredStudents, setFilteredStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Multi-selection states\n    const [isMultiMode, setIsMultiMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedStudents, setSelectedStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [studentStatuses, setStudentStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [bulkStatus, setBulkStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Present\");\n    // Search states\n    const [scheduleSearch, setScheduleSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [studentSearch, setStudentSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showScheduleDropdown, setShowScheduleDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStudentDropdown, setShowStudentDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isEditing = !!attendance;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (attendance) {\n                    // Single edit mode\n                    setIsMultiMode(false);\n                    setFormData({\n                        student_id: attendance.student_id || \"\",\n                        schedule_id: attendance.schedule_id || \"\",\n                        status: attendance.status || \"Present\",\n                        date: attendance.date ? new Date(attendance.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n                        academic_year: attendance.academic_year || \"2024-2025\"\n                    });\n                } else {\n                    // New attendance - default to multi mode\n                    setIsMultiMode(true);\n                    setFormData({\n                        student_id: \"\",\n                        schedule_id: \"\",\n                        status: \"Present\",\n                        date: new Date().toISOString().split('T')[0],\n                        academic_year: \"2024-2025\"\n                    });\n                }\n                setErrors({});\n                setSelectedStudents(new Set());\n                setStudentStatuses({});\n                setBulkStatus(\"Present\");\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        isOpen,\n        attendance\n    ]);\n    // Filter students based on selected schedule\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (formData.schedule_id) {\n                const selectedSchedule = schedules.find({\n                    \"AttendanceModal.useEffect.selectedSchedule\": (s)=>s._id === formData.schedule_id\n                }[\"AttendanceModal.useEffect.selectedSchedule\"]);\n                if (selectedSchedule) {\n                    const classStudents = students.filter({\n                        \"AttendanceModal.useEffect.classStudents\": (student)=>student.class_id === selectedSchedule.class_id\n                    }[\"AttendanceModal.useEffect.classStudents\"]);\n                    setFilteredStudents(classStudents);\n                    // Initialize student statuses for multi-mode\n                    if (isMultiMode) {\n                        const initialStatuses = {};\n                        classStudents.forEach({\n                            \"AttendanceModal.useEffect\": (student)=>{\n                                initialStatuses[student._id] = \"Present\";\n                            }\n                        }[\"AttendanceModal.useEffect\"]);\n                        setStudentStatuses(initialStatuses);\n                    }\n                }\n            } else {\n                setFilteredStudents(students);\n                if (isMultiMode) {\n                    const initialStatuses = {};\n                    students.forEach({\n                        \"AttendanceModal.useEffect\": (student)=>{\n                            initialStatuses[student._id] = \"Present\";\n                        }\n                    }[\"AttendanceModal.useEffect\"]);\n                    setStudentStatuses(initialStatuses);\n                }\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        formData.schedule_id,\n        schedules,\n        students,\n        isMultiMode\n    ]);\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"AttendanceModal.useEffect.handleClickOutside\": ()=>{\n                    setShowScheduleDropdown(false);\n                    setShowStudentDropdown(false);\n                }\n            }[\"AttendanceModal.useEffect.handleClickOutside\"];\n            if (showScheduleDropdown || showStudentDropdown) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"AttendanceModal.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"AttendanceModal.useEffect\"];\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        showScheduleDropdown,\n        showStudentDropdown\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!isMultiMode && !formData.student_id) {\n            newErrors.student_id = \"Student is required\";\n        }\n        if (isMultiMode && selectedStudents.size === 0) {\n            newErrors.students = \"At least one student must be selected\";\n        }\n        if (!formData.schedule_id) {\n            newErrors.schedule_id = \"Class schedule is required\";\n        }\n        if (!formData.date) {\n            newErrors.date = \"Date is required\";\n        }\n        if (!formData.academic_year) {\n            newErrors.academic_year = \"Academic year is required\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsSubmitting(true);\n        try {\n            if (isMultiMode) {\n                // Submit multiple attendance records\n                const attendancePromises = Array.from(selectedStudents).map((studentId)=>{\n                    const studentStatus = studentStatuses[studentId] || \"Present\";\n                    return onSubmit({\n                        ...formData,\n                        student_id: studentId,\n                        status: studentStatus\n                    });\n                });\n                await Promise.all(attendancePromises);\n            } else {\n                // Submit single attendance record\n                await onSubmit(formData);\n            }\n            onClose();\n        } catch (error) {\n            console.error(\"Error submitting attendance:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    // Multi-selection functions\n    const toggleStudentSelection = (studentId)=>{\n        const newSelected = new Set(selectedStudents);\n        if (newSelected.has(studentId)) {\n            newSelected.delete(studentId);\n        } else {\n            newSelected.add(studentId);\n        }\n        setSelectedStudents(newSelected);\n    };\n    const selectAllStudents = ()=>{\n        const allStudentIds = new Set(filteredStudents.map((s)=>s._id));\n        setSelectedStudents(allStudentIds);\n    };\n    const deselectAllStudents = ()=>{\n        setSelectedStudents(new Set());\n    };\n    const applyBulkStatus = ()=>{\n        const newStatuses = {\n            ...studentStatuses\n        };\n        selectedStudents.forEach((studentId)=>{\n            newStatuses[studentId] = bulkStatus;\n        });\n        setStudentStatuses(newStatuses);\n    };\n    const updateStudentStatus = (studentId, status)=>{\n        setStudentStatuses((prev)=>({\n                ...prev,\n                [studentId]: status\n            }));\n    };\n    const getScheduleDisplay = (schedule)=>{\n        const subject = subjects.find((s)=>s._id === schedule.subject_id);\n        const classInfo = classes.find((c)=>c._id === schedule.class_id);\n        return \"\".concat((subject === null || subject === void 0 ? void 0 : subject.name) || 'Unknown Subject', \" - \").concat((classInfo === null || classInfo === void 0 ? void 0 : classInfo.name) || 'Unknown Class', \" (\").concat(schedule.day_of_week, \")\");\n    };\n    // Helper functions for search and selection\n    const getSelectedScheduleName = ()=>{\n        const selectedSchedule = schedules.find((s)=>s._id === formData.schedule_id);\n        return selectedSchedule ? getScheduleDisplay(selectedSchedule) : '';\n    };\n    const getSelectedStudentName = ()=>{\n        const selectedStudent = filteredStudents.find((s)=>s._id === formData.student_id);\n        return selectedStudent ? \"\".concat(selectedStudent.first_name, \" \").concat(selectedStudent.last_name, \" (\").concat(selectedStudent.student_id, \")\") : '';\n    };\n    const filteredSchedules = schedules.filter((schedule)=>getScheduleDisplay(schedule).toLowerCase().includes(scheduleSearch.toLowerCase()));\n    const filteredStudentsForSearch = filteredStudents.filter((student)=>\"\".concat(student.first_name, \" \").concat(student.last_name, \" \").concat(student.student_id).toLowerCase().includes(studentSearch.toLowerCase()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-black bg-opacity-50\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full mx-4 \".concat(isMultiMode ? 'max-w-4xl' : 'max-w-md'),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-lg flex items-center justify-center \".concat(isMultiMode ? 'bg-green-500' : 'bg-blue-500'),\n                                            children: isMultiMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 34\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 77\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                    children: isEditing ? \"Edit Attendance\" : isMultiMode ? \"Mark Multiple Attendance\" : \"Mark Attendance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: isEditing ? \"Update attendance record\" : isMultiMode ? \"Mark attendance for multiple students\" : \"Create new attendance record\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        !isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMultiMode(!isMultiMode),\n                                            className: \"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600\",\n                                            children: isMultiMode ? 'Single Mode' : 'Multi Mode'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"p-6 space-y-4 \".concat(isMultiMode ? 'max-h-96 overflow-y-auto' : ''),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: [\n                                                \"Class Schedule \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 34\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 \".concat(errors.schedule_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setShowScheduleDropdown(!showScheduleDropdown);\n                                                        setShowStudentDropdown(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900 dark:text-white\",\n                                                            children: getSelectedScheduleName() || \"Select class schedule\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showScheduleDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 border-b border-gray-200 dark:border-gray-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        placeholder: \"Search schedules...\",\n                                                                        value: scheduleSearch,\n                                                                        onChange: (e)=>setScheduleSearch(e.target.value),\n                                                                        className: \"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white text-sm\",\n                                                                        onClick: (e)=>e.stopPropagation()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-48 overflow-y-auto\",\n                                                            children: filteredSchedules.length > 0 ? filteredSchedules.map((schedule)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleInputChange(\"schedule_id\", schedule._id);\n                                                                        setShowScheduleDropdown(false);\n                                                                        setScheduleSearch('');\n                                                                    },\n                                                                    children: getScheduleDisplay(schedule)\n                                                                }, schedule._id, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 29\n                                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-3 py-2 text-gray-500 dark:text-gray-400 text-sm\",\n                                                                children: \"No schedules found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.schedule_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.schedule_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this),\n                                !isMultiMode ? // Single student selection with search\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: [\n                                                \"Student \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 29\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 \".concat(errors.student_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\", \" \").concat(!formData.schedule_id ? 'opacity-50 cursor-not-allowed' : ''),\n                                                    onClick: (e)=>{\n                                                        if (!formData.schedule_id) return;\n                                                        e.stopPropagation();\n                                                        setShowStudentDropdown(!showStudentDropdown);\n                                                        setShowScheduleDropdown(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900 dark:text-white\",\n                                                            children: getSelectedStudentName() || (formData.schedule_id ? \"Select student\" : \"Select class schedule first\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this),\n                                                showStudentDropdown && formData.schedule_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 border-b border-gray-200 dark:border-gray-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        placeholder: \"Search students...\",\n                                                                        value: studentSearch,\n                                                                        onChange: (e)=>setStudentSearch(e.target.value),\n                                                                        className: \"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white text-sm\",\n                                                                        onClick: (e)=>e.stopPropagation()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-48 overflow-y-auto\",\n                                                            children: filteredStudentsForSearch.length > 0 ? filteredStudentsForSearch.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleInputChange(\"student_id\", student._id);\n                                                                        setShowStudentDropdown(false);\n                                                                        setStudentSearch('');\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium\",\n                                                                            children: [\n                                                                                student.first_name,\n                                                                                \" \",\n                                                                                student.last_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 432,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                            children: student.student_id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 433,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, student._id, true, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 31\n                                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-3 py-2 text-gray-500 dark:text-gray-400 text-sm\",\n                                                                children: \"No students found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 19\n                                        }, this),\n                                        errors.student_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.student_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 17\n                                }, this) : // Multi-student selection\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                    children: [\n                                                        \"Students (\",\n                                                        selectedStudents.size,\n                                                        \" selected)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: selectAllStudents,\n                                                            className: \"text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded\",\n                                                            disabled: !formData.schedule_id,\n                                                            children: \"Select All\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: deselectAllStudents,\n                                                            className: \"text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded\",\n                                                            children: \"Clear\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 19\n                                        }, this),\n                                        selectedStudents.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                        children: \"Apply to selected:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: bulkStatus,\n                                                        onChange: (e)=>setBulkStatus(e.target.value),\n                                                        className: \"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Present\",\n                                                                children: \"Present\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Absent\",\n                                                                children: \"Absent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Late\",\n                                                                children: \"Late\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Excused\",\n                                                                children: \"Excused\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: applyBulkStatus,\n                                                        className: \"px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600\",\n                                                        children: \"Apply\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md\",\n                                            children: filteredStudents.length > 0 ? filteredStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-600 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>toggleStudentSelection(student._id),\n                                                                    className: \"text-blue-500 hover:text-blue-600\",\n                                                                    children: selectedStudents.has(student._id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                        lineNumber: 520,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                            children: [\n                                                                                student.first_name,\n                                                                                \" \",\n                                                                                student.last_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                            children: student.student_id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                            lineNumber: 527,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        selectedStudents.has(student._id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: studentStatuses[student._id] || \"Present\",\n                                                            onChange: (e)=>updateStudentStatus(student._id, e.target.value),\n                                                            className: \"px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Present\",\n                                                                    children: \"Present\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Absent\",\n                                                                    children: \"Absent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Late\",\n                                                                    children: \"Late\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Excused\",\n                                                                    children: \"Excused\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, student._id, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 25\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center text-gray-500 dark:text-gray-400\",\n                                                children: formData.schedule_id ? \"No students found for this class\" : \"Please select a class schedule first\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 19\n                                        }, this),\n                                        errors.students && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.students\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 17\n                                }, this),\n                                !isMultiMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.status,\n                                            onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Present\",\n                                                    children: \"Present\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Absent\",\n                                                    children: \"Absent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Late\",\n                                                    children: \"Late\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Excused\",\n                                                    children: \"Excused\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Date\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.date,\n                                            onChange: (e)=>handleInputChange(\"date\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.date ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.date\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Academic Year\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.academic_year,\n                                            onChange: (e)=>handleInputChange(\"academic_year\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.academic_year ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            placeholder: \"e.g., 2024-2025\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.academic_year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.academic_year\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: onClose,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            disabled: isSubmitting,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                            children: [\n                                                isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_ChevronDown_FileCheck2_Save_Search_Square_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isSubmitting ? \"Saving...\" : isEditing ? \"Update\" : isMultiMode ? \"Mark \".concat(selectedStudents.size, \" Students\") : \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n            lineNumber: 253,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, this);\n}\n_s(AttendanceModal, \"ujAStAdb6hvNxzWAVhpwmsAnTao=\");\n_c = AttendanceModal;\nvar _c;\n$RefreshReg$(_c, \"AttendanceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/AttendanceModal.tsx\n"));

/***/ })

});