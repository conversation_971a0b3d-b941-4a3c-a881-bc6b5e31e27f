"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/teacher-assignment/page",{

/***/ "(app-pages-browser)/./src/components/modals/TeacherAssignmentModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/modals/TeacherAssignmentModal.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherAssignmentModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/TimetableServices */ \"(app-pages-browser)/./src/app/services/TimetableServices.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst DAYS = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday',\n    'Saturday',\n    'Sunday'\n];\nconst SCHEDULE_TYPES = [\n    'Normal',\n    'Exam',\n    'Event'\n];\nfunction TeacherAssignmentModal(param) {\n    let { isOpen, onClose, onSubmit, assignment, classes, subjects, teachers, periods, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        class_id: '',\n        subject_id: '',\n        teacher_id: '',\n        period_id: '',\n        day_of_week: '',\n        schedule_type: 'Normal'\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Search states\n    const [classSearch, setClassSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [subjectSearch, setSubjectSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [teacherSearch, setTeacherSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showClassDropdown, setShowClassDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSubjectDropdown, setShowSubjectDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTeacherDropdown, setShowTeacherDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Assigned periods for selected class\n    const [assignedPeriods, setAssignedPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Reset form when modal opens/closes or assignment changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherAssignmentModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (assignment) {\n                    // Edit mode - populate form with assignment data\n                    // Extract IDs from objects if they exist, or use direct IDs\n                    const classId = typeof assignment.class_id === 'object' ? assignment.class_id._id : assignment.class_id;\n                    const subjectId = typeof assignment.subject_id === 'object' ? assignment.subject_id._id : assignment.subject_id;\n                    const teacherId = typeof assignment.teacher_id === 'object' ? assignment.teacher_id._id : assignment.teacher_id;\n                    // For period, we might need to find by period_number if the ID doesn't match\n                    let periodId = typeof assignment.period_id === 'object' ? assignment.period_id._id : assignment.period_id;\n                    // If we have period_number but no direct period_id, find the period by number\n                    if (!periodId && assignment.period_number) {\n                        const matchingPeriod = periods.find({\n                            \"TeacherAssignmentModal.useEffect.matchingPeriod\": (p)=>p.period_number === assignment.period_number\n                        }[\"TeacherAssignmentModal.useEffect.matchingPeriod\"]);\n                        periodId = (matchingPeriod === null || matchingPeriod === void 0 ? void 0 : matchingPeriod._id) || '';\n                    }\n                    console.log('Assignment data for editing:', {\n                        assignment,\n                        extractedIds: {\n                            classId,\n                            subjectId,\n                            teacherId,\n                            periodId\n                        }\n                    });\n                    setFormData({\n                        class_id: classId || '',\n                        subject_id: subjectId || '',\n                        teacher_id: teacherId || '',\n                        period_id: periodId || '',\n                        day_of_week: assignment.day_of_week || '',\n                        schedule_type: assignment.schedule_type || 'Normal'\n                    });\n                } else {\n                    // Create mode - reset form\n                    setFormData({\n                        class_id: '',\n                        subject_id: '',\n                        teacher_id: '',\n                        period_id: '',\n                        day_of_week: '',\n                        schedule_type: 'Normal'\n                    });\n                }\n                setErrors({});\n                // Reset search states\n                setClassSearch('');\n                setSubjectSearch('');\n                setTeacherSearch('');\n                setShowClassDropdown(false);\n                setShowSubjectDropdown(false);\n                setShowTeacherDropdown(false);\n            }\n        }\n    }[\"TeacherAssignmentModal.useEffect\"], [\n        isOpen,\n        assignment\n    ]);\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherAssignmentModal.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"TeacherAssignmentModal.useEffect.handleClickOutside\": (event)=>{\n                    const target = event.target;\n                    if (!target.closest('.relative')) {\n                        setShowClassDropdown(false);\n                        setShowSubjectDropdown(false);\n                        setShowTeacherDropdown(false);\n                    }\n                }\n            }[\"TeacherAssignmentModal.useEffect.handleClickOutside\"];\n            if (showClassDropdown || showSubjectDropdown || showTeacherDropdown) {\n                document.addEventListener('mousedown', handleClickOutside);\n                return ({\n                    \"TeacherAssignmentModal.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n                })[\"TeacherAssignmentModal.useEffect\"];\n            }\n        }\n    }[\"TeacherAssignmentModal.useEffect\"], [\n        showClassDropdown,\n        showSubjectDropdown,\n        showTeacherDropdown\n    ]);\n    // Fetch assigned periods when class or day changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherAssignmentModal.useEffect\": ()=>{\n            const fetchAssignedPeriods = {\n                \"TeacherAssignmentModal.useEffect.fetchAssignedPeriods\": async ()=>{\n                    if (formData.class_id && formData.day_of_week) {\n                        try {\n                            // Extract school_id from the URL or props (assuming it's available)\n                            const schoolId = window.location.pathname.split('/')[2]; // Assuming URL structure /school-admin/{schoolId}/...\n                            const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_2__.getTimetable)(schoolId, {\n                                class_id: formData.class_id,\n                                day_of_week: formData.day_of_week\n                            });\n                            // Extract assigned period IDs for this class and day\n                            const assignedPeriodIds = response.schedule_records.filter({\n                                \"TeacherAssignmentModal.useEffect.fetchAssignedPeriods.assignedPeriodIds\": (record)=>record._id !== (assignment === null || assignment === void 0 ? void 0 : assignment._id)\n                            }[\"TeacherAssignmentModal.useEffect.fetchAssignedPeriods.assignedPeriodIds\"]) // Exclude current assignment when editing\n                            .map({\n                                \"TeacherAssignmentModal.useEffect.fetchAssignedPeriods.assignedPeriodIds\": (record)=>record.period_id\n                            }[\"TeacherAssignmentModal.useEffect.fetchAssignedPeriods.assignedPeriodIds\"]);\n                            setAssignedPeriods({\n                                \"TeacherAssignmentModal.useEffect.fetchAssignedPeriods\": (prev)=>({\n                                        ...prev,\n                                        [\"\".concat(formData.class_id, \"_\").concat(formData.day_of_week)]: assignedPeriodIds\n                                    })\n                            }[\"TeacherAssignmentModal.useEffect.fetchAssignedPeriods\"]);\n                        } catch (error) {\n                            console.error('Error fetching assigned periods:', error);\n                        }\n                    }\n                }\n            }[\"TeacherAssignmentModal.useEffect.fetchAssignedPeriods\"];\n            fetchAssignedPeriods();\n        }\n    }[\"TeacherAssignmentModal.useEffect\"], [\n        formData.class_id,\n        formData.day_of_week,\n        assignment === null || assignment === void 0 ? void 0 : assignment._id\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: ''\n                }));\n        }\n    };\n    // Filter functions\n    const filteredClasses = classes.filter((cls)=>cls.name.toLowerCase().includes(classSearch.toLowerCase()));\n    const filteredSubjects = subjects.filter((subject)=>subject.name.toLowerCase().includes(subjectSearch.toLowerCase()));\n    const filteredTeachers = teachers.filter((teacher)=>{\n        const teacherName = teacher.first_name && teacher.last_name ? \"\".concat(teacher.first_name, \" \").concat(teacher.last_name) : teacher.name || 'Unknown Teacher';\n        return teacherName.toLowerCase().includes(teacherSearch.toLowerCase());\n    });\n    // Get selected item names for display\n    const getSelectedClassName = ()=>{\n        const selectedClass = classes.find((cls)=>cls._id === formData.class_id);\n        return selectedClass ? selectedClass.name : '';\n    };\n    const getSelectedSubjectName = ()=>{\n        const selectedSubject = subjects.find((subject)=>subject._id === formData.subject_id);\n        return selectedSubject ? selectedSubject.name : '';\n    };\n    const getSelectedTeacherName = ()=>{\n        const selectedTeacher = teachers.find((teacher)=>teacher._id === formData.teacher_id);\n        if (!selectedTeacher) return '';\n        return selectedTeacher.first_name && selectedTeacher.last_name ? \"\".concat(selectedTeacher.first_name, \" \").concat(selectedTeacher.last_name) : selectedTeacher.name || 'Unknown Teacher';\n    };\n    // Check if a period is already assigned for the selected class and day\n    const isPeriodAssigned = (periodId)=>{\n        var _assignedPeriods_key;\n        if (!formData.class_id || !formData.day_of_week) return false;\n        const key = \"\".concat(formData.class_id, \"_\").concat(formData.day_of_week);\n        return ((_assignedPeriods_key = assignedPeriods[key]) === null || _assignedPeriods_key === void 0 ? void 0 : _assignedPeriods_key.includes(periodId)) || false;\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.class_id) newErrors.class_id = 'Class is required';\n        if (!formData.subject_id) newErrors.subject_id = 'Subject is required';\n        if (!formData.teacher_id) newErrors.teacher_id = 'Teacher is required';\n        if (!formData.period_id) newErrors.period_id = 'Period is required';\n        if (!formData.day_of_week) newErrors.day_of_week = 'Day of week is required';\n        if (!formData.schedule_type) newErrors.schedule_type = 'Schedule type is required';\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsSubmitting(true);\n        try {\n            await onSubmit(formData);\n            onClose();\n        } catch (error) {\n            console.error('Error submitting assignment:', error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleClose = ()=>{\n        if (!isSubmitting && !loading) {\n            onClose();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.95\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                exit: {\n                    opacity: 0,\n                    scale: 0.95\n                },\n                className: \"bg-widget rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-b border-stroke\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-foreground\",\n                                children: assignment ? 'Edit Teacher Assignment' : 'New Teacher Assignment'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClose,\n                                disabled: isSubmitting || loading,\n                                className: \"p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors disabled:opacity-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5 text-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"p-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Class \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.class_id ? getSelectedClassName() : classSearch,\n                                                        onChange: (e)=>{\n                                                            setClassSearch(e.target.value);\n                                                            if (formData.class_id) {\n                                                                handleInputChange('class_id', '');\n                                                            }\n                                                            setShowClassDropdown(true);\n                                                        },\n                                                        onFocus: ()=>setShowClassDropdown(true),\n                                                        placeholder: \"Search and select a class\",\n                                                        className: \"w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.class_id ? 'border-red-500' : 'border-stroke'),\n                                                        disabled: isSubmitting || loading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    showClassDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute z-10 w-full mt-1 bg-widget border border-stroke rounded-md shadow-lg max-h-60 overflow-y-auto\",\n                                                        children: filteredClasses.length > 0 ? filteredClasses.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                onClick: ()=>{\n                                                                    handleInputChange('class_id', classItem._id);\n                                                                    setClassSearch('');\n                                                                    setShowClassDropdown(false);\n                                                                },\n                                                                className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer text-foreground\",\n                                                                children: classItem.name\n                                                            }, classItem._id, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 27\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 text-foreground/60\",\n                                                            children: \"No classes found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.class_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.class_id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Subject \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.subject_id ? getSelectedSubjectName() : subjectSearch,\n                                                        onChange: (e)=>{\n                                                            setSubjectSearch(e.target.value);\n                                                            if (formData.subject_id) {\n                                                                handleInputChange('subject_id', '');\n                                                            }\n                                                            setShowSubjectDropdown(true);\n                                                        },\n                                                        onFocus: ()=>setShowSubjectDropdown(true),\n                                                        placeholder: \"Search and select a subject\",\n                                                        className: \"w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.subject_id ? 'border-red-500' : 'border-stroke'),\n                                                        disabled: isSubmitting || loading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    showSubjectDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute z-10 w-full mt-1 bg-widget border border-stroke rounded-md shadow-lg max-h-60 overflow-y-auto\",\n                                                        children: filteredSubjects.length > 0 ? filteredSubjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                onClick: ()=>{\n                                                                    handleInputChange('subject_id', subject._id);\n                                                                    setSubjectSearch('');\n                                                                    setShowSubjectDropdown(false);\n                                                                },\n                                                                className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer text-foreground\",\n                                                                children: subject.name\n                                                            }, subject._id, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 27\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 text-foreground/60\",\n                                                            children: \"No subjects found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.subject_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.subject_id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Teacher \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.teacher_id ? getSelectedTeacherName() : teacherSearch,\n                                                        onChange: (e)=>{\n                                                            setTeacherSearch(e.target.value);\n                                                            if (formData.teacher_id) {\n                                                                handleInputChange('teacher_id', '');\n                                                            }\n                                                            setShowTeacherDropdown(true);\n                                                        },\n                                                        onFocus: ()=>setShowTeacherDropdown(true),\n                                                        placeholder: \"Search and select a teacher\",\n                                                        className: \"w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.teacher_id ? 'border-red-500' : 'border-stroke'),\n                                                        disabled: isSubmitting || loading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    showTeacherDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute z-10 w-full mt-1 bg-widget border border-stroke rounded-md shadow-lg max-h-60 overflow-y-auto\",\n                                                        children: filteredTeachers.length > 0 ? filteredTeachers.map((teacher)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                onClick: ()=>{\n                                                                    handleInputChange('teacher_id', teacher._id);\n                                                                    setTeacherSearch('');\n                                                                    setShowTeacherDropdown(false);\n                                                                },\n                                                                className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer text-foreground\",\n                                                                children: teacher.first_name && teacher.last_name ? \"\".concat(teacher.first_name, \" \").concat(teacher.last_name) : teacher.name || 'Unknown Teacher'\n                                                            }, teacher._id, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 27\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 text-foreground/60\",\n                                                            children: \"No teachers found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.teacher_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.teacher_id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Period \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 26\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.period_id,\n                                                onChange: (e)=>handleInputChange('period_id', e.target.value),\n                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.period_id ? 'border-red-500' : 'border-stroke'),\n                                                disabled: isSubmitting || loading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select a period\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    periods.map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: period._id,\n                                                            children: [\n                                                                \"Period \",\n                                                                period.period_number,\n                                                                \" (\",\n                                                                period.start_time.slice(0, 5),\n                                                                \" - \",\n                                                                period.end_time.slice(0, 5),\n                                                                \")\"\n                                                            ]\n                                                        }, period._id, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.period_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.period_id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Day of Week \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.day_of_week,\n                                                onChange: (e)=>handleInputChange('day_of_week', e.target.value),\n                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.day_of_week ? 'border-red-500' : 'border-stroke'),\n                                                disabled: isSubmitting || loading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select a day\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: day,\n                                                            children: day\n                                                        }, day, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.day_of_week && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.day_of_week\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Schedule Type \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.schedule_type,\n                                                onChange: (e)=>handleInputChange('schedule_type', e.target.value),\n                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.schedule_type ? 'border-red-500' : 'border-stroke'),\n                                                disabled: isSubmitting || loading,\n                                                children: SCHEDULE_TYPES.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: type,\n                                                        children: type\n                                                    }, type, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.schedule_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.schedule_type\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end space-x-4 pt-6 border-t border-stroke\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleClose,\n                                        disabled: isSubmitting || loading,\n                                        className: \"px-4 py-2 text-foreground/70 hover:text-foreground transition-colors disabled:opacity-50\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || loading,\n                                        className: \"flex items-center space-x-2 px-6 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors disabled:opacity-50\",\n                                        children: [\n                                            (isSubmitting || loading) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 47\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: assignment ? 'Update Assignment' : 'Create Assignment'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                lineNumber: 257,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n        lineNumber: 255,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherAssignmentModal, \"ASvzNt+R/hRYkRiNl3ulv/rZo5M=\");\n_c = TeacherAssignmentModal;\nvar _c;\n$RefreshReg$(_c, \"TeacherAssignmentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/TeacherAssignmentModal.tsx\n"));

/***/ })

});