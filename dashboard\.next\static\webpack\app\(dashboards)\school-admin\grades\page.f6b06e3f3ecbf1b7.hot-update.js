"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/grades/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/grades/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GradesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/GradeServices */ \"(app-pages-browser)/./src/app/services/GradeServices.tsx\");\n/* harmony import */ var _app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/StudentServices */ \"(app-pages-browser)/./src/app/services/StudentServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/ExamTypeServices */ \"(app-pages-browser)/./src/app/services/ExamTypeServices.tsx\");\n/* harmony import */ var _app_services_UserServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/GradeModal */ \"(app-pages-browser)/./src/components/modals/GradeModal.tsx\");\n/* harmony import */ var _components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/PasswordConfirmDeleteModal */ \"(app-pages-browser)/./src/components/modals/PasswordConfirmDeleteModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    baseHref: \"/school-admin/grades\",\n    title: \"Grades\"\n};\nfunction GradesPage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    // State management\n    const [gradeRecords, setGradeRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalGrades: 0,\n        averageScore: 0,\n        highestScore: 0,\n        lowestScore: 0,\n        passRate: 0\n    });\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedTerm, setSelectedTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedExamType, setSelectedExamType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isGradeModalOpen, setIsGradeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gradeToEdit, setGradeToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gradeToDelete, setGradeToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedGrades, setSelectedGrades] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [examTypes, setExamTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch grade data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchGradeData = {\n                \"GradesPage.useEffect.fetchGradeData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n                        if (selectedTerm !== 'all') filters.term = selectedTerm;\n                        if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n                        // Fetch records and stats in parallel\n                        const [recordsResponse, statsResponse] = await Promise.all([\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n                        ]);\n                        setGradeRecords(recordsResponse.grade_records);\n                        setStats(statsResponse.stats);\n                    } catch (error) {\n                        console.error(\"Error fetching grade data:\", error);\n                        showError(\"Error\", \"Failed to load grade data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchGradeData\"];\n            fetchGradeData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId,\n        selectedClass,\n        selectedSubject,\n        selectedTerm,\n        selectedExamType\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"GradesPage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__.getStudentsBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__.getExamTypes)()\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setStudents(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch students:\", results[0].reason);\n                            setStudents([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setExamTypes(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch exam types:\", results[2].reason);\n                            setExamTypes([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateGrade = ()=>{\n        setGradeToEdit(null);\n        setIsGradeModalOpen(true);\n    };\n    const handleEditGrade = (grade)=>{\n        setGradeToEdit(grade);\n        setIsGradeModalOpen(true);\n    };\n    const handleDeleteGrade = (grade)=>{\n        setGradeToDelete(grade);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedGrades(selectedRows);\n    };\n    // Modal submission functions\n    const handleGradeSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (gradeToEdit) {\n                // Update existing grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.updateGrade)(gradeToEdit._id, data);\n            } else {\n                // Create new grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.createGrade)({\n                    ...data,\n                    school_id: schoolId\n                });\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsGradeModalOpen(false);\n            setGradeToEdit(null);\n            // Show success notification\n            showSuccess(gradeToEdit ? \"Grade Updated\" : \"Grade Added\", gradeToEdit ? \"Grade has been updated successfully.\" : \"New grade has been added successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting grade:\", error);\n            showError(\"Error\", \"Failed to save grade. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteConfirm = async (password)=>{\n        if (!schoolId || !user) return;\n        try {\n            // Verify password\n            const passwordVerified = await (0,_app_services_UserServices__WEBPACK_IMPORTED_MODULE_11__.verifyPassword)(password, user.email);\n            if (!passwordVerified) {\n                showError(\"Error\", \"Invalid password. Please try again.\");\n                throw new Error(\"Invalid password\");\n            }\n            if (deleteType === \"single\" && gradeToDelete) {\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteGrade)(gradeToDelete._id);\n            } else if (deleteType === \"multiple\") {\n                const selectedIds = selectedGrades.map((g)=>g._id);\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteMultipleGrades)(selectedIds);\n                setClearSelection(true);\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsDeleteModalOpen(false);\n            setGradeToDelete(null);\n            setSelectedGrades([]);\n            // Show success notification\n            if (deleteType === \"single\") {\n                showSuccess(\"Grade Deleted\", \"Grade record has been deleted successfully.\");\n            } else {\n                showSuccess(\"Grades Deleted\", \"\".concat(selectedGrades.length, \" grade records have been deleted successfully.\"));\n            }\n        } catch (error) {\n            console.error(\"Error deleting grade(s):\", error);\n            if (error.message !== \"Invalid password\") {\n                showError(\"Error\", \"Failed to delete grade record(s). Please try again.\");\n            }\n            throw error;\n        }\n    };\n    const getGradeColor = (grade)=>{\n        switch(grade){\n            case 'A+':\n            case 'A':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n            case 'B+':\n            case 'B':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n            case 'C+':\n            case 'C':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n            case 'D':\n            case 'F':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n        }\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 90) return 'text-green-600 font-semibold';\n        if (score >= 80) return 'text-blue-600 font-semibold';\n        if (score >= 70) return 'text-yellow-600 font-semibold';\n        if (score >= 60) return 'text-orange-600 font-semibold';\n        return 'text-red-600 font-semibold';\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Student\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-foreground\",\n                            children: row.student_name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-foreground/60\",\n                            children: row.student_id\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Exam Type\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.exam_type\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Score\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-bold \".concat(getScoreColor(row.score)),\n                    children: [\n                        row.score,\n                        \"%\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Grade\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-sm font-bold \".concat(getGradeColor(row.grade)),\n                    children: row.grade\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Term\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.term\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (grade)=>{\n                handleEditGrade(grade);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (grade)=>{\n                handleDeleteGrade(grade);\n            }\n        }\n    ];\n    // Filter data based on selections\n    const filteredRecords = gradeRecords.filter((record)=>{\n        if (selectedClass !== 'all' && record.class_name !== selectedClass) return false;\n        if (selectedSubject !== 'all' && record.subject_name !== selectedSubject) return false;\n        if (selectedTerm !== 'all' && record.term !== selectedTerm) return false;\n        if (selectedExamType !== 'all' && record.exam_type !== selectedExamType) return false;\n        return true;\n    });\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                lineNumber: 406,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 405,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Grades Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Monitor and manage student grades across all subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    stats.averageScore,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Average Score\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-5 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Grades\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: stats.totalGrades\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Average Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            stats.averageScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-teal rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Highest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: [\n                                                            stats.highestScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Lowest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: [\n                                                            stats.lowestScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Pass Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            stats.passRate,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Grade 10A\",\n                                                        children: \"Grade 10A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Grade 10B\",\n                                                        children: \"Grade 10B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Grade 11A\",\n                                                        children: \"Grade 11A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Grade 11B\",\n                                                        children: \"Grade 11B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Subject:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedSubject,\n                                                onChange: (e)=>setSelectedSubject(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Mathematics\",\n                                                        children: \"Mathematics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Physics\",\n                                                        children: \"Physics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Chemistry\",\n                                                        children: \"Chemistry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Biology\",\n                                                        children: \"Biology\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"English\",\n                                                        children: \"English\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Term:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedTerm,\n                                                onChange: (e)=>setSelectedTerm(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Terms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"First Term\",\n                                                        children: \"First Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Second Term\",\n                                                        children: \"Second Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Third Term\",\n                                                        children: \"Third Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Exam Type:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedExamType,\n                                                onChange: (e)=>setSelectedExamType(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Mid-term Exam\",\n                                                        children: \"Mid-term Exam\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Final Exam\",\n                                                        children: \"Final Exam\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Quiz\",\n                                                        children: \"Quiz\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Lab Test\",\n                                                        children: \"Lab Test\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Assignment\",\n                                                        children: \"Assignment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Grade Records (\",\n                                                filteredRecords.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateGrade,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Grade\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredRecords,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    isOpen: isGradeModalOpen,\n                    onClose: ()=>{\n                        setIsGradeModalOpen(false);\n                        setGradeToEdit(null);\n                    },\n                    onSubmit: handleGradeSubmit,\n                    grade: gradeToEdit,\n                    students: students,\n                    subjects: subjects,\n                    examTypes: examTypes,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 614,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setGradeToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Grade Record\" : \"Delete Selected Grade Records\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this grade record? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedGrades.length, \" selected grade records? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && gradeToDelete ? \"\".concat(gradeToDelete.student_name, \" - \").concat(gradeToDelete.subject_name, \" (\").concat(gradeToDelete.exam_type, \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedGrades.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 629,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 656,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 413,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n        lineNumber: 412,\n        columnNumber: 5\n    }, this);\n}\n_s(GradesPage, \"Unbmq46oVbBsrl3g2oGxiFvVnHU=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = GradesPage;\nvar _c;\n$RefreshReg$(_c, \"GradesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx\n"));

/***/ })

});