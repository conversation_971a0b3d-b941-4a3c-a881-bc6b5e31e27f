"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/grades/page",{

/***/ "(app-pages-browser)/./src/components/modals/GradeModal.tsx":
/*!**********************************************!*\
  !*** ./src/components/modals/GradeModal.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GradeModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Percent_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Percent,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Percent_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Percent,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Percent_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Percent,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Percent_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Percent,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Percent_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Percent,Save,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction GradeModal(param) {\n    let { isOpen, onClose, onSubmit, grade, students, subjects, examTypes, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id: \"\",\n        subject_id: \"\",\n        exam_type: \"\",\n        term: \"First Term\",\n        academic_year: \"2024-2025\",\n        score: \"\",\n        grade: \"\",\n        comments: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Search states\n    const [studentSearch, setStudentSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [subjectSearch, setSubjectSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [examTypeSearch, setExamTypeSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showStudentDropdown, setShowStudentDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSubjectDropdown, setShowSubjectDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showExamTypeDropdown, setShowExamTypeDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isEditing = !!grade;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GradeModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (grade) {\n                    // Find the actual IDs from the grade record\n                    const studentId = typeof grade.student_id === 'object' ? grade.student_id._id : grade.student_id;\n                    const subjectId = typeof grade.subject_id === 'object' ? grade.subject_id._id : grade.subject_id;\n                    const examTypeId = typeof grade.exam_type === 'object' ? grade.exam_type._id : grade.exam_type;\n                    // Convert percentage score to /20 for display\n                    const scoreOn20 = grade.score ? grade.score.toFixed(1) : \"\";\n                    setFormData({\n                        student_id: studentId || \"\",\n                        subject_id: subjectId || \"\",\n                        exam_type: examTypeId || \"\",\n                        term: grade.term || \"First Term\",\n                        academic_year: grade.academic_year || \"2024-2025\",\n                        score: scoreOn20,\n                        grade: grade.grade || \"\",\n                        comments: grade.comments || \"\"\n                    });\n                } else {\n                    setFormData({\n                        student_id: \"\",\n                        subject_id: \"\",\n                        exam_type: \"\",\n                        term: \"First Term\",\n                        academic_year: \"2024-2025\",\n                        score: \"\",\n                        grade: \"\",\n                        comments: \"\"\n                    });\n                }\n                setErrors({});\n            }\n        }\n    }[\"GradeModal.useEffect\"], [\n        isOpen,\n        grade\n    ]);\n    // Auto-calculate grade based on score (/20 system) with French/English mentions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GradeModal.useEffect\": ()=>{\n            if (formData.score) {\n                const score = parseFloat(formData.score);\n                let calculatedGrade = \"\";\n                // French/English grading system based on /20 score\n                if (score >= 18) calculatedGrade = \"A+/Excellent\";\n                else if (score >= 16) calculatedGrade = \"A/Très bien\";\n                else if (score >= 14) calculatedGrade = \"B+/Bien\";\n                else if (score >= 12) calculatedGrade = \"B/Assez bien\";\n                else if (score >= 10) calculatedGrade = \"C+/Passable\";\n                else if (score >= 8) calculatedGrade = \"C/Insuffisant\";\n                else if (score >= 6) calculatedGrade = \"D+/Médiocre\";\n                else if (score >= 4) calculatedGrade = \"D/Très insuffisant\";\n                else calculatedGrade = \"F/Nul\";\n                setFormData({\n                    \"GradeModal.useEffect\": (prev)=>({\n                            ...prev,\n                            grade: calculatedGrade\n                        })\n                }[\"GradeModal.useEffect\"]);\n            }\n        }\n    }[\"GradeModal.useEffect\"], [\n        formData.score\n    ]);\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GradeModal.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"GradeModal.useEffect.handleClickOutside\": ()=>{\n                    setShowStudentDropdown(false);\n                    setShowSubjectDropdown(false);\n                    setShowExamTypeDropdown(false);\n                }\n            }[\"GradeModal.useEffect.handleClickOutside\"];\n            if (showStudentDropdown || showSubjectDropdown || showExamTypeDropdown) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"GradeModal.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"GradeModal.useEffect\"];\n            }\n        }\n    }[\"GradeModal.useEffect\"], [\n        showStudentDropdown,\n        showSubjectDropdown,\n        showExamTypeDropdown\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.student_id) {\n            newErrors.student_id = \"Student is required\";\n        }\n        if (!formData.subject_id) {\n            newErrors.subject_id = \"Subject is required\";\n        }\n        if (!formData.exam_type) {\n            newErrors.exam_type = \"Exam type is required\";\n        }\n        if (!formData.score) {\n            newErrors.score = \"Score is required\";\n        } else {\n            const score = parseFloat(formData.score);\n            if (isNaN(score) || score < 0 || score > 20) {\n                newErrors.score = \"Score must be a number between 0 and 20\";\n            }\n        }\n        if (!formData.academic_year) {\n            newErrors.academic_year = \"Academic year is required\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsSubmitting(true);\n        try {\n            const scoreOn20 = parseFloat(formData.score);\n            // Convert /20 score to percentage for backend storage\n            const scorePercentage = scoreOn20 * 100 / 20;\n            const submitData = {\n                ...formData,\n                score: scorePercentage\n            };\n            await onSubmit(submitData);\n            onClose();\n        } catch (error) {\n            console.error(\"Error submitting grade:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    // Helper functions for search and selection\n    const getSelectedStudentName = ()=>{\n        const selectedStudent = students.find((s)=>s._id === formData.student_id);\n        return selectedStudent ? \"\".concat(selectedStudent.first_name, \" \").concat(selectedStudent.last_name, \" (\").concat(selectedStudent.student_id, \")\") : '';\n    };\n    const getSelectedSubjectName = ()=>{\n        const selectedSubject = subjects.find((s)=>s._id === formData.subject_id);\n        return selectedSubject ? selectedSubject.name : '';\n    };\n    const getSelectedExamTypeName = ()=>{\n        const selectedExamType = examTypes.find((e)=>e._id === formData.exam_type);\n        return selectedExamType ? selectedExamType.type : '';\n    };\n    const filteredStudents = students.filter((student)=>\"\".concat(student.first_name, \" \").concat(student.last_name, \" \").concat(student.student_id).toLowerCase().includes(studentSearch.toLowerCase()));\n    const filteredSubjects = subjects.filter((subject)=>subject.name.toLowerCase().includes(subjectSearch.toLowerCase()));\n    const filteredExamTypes = examTypes.filter((examType)=>examType.type.toLowerCase().includes(examTypeSearch.toLowerCase()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-black bg-opacity-50\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Percent_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                    children: isEditing ? \"Edit Grade\" : \"Add New Grade\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: isEditing ? \"Update grade record\" : \"Create new grade record\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Percent_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"p-6 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: [\n                                                \"Student \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 \".concat(errors.student_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setShowStudentDropdown(!showStudentDropdown);\n                                                        setShowSubjectDropdown(false);\n                                                        setShowExamTypeDropdown(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900 dark:text-white\",\n                                                            children: getSelectedStudentName() || \"Select student\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Percent_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showStudentDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 border-b border-gray-200 dark:border-gray-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Percent_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        placeholder: \"Search students...\",\n                                                                        value: studentSearch,\n                                                                        onChange: (e)=>setStudentSearch(e.target.value),\n                                                                        className: \"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-600 dark:text-white text-sm\",\n                                                                        onClick: (e)=>e.stopPropagation()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-48 overflow-y-auto\",\n                                                            children: filteredStudents.length > 0 ? filteredStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleInputChange(\"student_id\", student._id);\n                                                                        setShowStudentDropdown(false);\n                                                                        setStudentSearch('');\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium\",\n                                                                            children: [\n                                                                                student.first_name,\n                                                                                \" \",\n                                                                                student.last_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                            children: student.student_id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, student._id, true, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 29\n                                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-3 py-2 text-gray-500 dark:text-gray-400 text-sm\",\n                                                                children: \"No students found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.student_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.student_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: [\n                                                \"Subject \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 \".concat(errors.subject_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setShowSubjectDropdown(!showSubjectDropdown);\n                                                        setShowStudentDropdown(false);\n                                                        setShowExamTypeDropdown(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900 dark:text-white\",\n                                                            children: getSelectedSubjectName() || \"Select subject\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Percent_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showSubjectDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 border-b border-gray-200 dark:border-gray-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Percent_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        placeholder: \"Search subjects...\",\n                                                                        value: subjectSearch,\n                                                                        onChange: (e)=>setSubjectSearch(e.target.value),\n                                                                        className: \"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-600 dark:text-white text-sm\",\n                                                                        onClick: (e)=>e.stopPropagation()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-48 overflow-y-auto\",\n                                                            children: filteredSubjects.length > 0 ? filteredSubjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleInputChange(\"subject_id\", subject._id);\n                                                                        setShowSubjectDropdown(false);\n                                                                        setSubjectSearch('');\n                                                                    },\n                                                                    children: subject.name\n                                                                }, subject._id, false, {\n                                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 29\n                                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-3 py-2 text-gray-500 dark:text-gray-400 text-sm\",\n                                                                children: \"No subjects found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.subject_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.subject_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Exam Type\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.exam_type,\n                                            onChange: (e)=>handleInputChange(\"exam_type\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white \".concat(errors.exam_type ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select exam type\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this),\n                                                examTypes.map((examType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: examType._id,\n                                                        children: examType.type\n                                                    }, examType._id, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.exam_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.exam_type\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: \"Term\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.term,\n                                                    onChange: (e)=>handleInputChange(\"term\", e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"First Term\",\n                                                            children: \"First Term\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Second Term\",\n                                                            children: \"Second Term\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Third Term\",\n                                                            children: \"Third Term\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: \"Academic Year\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.academic_year,\n                                                    onChange: (e)=>handleInputChange(\"academic_year\", e.target.value),\n                                                    className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white \".concat(errors.academic_year ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                                    placeholder: \"2024-2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, this),\n                                                errors.academic_year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-500\",\n                                                    children: errors.academic_year\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: \"Score (/20)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"20\",\n                                                    step: \"0.1\",\n                                                    value: formData.score,\n                                                    onChange: (e)=>handleInputChange(\"score\", e.target.value),\n                                                    className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white \".concat(errors.score ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                                    placeholder: \"17.1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, this),\n                                                errors.score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-500\",\n                                                    children: errors.score\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: \"Grade (Auto-calculated - French/English)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.grade,\n                                                    onChange: (e)=>handleInputChange(\"grade\", e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white\",\n                                                    placeholder: \"A+/Excellent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Comments (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.comments,\n                                            onChange: (e)=>handleInputChange(\"comments\", e.target.value),\n                                            rows: 3,\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white\",\n                                            placeholder: \"Additional comments about the student's performance...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: onClose,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            disabled: isSubmitting,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                            children: [\n                                                isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Percent_Save_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isSubmitting ? \"Saving...\" : isEditing ? \"Update\" : \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n            lineNumber: 213,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\GradeModal.tsx\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, this);\n}\n_s(GradeModal, \"4LjmiVB5Nqk1FAPbqU0zLRYGwH0=\");\n_c = GradeModal;\nvar _c;\n$RefreshReg$(_c, \"GradeModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/GradeModal.tsx\n"));

/***/ })

});