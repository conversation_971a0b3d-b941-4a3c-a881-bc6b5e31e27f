"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/teacher-assignment/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx":
/*!***********************************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx ***!
  \***********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherAssignmentPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Filter,Plus,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/TimetableServices */ \"(app-pages-browser)/./src/app/services/TimetableServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/TeacherServices */ \"(app-pages-browser)/./src/app/services/TeacherServices.tsx\");\n/* harmony import */ var _app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/PeriodServices */ \"(app-pages-browser)/./src/app/services/PeriodServices.tsx\");\n/* harmony import */ var _app_services_UserServices__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/services/UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _components_modals_TeacherAssignmentModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/TeacherAssignmentModal */ \"(app-pages-browser)/./src/components/modals/TeacherAssignmentModal.tsx\");\n/* harmony import */ var _components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/PasswordConfirmDeleteModal */ \"(app-pages-browser)/./src/components/modals/PasswordConfirmDeleteModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    baseHref: \"/school-admin/teacher-assignment\",\n    title: \"Teacher Assignment\"\n};\nconst DAYS = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\nfunction TeacherAssignmentPage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    // State management\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedTeacher, setSelectedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedDay, setSelectedDay] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isAssignmentModalOpen, setIsAssignmentModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [assignmentToEdit, setAssignmentToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [assignmentToDelete, setAssignmentToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedAssignments, setSelectedAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [periods, setPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch assignment data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TeacherAssignmentPage.useEffect\": ()=>{\n            const fetchAssignmentData = {\n                \"TeacherAssignmentPage.useEffect.fetchAssignmentData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;\n                        if (selectedDay !== 'all') filters.day_of_week = selectedDay;\n                        // Fetch assignments (timetable entries)\n                        const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_7__.getTimetable)(schoolId, filters);\n                        setAssignments(response.schedule_records);\n                    } catch (error) {\n                        console.error(\"Error fetching assignment data:\", error);\n                        showError(\"Error\", \"Failed to load assignment data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"TeacherAssignmentPage.useEffect.fetchAssignmentData\"];\n            fetchAssignmentData();\n        }\n    }[\"TeacherAssignmentPage.useEffect\"], [\n        schoolId,\n        selectedClass,\n        selectedTeacher,\n        selectedDay\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TeacherAssignmentPage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"TeacherAssignmentPage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_8__.getClassesBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_TeacherServices__WEBPACK_IMPORTED_MODULE_10__.getTeachersBySchool)(schoolId),\n                            (0,_app_services_PeriodServices__WEBPACK_IMPORTED_MODULE_11__.getPeriodsBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setClasses(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[0].reason);\n                            setClasses([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setTeachers(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch teachers:\", results[2].reason);\n                            setTeachers([]);\n                        }\n                        if (results[3].status === 'fulfilled') {\n                            setPeriods(results[3].value);\n                        } else {\n                            console.error(\"Failed to fetch periods:\", results[3].reason);\n                            setPeriods([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"TeacherAssignmentPage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"TeacherAssignmentPage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"TeacherAssignmentPage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"TeacherAssignmentPage.useEffect\"], [\n        schoolId\n    ]);\n    // CRUD Functions\n    const handleCreateAssignment = ()=>{\n        setAssignmentToEdit(null);\n        setIsAssignmentModalOpen(true);\n    };\n    const handleEditAssignment = (assignment)=>{\n        setAssignmentToEdit(assignment);\n        setIsAssignmentModalOpen(true);\n    };\n    const handleDeleteAssignment = (assignment)=>{\n        setAssignmentToDelete(assignment);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedAssignments(selectedRows);\n    };\n    const handleDeleteConfirm = async (password)=>{\n        if (!schoolId || !user) return;\n        try {\n            // Verify password\n            const passwordVerified = await (0,_app_services_UserServices__WEBPACK_IMPORTED_MODULE_12__.verifyPassword)(password, user.email);\n            if (!passwordVerified) {\n                showError(\"Error\", \"Invalid password. Please try again.\");\n                throw new Error(\"Invalid password\");\n            }\n            // TODO: Implement actual deletion logic when backend endpoints are available\n            // For now, just show success message\n            if (deleteType === \"single\" && assignmentToDelete) {\n                // await deleteTeacherAssignment(assignmentToDelete._id);\n                showSuccess(\"Assignment Deleted\", \"Teacher assignment has been deleted successfully.\");\n            } else if (deleteType === \"multiple\") {\n                // const selectedIds = selectedAssignments.map(a => a._id);\n                // await deleteMultipleTeacherAssignments(selectedIds);\n                showSuccess(\"Assignments Deleted\", \"\".concat(selectedAssignments.length, \" teacher assignments have been deleted successfully.\"));\n                setClearSelection(true);\n            }\n            // Close modal and reset state\n            setIsDeleteModalOpen(false);\n            setAssignmentToDelete(null);\n            setSelectedAssignments([]);\n        // TODO: Refresh assignments list when backend endpoints are available\n        // const filters: any = {};\n        // if (selectedClass !== 'all') filters.class_id = selectedClass;\n        // if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;\n        // if (selectedDay !== 'all') filters.day_of_week = selectedDay;\n        // const response = await getTimetable(schoolId as string, filters);\n        // setAssignments(response.schedule_records);\n        } catch (error) {\n            console.error(\"Error deleting assignment(s):\", error);\n            if (error.message !== \"Invalid password\") {\n                showError(\"Error\", \"Failed to delete assignment(s). Please try again.\");\n            }\n            throw error;\n        }\n    };\n    // Modal submission function\n    const handleAssignmentSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // Create new assignment\n            await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_7__.createScheduleEntry)(schoolId, data);\n            // Refresh assignments list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;\n            if (selectedDay !== 'all') filters.day_of_week = selectedDay;\n            const response = await (0,_app_services_TimetableServices__WEBPACK_IMPORTED_MODULE_7__.getTimetable)(schoolId, filters);\n            setAssignments(response.schedule_records);\n            setIsAssignmentModalOpen(false);\n            setAssignmentToEdit(null);\n            // Show success notification\n            showSuccess(\"Assignment Created\", \"Teacher assignment has been created successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting assignment:\", error);\n            showError(\"Error\", \"Failed to save assignment. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const getDayColor = (day)=>{\n        const colors = {\n            Monday: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',\n            Tuesday: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',\n            Wednesday: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',\n            Thursday: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',\n            Friday: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',\n            Saturday: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',\n            Sunday: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'\n        };\n        return colors[day] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Teacher\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium text-foreground\",\n                        children: row.teacher_name\n                    }, void 0, false, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.teacher_name\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.class_name\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.subject_name\n        },\n        {\n            header: \"Day\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getDayColor(row.day_of_week)),\n                    children: row.day_of_week\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.day_of_week\n        },\n        {\n            header: \"Period\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-semibold\",\n                            children: [\n                                \"Period \",\n                                row.period_number\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-foreground/60\",\n                            children: [\n                                row.start_time.slice(0, 5),\n                                \" - \",\n                                row.end_time.slice(0, 5)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>\"Period \".concat(row.period_number, \" \").concat(row.start_time, \" \").concat(row.end_time)\n        },\n        {\n            header: \"Type\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.schedule_type\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.schedule_type\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (assignment)=>{\n                handleEditAssignment(assignment);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (assignment)=>{\n                handleDeleteAssignment(assignment);\n            }\n        }\n    ];\n    // Filter data based on selections\n    const filteredAssignments = assignments.filter((assignment)=>{\n        if (selectedClass !== 'all') {\n            var _classes_find;\n            // Find class name from classes array\n            const selectedClassName = (_classes_find = classes.find((c)=>c._id === selectedClass)) === null || _classes_find === void 0 ? void 0 : _classes_find.name;\n            if (assignment.class_name !== selectedClassName) return false;\n        }\n        if (selectedTeacher !== 'all') {\n            // Find teacher name from teachers array\n            const selectedTeacherObj = teachers.find((t)=>t._id === selectedTeacher);\n            const selectedTeacherName = selectedTeacherObj ? \"\".concat(selectedTeacherObj.first_name, \" \").concat(selectedTeacherObj.last_name) : '';\n            if (assignment.teacher_name !== selectedTeacherName) return false;\n        }\n        if (selectedDay !== 'all' && assignment.day_of_week !== selectedDay) return false;\n        return true;\n    });\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                lineNumber: 365,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n            lineNumber: 364,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Teacher Assignment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Assign teachers to classes for specific periods and subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: assignments.length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Total Assignments\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: teachers.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: classes.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Active Assignments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: assignments.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: subjects.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: classItem._id,\n                                                            children: classItem.name\n                                                        }, classItem._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Teacher:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedTeacher,\n                                                onChange: (e)=>setSelectedTeacher(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    teachers.map((teacher)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: teacher._id,\n                                                            children: teacher.first_name && teacher.last_name ? \"\".concat(teacher.first_name, \" \").concat(teacher.last_name) : teacher.name || 'Unknown Teacher'\n                                                        }, teacher._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Day:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedDay,\n                                                onChange: (e)=>setSelectedDay(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: day,\n                                                            children: day\n                                                        }, day, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Teacher Assignments (\",\n                                                filteredAssignments.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateAssignment,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Filter_Plus_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"New Assignment\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredAssignments,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TeacherAssignmentModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    isOpen: isAssignmentModalOpen,\n                    onClose: ()=>{\n                        setIsAssignmentModalOpen(false);\n                        setAssignmentToEdit(null);\n                    },\n                    onSubmit: handleAssignmentSubmit,\n                    assignment: assignmentToEdit,\n                    classes: classes,\n                    subjects: subjects,\n                    teachers: teachers,\n                    periods: periods,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 541,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setAssignmentToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Teacher Assignment\" : \"Delete Selected Assignments\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this teacher assignment? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedAssignments.length, \" selected assignments? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && assignmentToDelete ? \"\".concat(assignmentToDelete.teacher_name, \" - \").concat(assignmentToDelete.class_name, \" (\").concat(assignmentToDelete.subject_name, \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedAssignments.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 557,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n                    lineNumber: 584,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n            lineNumber: 372,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\teacher-assignment\\\\page.tsx\",\n        lineNumber: 371,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherAssignmentPage, \"/n1j+j++YyVrhUTkcwfwhdPaJT8=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = TeacherAssignmentPage;\nvar _c;\n$RefreshReg$(_c, \"TeacherAssignmentPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/teacher-assignment/page.tsx\n"));

/***/ })

});