"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/attendance/page",{

/***/ "(app-pages-browser)/./src/components/modals/AttendanceModal.tsx":
/*!***************************************************!*\
  !*** ./src/components/modals/AttendanceModal.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttendanceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FileCheck2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FileCheck2,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_FileCheck2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileCheck2,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_FileCheck2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileCheck2,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AttendanceModal(param) {\n    let { isOpen, onClose, onSubmit, attendance, students, classes, subjects, schedules, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id: \"\",\n        schedule_id: \"\",\n        status: \"Present\",\n        date: new Date().toISOString().split('T')[0],\n        academic_year: \"2024-2025\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filteredStudents, setFilteredStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Multi-selection states\n    const [isMultiMode, setIsMultiMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedStudents, setSelectedStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [studentStatuses, setStudentStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [bulkStatus, setBulkStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Present\");\n    const isEditing = !!attendance;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (attendance) {\n                    // Single edit mode\n                    setIsMultiMode(false);\n                    setFormData({\n                        student_id: attendance.student_id || \"\",\n                        schedule_id: attendance.schedule_id || \"\",\n                        status: attendance.status || \"Present\",\n                        date: attendance.date ? new Date(attendance.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n                        academic_year: attendance.academic_year || \"2024-2025\"\n                    });\n                } else {\n                    // New attendance - default to multi mode\n                    setIsMultiMode(true);\n                    setFormData({\n                        student_id: \"\",\n                        schedule_id: \"\",\n                        status: \"Present\",\n                        date: new Date().toISOString().split('T')[0],\n                        academic_year: \"2024-2025\"\n                    });\n                }\n                setErrors({});\n                setSelectedStudents(new Set());\n                setStudentStatuses({});\n                setBulkStatus(\"Present\");\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        isOpen,\n        attendance\n    ]);\n    // Filter students based on selected schedule\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AttendanceModal.useEffect\": ()=>{\n            if (formData.schedule_id) {\n                const selectedSchedule = schedules.find({\n                    \"AttendanceModal.useEffect.selectedSchedule\": (s)=>s._id === formData.schedule_id\n                }[\"AttendanceModal.useEffect.selectedSchedule\"]);\n                if (selectedSchedule) {\n                    const classStudents = students.filter({\n                        \"AttendanceModal.useEffect.classStudents\": (student)=>student.class_id === selectedSchedule.class_id\n                    }[\"AttendanceModal.useEffect.classStudents\"]);\n                    setFilteredStudents(classStudents);\n                    // Initialize student statuses for multi-mode\n                    if (isMultiMode) {\n                        const initialStatuses = {};\n                        classStudents.forEach({\n                            \"AttendanceModal.useEffect\": (student)=>{\n                                initialStatuses[student._id] = \"Present\";\n                            }\n                        }[\"AttendanceModal.useEffect\"]);\n                        setStudentStatuses(initialStatuses);\n                    }\n                }\n            } else {\n                setFilteredStudents(students);\n                if (isMultiMode) {\n                    const initialStatuses = {};\n                    students.forEach({\n                        \"AttendanceModal.useEffect\": (student)=>{\n                            initialStatuses[student._id] = \"Present\";\n                        }\n                    }[\"AttendanceModal.useEffect\"]);\n                    setStudentStatuses(initialStatuses);\n                }\n            }\n        }\n    }[\"AttendanceModal.useEffect\"], [\n        formData.schedule_id,\n        schedules,\n        students,\n        isMultiMode\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!isMultiMode && !formData.student_id) {\n            newErrors.student_id = \"Student is required\";\n        }\n        if (isMultiMode && selectedStudents.size === 0) {\n            newErrors.students = \"At least one student must be selected\";\n        }\n        if (!formData.schedule_id) {\n            newErrors.schedule_id = \"Class schedule is required\";\n        }\n        if (!formData.date) {\n            newErrors.date = \"Date is required\";\n        }\n        if (!formData.academic_year) {\n            newErrors.academic_year = \"Academic year is required\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsSubmitting(true);\n        try {\n            if (isMultiMode) {\n                // Submit multiple attendance records\n                const attendancePromises = Array.from(selectedStudents).map((studentId)=>{\n                    const studentStatus = studentStatuses[studentId] || \"Present\";\n                    return onSubmit({\n                        ...formData,\n                        student_id: studentId,\n                        status: studentStatus\n                    });\n                });\n                await Promise.all(attendancePromises);\n            } else {\n                // Submit single attendance record\n                await onSubmit(formData);\n            }\n            onClose();\n        } catch (error) {\n            console.error(\"Error submitting attendance:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    // Multi-selection functions\n    const toggleStudentSelection = (studentId)=>{\n        const newSelected = new Set(selectedStudents);\n        if (newSelected.has(studentId)) {\n            newSelected.delete(studentId);\n        } else {\n            newSelected.add(studentId);\n        }\n        setSelectedStudents(newSelected);\n    };\n    const selectAllStudents = ()=>{\n        const allStudentIds = new Set(filteredStudents.map((s)=>s._id));\n        setSelectedStudents(allStudentIds);\n    };\n    const deselectAllStudents = ()=>{\n        setSelectedStudents(new Set());\n    };\n    const applyBulkStatus = ()=>{\n        const newStatuses = {\n            ...studentStatuses\n        };\n        selectedStudents.forEach((studentId)=>{\n            newStatuses[studentId] = bulkStatus;\n        });\n        setStudentStatuses(newStatuses);\n    };\n    const updateStudentStatus = (studentId, status)=>{\n        setStudentStatuses((prev)=>({\n                ...prev,\n                [studentId]: status\n            }));\n    };\n    const getScheduleDisplay = (schedule)=>{\n        const subject = subjects.find((s)=>s._id === schedule.subject_id);\n        const classInfo = classes.find((c)=>c._id === schedule.class_id);\n        return \"\".concat((subject === null || subject === void 0 ? void 0 : subject.name) || 'Unknown Subject', \" - \").concat((classInfo === null || classInfo === void 0 ? void 0 : classInfo.name) || 'Unknown Class', \" (\").concat(schedule.day_of_week, \")\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-black bg-opacity-50\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full mx-4 \".concat(isMultiMode ? 'max-w-4xl' : 'max-w-md'),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileCheck2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                    children: isEditing ? \"Edit Attendance\" : \"Mark Attendance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: isEditing ? \"Update attendance record\" : \"Create new attendance record\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileCheck2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"p-6 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Class Schedule\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.schedule_id,\n                                            onChange: (e)=>handleInputChange(\"schedule_id\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.schedule_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select class schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, this),\n                                                schedules.map((schedule)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: schedule._id,\n                                                        children: getScheduleDisplay(schedule)\n                                                    }, schedule._id, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.schedule_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.schedule_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Student\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.student_id,\n                                            onChange: (e)=>handleInputChange(\"student_id\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.student_id ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            disabled: !formData.schedule_id,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select student\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this),\n                                                filteredStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: student._id,\n                                                        children: [\n                                                            student.first_name,\n                                                            \" \",\n                                                            student.last_name,\n                                                            \" (\",\n                                                            student.student_id,\n                                                            \")\"\n                                                        ]\n                                                    }, student._id, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.student_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.student_id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.status,\n                                            onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Present\",\n                                                    children: \"Present\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Absent\",\n                                                    children: \"Absent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Late\",\n                                                    children: \"Late\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Excused\",\n                                                    children: \"Excused\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Date\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.date,\n                                            onChange: (e)=>handleInputChange(\"date\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.date ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.date\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"Academic Year\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.academic_year,\n                                            onChange: (e)=>handleInputChange(\"academic_year\", e.target.value),\n                                            className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white \".concat(errors.academic_year ? \"border-red-500 dark:border-red-500\" : \"border-gray-300 dark:border-gray-600\"),\n                                            placeholder: \"e.g., 2024-2025\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.academic_year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-500\",\n                                            children: errors.academic_year\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: onClose,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            disabled: isSubmitting,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                            children: [\n                                                isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileCheck2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isSubmitting ? \"Saving...\" : isEditing ? \"Update\" : \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n            lineNumber: 215,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\AttendanceModal.tsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, this);\n}\n_s(AttendanceModal, \"3tsxPmqIApOmqHrWSp1PJC8s8Qw=\");\n_c = AttendanceModal;\nvar _c;\n$RefreshReg$(_c, \"AttendanceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/AttendanceModal.tsx\n"));

/***/ })

});