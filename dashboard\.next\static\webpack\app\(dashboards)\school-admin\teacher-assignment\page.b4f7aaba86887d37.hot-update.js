"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/teacher-assignment/page",{

/***/ "(app-pages-browser)/./src/components/modals/TeacherAssignmentModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/modals/TeacherAssignmentModal.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherAssignmentModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst DAYS = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday',\n    'Saturday',\n    'Sunday'\n];\nconst SCHEDULE_TYPES = [\n    'Normal',\n    'Exam',\n    'Event'\n];\nfunction TeacherAssignmentModal(param) {\n    let { isOpen, onClose, onSubmit, assignment, classes, subjects, teachers, periods, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        class_id: '',\n        subject_id: '',\n        teacher_id: '',\n        period_id: '',\n        day_of_week: '',\n        schedule_type: 'Normal'\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Search states\n    const [classSearch, setClassSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [subjectSearch, setSubjectSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [teacherSearch, setTeacherSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showClassDropdown, setShowClassDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSubjectDropdown, setShowSubjectDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTeacherDropdown, setShowTeacherDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Reset form when modal opens/closes or assignment changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherAssignmentModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (assignment) {\n                    // Edit mode - populate form with assignment data\n                    // Extract IDs from objects if they exist\n                    const classId = typeof assignment.class_id === 'object' ? assignment.class_id._id : assignment.class_id;\n                    const subjectId = typeof assignment.subject_id === 'object' ? assignment.subject_id._id : assignment.subject_id;\n                    const teacherId = typeof assignment.teacher_id === 'object' ? assignment.teacher_id._id : assignment.teacher_id;\n                    const periodId = typeof assignment.period_id === 'object' ? assignment.period_id._id : assignment.period_id;\n                    setFormData({\n                        class_id: classId || '',\n                        subject_id: subjectId || '',\n                        teacher_id: teacherId || '',\n                        period_id: periodId || '',\n                        day_of_week: assignment.day_of_week || '',\n                        schedule_type: assignment.schedule_type || 'Normal'\n                    });\n                } else {\n                    // Create mode - reset form\n                    setFormData({\n                        class_id: '',\n                        subject_id: '',\n                        teacher_id: '',\n                        period_id: '',\n                        day_of_week: '',\n                        schedule_type: 'Normal'\n                    });\n                }\n                setErrors({});\n            }\n        }\n    }[\"TeacherAssignmentModal.useEffect\"], [\n        isOpen,\n        assignment\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: ''\n                }));\n        }\n    };\n    // Filter functions\n    const filteredClasses = classes.filter((cls)=>cls.name.toLowerCase().includes(classSearch.toLowerCase()));\n    const filteredSubjects = subjects.filter((subject)=>subject.name.toLowerCase().includes(subjectSearch.toLowerCase()));\n    const filteredTeachers = teachers.filter((teacher)=>{\n        const teacherName = teacher.first_name && teacher.last_name ? \"\".concat(teacher.first_name, \" \").concat(teacher.last_name) : teacher.name || 'Unknown Teacher';\n        return teacherName.toLowerCase().includes(teacherSearch.toLowerCase());\n    });\n    // Get selected item names for display\n    const getSelectedClassName = ()=>{\n        const selectedClass = classes.find((cls)=>cls._id === formData.class_id);\n        return selectedClass ? selectedClass.name : '';\n    };\n    const getSelectedSubjectName = ()=>{\n        const selectedSubject = subjects.find((subject)=>subject._id === formData.subject_id);\n        return selectedSubject ? selectedSubject.name : '';\n    };\n    const getSelectedTeacherName = ()=>{\n        const selectedTeacher = teachers.find((teacher)=>teacher._id === formData.teacher_id);\n        if (!selectedTeacher) return '';\n        return selectedTeacher.first_name && selectedTeacher.last_name ? \"\".concat(selectedTeacher.first_name, \" \").concat(selectedTeacher.last_name) : selectedTeacher.name || 'Unknown Teacher';\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.class_id) newErrors.class_id = 'Class is required';\n        if (!formData.subject_id) newErrors.subject_id = 'Subject is required';\n        if (!formData.teacher_id) newErrors.teacher_id = 'Teacher is required';\n        if (!formData.period_id) newErrors.period_id = 'Period is required';\n        if (!formData.day_of_week) newErrors.day_of_week = 'Day of week is required';\n        if (!formData.schedule_type) newErrors.schedule_type = 'Schedule type is required';\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsSubmitting(true);\n        try {\n            await onSubmit(formData);\n            onClose();\n        } catch (error) {\n            console.error('Error submitting assignment:', error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleClose = ()=>{\n        if (!isSubmitting && !loading) {\n            onClose();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.95\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                exit: {\n                    opacity: 0,\n                    scale: 0.95\n                },\n                className: \"bg-widget rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-b border-stroke\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-foreground\",\n                                children: assignment ? 'Edit Teacher Assignment' : 'New Teacher Assignment'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClose,\n                                disabled: isSubmitting || loading,\n                                className: \"p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors disabled:opacity-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5 text-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"p-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Class \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.class_id,\n                                                onChange: (e)=>handleInputChange('class_id', e.target.value),\n                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.class_id ? 'border-red-500' : 'border-stroke'),\n                                                disabled: isSubmitting || loading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select a class\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: classItem._id,\n                                                            children: classItem.name\n                                                        }, classItem._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.class_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.class_id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Subject \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.subject_id,\n                                                onChange: (e)=>handleInputChange('subject_id', e.target.value),\n                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.subject_id ? 'border-red-500' : 'border-stroke'),\n                                                disabled: isSubmitting || loading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select a subject\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: subject._id,\n                                                            children: subject.name\n                                                        }, subject._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.subject_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.subject_id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Teacher \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.teacher_id,\n                                                onChange: (e)=>handleInputChange('teacher_id', e.target.value),\n                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.teacher_id ? 'border-red-500' : 'border-stroke'),\n                                                disabled: isSubmitting || loading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select a teacher\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    teachers.map((teacher)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: teacher._id,\n                                                            children: teacher.first_name && teacher.last_name ? \"\".concat(teacher.first_name, \" \").concat(teacher.last_name) : teacher.name || 'Unknown Teacher'\n                                                        }, teacher._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.teacher_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.teacher_id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Period \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 26\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.period_id,\n                                                onChange: (e)=>handleInputChange('period_id', e.target.value),\n                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.period_id ? 'border-red-500' : 'border-stroke'),\n                                                disabled: isSubmitting || loading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select a period\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    periods.map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: period._id,\n                                                            children: [\n                                                                \"Period \",\n                                                                period.period_number,\n                                                                \" (\",\n                                                                period.start_time.slice(0, 5),\n                                                                \" - \",\n                                                                period.end_time.slice(0, 5),\n                                                                \")\"\n                                                            ]\n                                                        }, period._id, true, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.period_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.period_id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Day of Week \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.day_of_week,\n                                                onChange: (e)=>handleInputChange('day_of_week', e.target.value),\n                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.day_of_week ? 'border-red-500' : 'border-stroke'),\n                                                disabled: isSubmitting || loading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select a day\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    DAYS.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: day,\n                                                            children: day\n                                                        }, day, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.day_of_week && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.day_of_week\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-foreground mb-2\",\n                                                children: [\n                                                    \"Schedule Type \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.schedule_type,\n                                                onChange: (e)=>handleInputChange('schedule_type', e.target.value),\n                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground \".concat(errors.schedule_type ? 'border-red-500' : 'border-stroke'),\n                                                disabled: isSubmitting || loading,\n                                                children: SCHEDULE_TYPES.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: type,\n                                                        children: type\n                                                    }, type, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.schedule_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.schedule_type\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end space-x-4 pt-6 border-t border-stroke\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleClose,\n                                        disabled: isSubmitting || loading,\n                                        className: \"px-4 py-2 text-foreground/70 hover:text-foreground transition-colors disabled:opacity-50\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || loading,\n                                        className: \"flex items-center space-x-2 px-6 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors disabled:opacity-50\",\n                                        children: [\n                                            (isSubmitting || loading) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 47\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: assignment ? 'Update Assignment' : 'Create Assignment'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\components\\\\modals\\\\TeacherAssignmentModal.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherAssignmentModal, \"EFVpswJncb6aZCXjmpE3XdOkRwE=\");\n_c = TeacherAssignmentModal;\nvar _c;\n$RefreshReg$(_c, \"TeacherAssignmentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/TeacherAssignmentModal.tsx\n"));

/***/ })

});