"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/grades/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/grades/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GradesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/GradeServices */ \"(app-pages-browser)/./src/app/services/GradeServices.tsx\");\n/* harmony import */ var _app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/StudentServices */ \"(app-pages-browser)/./src/app/services/StudentServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/ExamTypeServices */ \"(app-pages-browser)/./src/app/services/ExamTypeServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_UserServices__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/services/UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/GradeModal */ \"(app-pages-browser)/./src/components/modals/GradeModal.tsx\");\n/* harmony import */ var _components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/PasswordConfirmDeleteModal */ \"(app-pages-browser)/./src/components/modals/PasswordConfirmDeleteModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    baseHref: \"/school-admin/grades\",\n    title: \"Grades\"\n};\nfunction GradesPage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    // State management\n    const [gradeRecords, setGradeRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalGrades: 0,\n        averageScore: 0,\n        highestScore: 0,\n        lowestScore: 0,\n        passRate: 0\n    });\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedTerm, setSelectedTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedExamType, setSelectedExamType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isGradeModalOpen, setIsGradeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gradeToEdit, setGradeToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gradeToDelete, setGradeToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedGrades, setSelectedGrades] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [examTypes, setExamTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Export states\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showExportDropdown, setShowExportDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch grade data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchGradeData = {\n                \"GradesPage.useEffect.fetchGradeData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n                        if (selectedTerm !== 'all') filters.term = selectedTerm;\n                        if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n                        // Fetch records and stats in parallel\n                        const [recordsResponse, statsResponse] = await Promise.all([\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n                        ]);\n                        setGradeRecords(recordsResponse.grade_records);\n                        setStats(statsResponse.stats);\n                    } catch (error) {\n                        console.error(\"Error fetching grade data:\", error);\n                        showError(\"Error\", \"Failed to load grade data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchGradeData\"];\n            fetchGradeData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId,\n        selectedClass,\n        selectedSubject,\n        selectedTerm,\n        selectedExamType\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"GradesPage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__.getStudentsBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__.getExamTypes)(),\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_11__.getClassesBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setStudents(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch students:\", results[0].reason);\n                            setStudents([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setExamTypes(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch exam types:\", results[2].reason);\n                            setExamTypes([]);\n                        }\n                        if (results[3].status === 'fulfilled') {\n                            setClasses(results[3].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[3].reason);\n                            setClasses([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateGrade = ()=>{\n        setGradeToEdit(null);\n        setIsGradeModalOpen(true);\n    };\n    const handleEditGrade = (grade)=>{\n        setGradeToEdit(grade);\n        setIsGradeModalOpen(true);\n    };\n    const handleDeleteGrade = (grade)=>{\n        setGradeToDelete(grade);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedGrades(selectedRows);\n    };\n    // Modal submission functions\n    const handleGradeSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (gradeToEdit) {\n                // Update existing grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.updateGrade)(gradeToEdit._id, data);\n            } else {\n                // Create new grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.createGrade)({\n                    ...data,\n                    school_id: schoolId\n                });\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsGradeModalOpen(false);\n            setGradeToEdit(null);\n            // Show success notification\n            showSuccess(gradeToEdit ? \"Grade Updated\" : \"Grade Added\", gradeToEdit ? \"Grade has been updated successfully.\" : \"New grade has been added successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting grade:\", error);\n            showError(\"Error\", \"Failed to save grade. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteConfirm = async (password)=>{\n        if (!schoolId || !user) return;\n        try {\n            // Verify password\n            const passwordVerified = await (0,_app_services_UserServices__WEBPACK_IMPORTED_MODULE_12__.verifyPassword)(password, user.email);\n            if (!passwordVerified) {\n                showError(\"Error\", \"Invalid password. Please try again.\");\n                throw new Error(\"Invalid password\");\n            }\n            if (deleteType === \"single\" && gradeToDelete) {\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteGrade)(gradeToDelete._id);\n            } else if (deleteType === \"multiple\") {\n                const selectedIds = selectedGrades.map((g)=>g._id);\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteMultipleGrades)(selectedIds);\n                setClearSelection(true);\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsDeleteModalOpen(false);\n            setGradeToDelete(null);\n            setSelectedGrades([]);\n            // Show success notification\n            if (deleteType === \"single\") {\n                showSuccess(\"Grade Deleted\", \"Grade record has been deleted successfully.\");\n            } else {\n                showSuccess(\"Grades Deleted\", \"\".concat(selectedGrades.length, \" grade records have been deleted successfully.\"));\n            }\n        } catch (error) {\n            console.error(\"Error deleting grade(s):\", error);\n            if (error.message !== \"Invalid password\") {\n                showError(\"Error\", \"Failed to delete grade record(s). Please try again.\");\n            }\n            throw error;\n        }\n    };\n    const getGradeColor = (grade)=>{\n        // Handle both old format (A+, A, B+, etc.) and new format (A+/Excellent, etc.)\n        const gradePrefix = grade.split('/')[0];\n        switch(gradePrefix){\n            case 'A+':\n            case 'A':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n            case 'B+':\n            case 'B':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n            case 'C+':\n            case 'C':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n            case 'D+':\n            case 'D':\n            case 'F':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n        }\n    };\n    const getScoreColor = (score)=>{\n        // Convert percentage to /20 scale for color determination\n        const scoreOn20 = score * 20 / 100;\n        if (scoreOn20 >= 18) return 'text-green-600 font-semibold'; // 18-20/20 (90-100%)\n        if (scoreOn20 >= 16) return 'text-blue-600 font-semibold'; // 16-17.9/20 (80-89%)\n        if (scoreOn20 >= 14) return 'text-yellow-600 font-semibold'; // 14-15.9/20 (70-79%)\n        if (scoreOn20 >= 12) return 'text-orange-600 font-semibold'; // 12-13.9/20 (60-69%)\n        return 'text-red-600 font-semibold'; // <12/20 (<60%)\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Student\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-foreground\",\n                            children: row.student_name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-foreground/60\",\n                            children: row.student_id\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>\"\".concat(row.student_name, \" \").concat(row.student_id)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.class_name\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.subject_name\n        },\n        {\n            header: \"Exam Type\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.exam_type\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.exam_type\n        },\n        {\n            header: \"Score\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-bold \".concat(getScoreColor(row.score)),\n                    children: [\n                        (row.score * 20 / 100).toFixed(1),\n                        \"/20\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.score.toString()\n        },\n        {\n            header: \"Grade\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-sm font-bold \".concat(getGradeColor(row.grade)),\n                    children: row.grade\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.grade\n        },\n        {\n            header: \"Term\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.term\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.term\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (grade)=>{\n                handleEditGrade(grade);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (grade)=>{\n                handleDeleteGrade(grade);\n            }\n        }\n    ];\n    // Filter data based on selections - Note: Backend filtering is primary, this is just for display consistency\n    const filteredRecords = gradeRecords;\n    // Export functions\n    const handleExportPDF = async ()=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsExporting(true);\n        try {\n            const filters = {\n                class_id: selectedClass !== 'all' ? selectedClass : undefined,\n                subject_id: selectedSubject !== 'all' ? selectedSubject : undefined,\n                term: selectedTerm !== 'all' ? selectedTerm : undefined,\n                exam_type_id: selectedExamType !== 'all' ? selectedExamType : undefined\n            };\n            const blob = await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.exportGradesPDF)(schoolId, filters);\n            // Create download link\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"grades-export-\".concat(new Date().toISOString().split('T')[0], \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            showSuccess(\"Export Successful\", \"Grades have been exported to PDF successfully.\");\n            setShowExportDropdown(false);\n        } catch (error) {\n            console.error(\"Error exporting to PDF:\", error);\n            showError(\"Export Failed\", \"Failed to export grades to PDF. Please try again.\");\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const handleExportExcel = async ()=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsExporting(true);\n        try {\n            const filters = {\n                class_id: selectedClass !== 'all' ? selectedClass : undefined,\n                subject_id: selectedSubject !== 'all' ? selectedSubject : undefined,\n                term: selectedTerm !== 'all' ? selectedTerm : undefined,\n                exam_type_id: selectedExamType !== 'all' ? selectedExamType : undefined,\n                academic_year: selectedAcademicYear !== 'all' ? selectedAcademicYear : undefined\n            };\n            const blob = await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.exportGradesExcel)(schoolId, filters);\n            // Create download link\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"grades-export-\".concat(new Date().toISOString().split('T')[0], \".xlsx\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            showSuccess(\"Export Successful\", \"Grades have been exported to Excel successfully.\");\n            setShowExportDropdown(false);\n        } catch (error) {\n            console.error(\"Error exporting to Excel:\", error);\n            showError(\"Export Failed\", \"Failed to export grades to Excel. Please try again.\");\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                lineNumber: 503,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 502,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Grades Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Monitor and manage student grades across all subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    (stats.averageScore * 20 / 100).toFixed(1),\n                                                    \"/20\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Average Score\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-5 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Grades\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: stats.totalGrades\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Average Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            (stats.averageScore * 20 / 100).toFixed(1),\n                                                            \"/20\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-teal rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Highest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: [\n                                                            (stats.highestScore * 20 / 100).toFixed(1),\n                                                            \"/20\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Lowest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: [\n                                                            (stats.lowestScore * 20 / 100).toFixed(1),\n                                                            \"/20\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Pass Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            stats.passRate,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((cls)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: cls._id,\n                                                            children: cls.name\n                                                        }, cls._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Subject:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedSubject,\n                                                onChange: (e)=>setSelectedSubject(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: subject._id,\n                                                            children: subject.name\n                                                        }, subject._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Term:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedTerm,\n                                                onChange: (e)=>setSelectedTerm(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Terms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"First Term\",\n                                                        children: \"First Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Second Term\",\n                                                        children: \"Second Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Third Term\",\n                                                        children: \"Third Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Exam Type:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedExamType,\n                                                onChange: (e)=>setSelectedExamType(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    examTypes.map((examType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: examType._id,\n                                                            children: examType.type\n                                                        }, examType._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Grade Records (\",\n                                                filteredRecords.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateGrade,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Grade\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredRecords,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 696,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 679,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    isOpen: isGradeModalOpen,\n                    onClose: ()=>{\n                        setIsGradeModalOpen(false);\n                        setGradeToEdit(null);\n                    },\n                    onSubmit: handleGradeSubmit,\n                    grade: gradeToEdit,\n                    students: students,\n                    subjects: subjects,\n                    examTypes: examTypes,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 712,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setGradeToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Grade Record\" : \"Delete Selected Grade Records\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this grade record? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedGrades.length, \" selected grade records? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && gradeToDelete ? \"\".concat(gradeToDelete.student_name, \" - \").concat(gradeToDelete.subject_name, \" (\").concat(gradeToDelete.exam_type, \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedGrades.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 727,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 754,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 510,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n        lineNumber: 509,\n        columnNumber: 5\n    }, this);\n}\n_s(GradesPage, \"sCkfk1/74HB4i9qZ2zMbxx1B86M=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = GradesPage;\nvar _c;\n$RefreshReg$(_c, \"GradesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx\n"));

/***/ })

});