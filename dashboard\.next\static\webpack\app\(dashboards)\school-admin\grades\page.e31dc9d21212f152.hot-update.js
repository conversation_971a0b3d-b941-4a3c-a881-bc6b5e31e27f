"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/grades/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/grades/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GradesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/GradeServices */ \"(app-pages-browser)/./src/app/services/GradeServices.tsx\");\n/* harmony import */ var _app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/StudentServices */ \"(app-pages-browser)/./src/app/services/StudentServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/ExamTypeServices */ \"(app-pages-browser)/./src/app/services/ExamTypeServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_UserServices__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/services/UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/GradeModal */ \"(app-pages-browser)/./src/components/modals/GradeModal.tsx\");\n/* harmony import */ var _components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/PasswordConfirmDeleteModal */ \"(app-pages-browser)/./src/components/modals/PasswordConfirmDeleteModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    baseHref: \"/school-admin/grades\",\n    title: \"Grades\"\n};\nfunction GradesPage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    // State management\n    const [gradeRecords, setGradeRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalGrades: 0,\n        averageScore: 0,\n        highestScore: 0,\n        lowestScore: 0,\n        passRate: 0\n    });\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedTerm, setSelectedTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedExamType, setSelectedExamType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isGradeModalOpen, setIsGradeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gradeToEdit, setGradeToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gradeToDelete, setGradeToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedGrades, setSelectedGrades] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [examTypes, setExamTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Export states\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showExportDropdown, setShowExportDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch grade data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchGradeData = {\n                \"GradesPage.useEffect.fetchGradeData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n                        if (selectedTerm !== 'all') filters.term = selectedTerm;\n                        if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n                        // Fetch records and stats in parallel\n                        const [recordsResponse, statsResponse] = await Promise.all([\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n                        ]);\n                        setGradeRecords(recordsResponse.grade_records);\n                        setStats(statsResponse.stats);\n                    } catch (error) {\n                        console.error(\"Error fetching grade data:\", error);\n                        showError(\"Error\", \"Failed to load grade data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchGradeData\"];\n            fetchGradeData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId,\n        selectedClass,\n        selectedSubject,\n        selectedTerm,\n        selectedExamType\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"GradesPage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__.getStudentsBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__.getExamTypes)(),\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_11__.getClassesBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setStudents(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch students:\", results[0].reason);\n                            setStudents([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setExamTypes(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch exam types:\", results[2].reason);\n                            setExamTypes([]);\n                        }\n                        if (results[3].status === 'fulfilled') {\n                            setClasses(results[3].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[3].reason);\n                            setClasses([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateGrade = ()=>{\n        setGradeToEdit(null);\n        setIsGradeModalOpen(true);\n    };\n    const handleEditGrade = (grade)=>{\n        setGradeToEdit(grade);\n        setIsGradeModalOpen(true);\n    };\n    const handleDeleteGrade = (grade)=>{\n        setGradeToDelete(grade);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedGrades(selectedRows);\n    };\n    // Modal submission functions\n    const handleGradeSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (gradeToEdit) {\n                // Update existing grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.updateGrade)(gradeToEdit._id, data);\n            } else {\n                // Create new grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.createGrade)({\n                    ...data,\n                    school_id: schoolId\n                });\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsGradeModalOpen(false);\n            setGradeToEdit(null);\n            // Show success notification\n            showSuccess(gradeToEdit ? \"Grade Updated\" : \"Grade Added\", gradeToEdit ? \"Grade has been updated successfully.\" : \"New grade has been added successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting grade:\", error);\n            showError(\"Error\", \"Failed to save grade. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteConfirm = async (password)=>{\n        if (!schoolId || !user) return;\n        try {\n            // Verify password\n            const passwordVerified = await (0,_app_services_UserServices__WEBPACK_IMPORTED_MODULE_12__.verifyPassword)(password, user.email);\n            if (!passwordVerified) {\n                showError(\"Error\", \"Invalid password. Please try again.\");\n                throw new Error(\"Invalid password\");\n            }\n            if (deleteType === \"single\" && gradeToDelete) {\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteGrade)(gradeToDelete._id);\n            } else if (deleteType === \"multiple\") {\n                const selectedIds = selectedGrades.map((g)=>g._id);\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteMultipleGrades)(selectedIds);\n                setClearSelection(true);\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsDeleteModalOpen(false);\n            setGradeToDelete(null);\n            setSelectedGrades([]);\n            // Show success notification\n            if (deleteType === \"single\") {\n                showSuccess(\"Grade Deleted\", \"Grade record has been deleted successfully.\");\n            } else {\n                showSuccess(\"Grades Deleted\", \"\".concat(selectedGrades.length, \" grade records have been deleted successfully.\"));\n            }\n        } catch (error) {\n            console.error(\"Error deleting grade(s):\", error);\n            if (error.message !== \"Invalid password\") {\n                showError(\"Error\", \"Failed to delete grade record(s). Please try again.\");\n            }\n            throw error;\n        }\n    };\n    const getGradeColor = (grade)=>{\n        // Handle both old format (A+, A, B+, etc.) and new format (A+/Excellent, etc.)\n        const gradePrefix = grade.split('/')[0];\n        switch(gradePrefix){\n            case 'A+':\n            case 'A':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n            case 'B+':\n            case 'B':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n            case 'C+':\n            case 'C':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n            case 'D+':\n            case 'D':\n            case 'F':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n        }\n    };\n    const getScoreColor = (score)=>{\n        // Convert percentage to /20 scale for color determination\n        const scoreOn20 = score * 20 / 100;\n        if (scoreOn20 >= 18) return 'text-green-600 font-semibold'; // 18-20/20 (90-100%)\n        if (scoreOn20 >= 16) return 'text-blue-600 font-semibold'; // 16-17.9/20 (80-89%)\n        if (scoreOn20 >= 14) return 'text-yellow-600 font-semibold'; // 14-15.9/20 (70-79%)\n        if (scoreOn20 >= 12) return 'text-orange-600 font-semibold'; // 12-13.9/20 (60-69%)\n        return 'text-red-600 font-semibold'; // <12/20 (<60%)\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Student\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-foreground\",\n                            children: row.student_name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-foreground/60\",\n                            children: row.student_id\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>\"\".concat(row.student_name, \" \").concat(row.student_id)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.class_name\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.subject_name\n        },\n        {\n            header: \"Exam Type\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.exam_type\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.exam_type\n        },\n        {\n            header: \"Score\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-bold \".concat(getScoreColor(row.score)),\n                    children: [\n                        (row.score * 20 / 100).toFixed(1),\n                        \"/20\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.score.toString()\n        },\n        {\n            header: \"Grade\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-sm font-bold \".concat(getGradeColor(row.grade)),\n                    children: row.grade\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.grade\n        },\n        {\n            header: \"Term\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.term\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 9\n                }, this),\n            searchValue: (row)=>row.term\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (grade)=>{\n                handleEditGrade(grade);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (grade)=>{\n                handleDeleteGrade(grade);\n            }\n        }\n    ];\n    // Filter data based on selections - Note: Backend filtering is primary, this is just for display consistency\n    const filteredRecords = gradeRecords;\n    // Export functions\n    const handleExportPDF = async ()=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsExporting(true);\n        try {\n            const filters = {\n                class_id: selectedClass !== 'all' ? selectedClass : undefined,\n                subject_id: selectedSubject !== 'all' ? selectedSubject : undefined,\n                term: selectedTerm !== 'all' ? selectedTerm : undefined,\n                exam_type_id: selectedExamType !== 'all' ? selectedExamType : undefined\n            };\n            const blob = await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.exportGradesPDF)(schoolId, filters);\n            // Create download link\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"grades-export-\".concat(new Date().toISOString().split('T')[0], \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            showSuccess(\"Export Successful\", \"Grades have been exported to PDF successfully.\");\n            setShowExportDropdown(false);\n        } catch (error) {\n            console.error(\"Error exporting to PDF:\", error);\n            showError(\"Export Failed\", \"Failed to export grades to PDF. Please try again.\");\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const handleExportExcel = async ()=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsExporting(true);\n        try {\n            const filters = {\n                class_id: selectedClass !== 'all' ? selectedClass : undefined,\n                subject_id: selectedSubject !== 'all' ? selectedSubject : undefined,\n                term: selectedTerm !== 'all' ? selectedTerm : undefined,\n                exam_type_id: selectedExamType !== 'all' ? selectedExamType : undefined\n            };\n            const blob = await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.exportGradesExcel)(schoolId, filters);\n            // Create download link\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"grades-export-\".concat(new Date().toISOString().split('T')[0], \".xlsx\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            showSuccess(\"Export Successful\", \"Grades have been exported to Excel successfully.\");\n            setShowExportDropdown(false);\n        } catch (error) {\n            console.error(\"Error exporting to Excel:\", error);\n            showError(\"Export Failed\", \"Failed to export grades to Excel. Please try again.\");\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 501,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Grades Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Monitor and manage student grades across all subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    (stats.averageScore * 20 / 100).toFixed(1),\n                                                    \"/20\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Average Score\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-5 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Grades\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: stats.totalGrades\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Average Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            (stats.averageScore * 20 / 100).toFixed(1),\n                                                            \"/20\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-teal rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Highest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: [\n                                                            (stats.highestScore * 20 / 100).toFixed(1),\n                                                            \"/20\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Lowest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: [\n                                                            (stats.lowestScore * 20 / 100).toFixed(1),\n                                                            \"/20\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Pass Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            stats.passRate,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((cls)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: cls._id,\n                                                            children: cls.name\n                                                        }, cls._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Subject:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedSubject,\n                                                onChange: (e)=>setSelectedSubject(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: subject._id,\n                                                            children: subject.name\n                                                        }, subject._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Term:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedTerm,\n                                                onChange: (e)=>setSelectedTerm(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Terms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"First Term\",\n                                                        children: \"First Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Second Term\",\n                                                        children: \"Second Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Third Term\",\n                                                        children: \"Third Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Exam Type:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedExamType,\n                                                onChange: (e)=>setSelectedExamType(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    examTypes.map((examType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: examType._id,\n                                                            children: examType.type\n                                                        }, examType._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 597,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Grade Records (\",\n                                                filteredRecords.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateGrade,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Grade\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 684,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredRecords,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 510,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    isOpen: isGradeModalOpen,\n                    onClose: ()=>{\n                        setIsGradeModalOpen(false);\n                        setGradeToEdit(null);\n                    },\n                    onSubmit: handleGradeSubmit,\n                    grade: gradeToEdit,\n                    students: students,\n                    subjects: subjects,\n                    examTypes: examTypes,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 711,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setGradeToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Grade Record\" : \"Delete Selected Grade Records\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this grade record? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedGrades.length, \" selected grade records? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && gradeToDelete ? \"\".concat(gradeToDelete.student_name, \" - \").concat(gradeToDelete.subject_name, \" (\").concat(gradeToDelete.exam_type, \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedGrades.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 726,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 753,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 509,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n        lineNumber: 508,\n        columnNumber: 5\n    }, this);\n}\n_s(GradesPage, \"sCkfk1/74HB4i9qZ2zMbxx1B86M=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = GradesPage;\nvar _c;\n$RefreshReg$(_c, \"GradesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx\n"));

/***/ })

});