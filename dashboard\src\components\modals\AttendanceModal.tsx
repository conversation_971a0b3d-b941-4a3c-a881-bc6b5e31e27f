"use client";

import React, { useState, useEffect } from "react";
import { X, FileCheck2, Save, Users, CheckSquare, Square, Search, ChevronDown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface AttendanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  attendance?: any | null;
  students: any[];
  classes: any[];
  subjects: any[];
  schedules: any[];
  loading?: boolean;
}

export default function AttendanceModal({
  isOpen,
  onClose,
  onSubmit,
  attendance,
  students,
  classes,
  subjects,
  schedules,
  loading = false
}: AttendanceModalProps) {
  const [formData, setFormData] = useState({
    student_id: "",
    schedule_id: "",
    status: "Present" as "Present" | "Absent" | "Late" | "Excused",
    date: new Date().toISOString().split('T')[0],
    academic_year: "2024-2025"
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [filteredStudents, setFilteredStudents] = useState<any[]>([]);

  // Multi-selection states
  const [isMultiMode, setIsMultiMode] = useState(false);
  const [selectedStudents, setSelectedStudents] = useState<Set<string>>(new Set());
  const [studentStatuses, setStudentStatuses] = useState<Record<string, string>>({});
  const [bulkStatus, setBulkStatus] = useState<"Present" | "Absent" | "Late" | "Excused">("Present");

  // Search states
  const [scheduleSearch, setScheduleSearch] = useState('');
  const [studentSearch, setStudentSearch] = useState('');
  const [showScheduleDropdown, setShowScheduleDropdown] = useState(false);
  const [showStudentDropdown, setShowStudentDropdown] = useState(false);

  const isEditing = !!attendance;

  useEffect(() => {
    if (isOpen) {
      if (attendance) {
        // Single edit mode
        setIsMultiMode(false);
        setFormData({
          student_id: attendance.student_id || "",
          schedule_id: attendance.schedule_id || "",
          status: attendance.status || "Present",
          date: attendance.date ? new Date(attendance.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
          academic_year: attendance.academic_year || "2024-2025"
        });
      } else {
        // New attendance - default to multi mode
        setIsMultiMode(true);
        setFormData({
          student_id: "",
          schedule_id: "",
          status: "Present",
          date: new Date().toISOString().split('T')[0],
          academic_year: "2024-2025"
        });
      }
      setErrors({});
      setSelectedStudents(new Set());
      setStudentStatuses({});
      setBulkStatus("Present");
    }
  }, [isOpen, attendance]);

  // Filter students based on selected schedule
  useEffect(() => {
    if (formData.schedule_id) {
      const selectedSchedule = schedules.find(s => s._id === formData.schedule_id);
      if (selectedSchedule) {
        const classStudents = students.filter(student =>
          student.class_id === selectedSchedule.class_id
        );
        setFilteredStudents(classStudents);

        // Initialize student statuses for multi-mode
        if (isMultiMode) {
          const initialStatuses: Record<string, string> = {};
          classStudents.forEach(student => {
            initialStatuses[student._id] = "Present";
          });
          setStudentStatuses(initialStatuses);
        }
      }
    } else {
      setFilteredStudents(students);
      if (isMultiMode) {
        const initialStatuses: Record<string, string> = {};
        students.forEach(student => {
          initialStatuses[student._id] = "Present";
        });
        setStudentStatuses(initialStatuses);
      }
    }
  }, [formData.schedule_id, schedules, students, isMultiMode]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShowScheduleDropdown(false);
      setShowStudentDropdown(false);
    };

    if (showScheduleDropdown || showStudentDropdown) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showScheduleDropdown, showStudentDropdown]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!isMultiMode && !formData.student_id) {
      newErrors.student_id = "Student is required";
    }
    if (isMultiMode && selectedStudents.size === 0) {
      newErrors.students = "At least one student must be selected";
    }
    if (!formData.schedule_id) {
      newErrors.schedule_id = "Class schedule is required";
    }
    if (!formData.date) {
      newErrors.date = "Date is required";
    }
    if (!formData.academic_year) {
      newErrors.academic_year = "Academic year is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log('handleSubmit called', { isMultiMode, selectedStudents: selectedStudents.size, formData }); // Debug log

    if (!validateForm()) {
      console.log('Validation failed'); // Debug log
      return;
    }

    setIsSubmitting(true);
    try {
      if (isMultiMode) {
        console.log('Multi-mode submission', { selectedStudents: Array.from(selectedStudents), studentStatuses }); // Debug log
        // Submit multiple attendance records
        const attendancePromises = Array.from(selectedStudents).map(studentId => {
          const studentStatus = studentStatuses[studentId] || "Present";
          const submissionData = {
            ...formData,
            student_id: studentId,
            status: studentStatus
          };
          console.log('Submitting for student:', submissionData); // Debug log
          return onSubmit(submissionData);
        });

        await Promise.all(attendancePromises);
        console.log('Multi-mode submission completed'); // Debug log
      } else {
        console.log('Single-mode submission', formData); // Debug log
        // Submit single attendance record
        await onSubmit(formData);
        console.log('Single-mode submission completed'); // Debug log
      }
      onClose();
    } catch (error) {
      console.error("Error submitting attendance:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  // Multi-selection functions
  const toggleStudentSelection = (studentId: string) => {
    const newSelected = new Set(selectedStudents);
    if (newSelected.has(studentId)) {
      newSelected.delete(studentId);
    } else {
      newSelected.add(studentId);
    }
    setSelectedStudents(newSelected);
  };

  const selectAllStudents = () => {
    console.log('selectAllStudents called, filteredStudents:', filteredStudents); // Debug log
    if (filteredStudents && filteredStudents.length > 0) {
      const allStudentIds = new Set(filteredStudents.map(s => s._id));
      console.log('Setting selected students:', allStudentIds); // Debug log
      setSelectedStudents(allStudentIds);

      // Initialize statuses for all selected students
      const newStatuses = { ...studentStatuses };
      filteredStudents.forEach(student => {
        if (!newStatuses[student._id]) {
          newStatuses[student._id] = "Present";
        }
      });
      setStudentStatuses(newStatuses);
    } else {
      console.log('No filtered students available'); // Debug log
    }
  };

  const deselectAllStudents = () => {
    setSelectedStudents(new Set());
  };

  const applyBulkStatus = () => {
    const newStatuses = { ...studentStatuses };
    selectedStudents.forEach(studentId => {
      newStatuses[studentId] = bulkStatus;
    });
    setStudentStatuses(newStatuses);
  };

  const updateStudentStatus = (studentId: string, status: string) => {
    setStudentStatuses(prev => ({
      ...prev,
      [studentId]: status
    }));
  };

  const getScheduleDisplay = (schedule: any) => {
    const subject = subjects.find(s => s._id === schedule.subject_id);
    const classInfo = classes.find(c => c._id === schedule.class_id);
    return `${subject?.name || 'Unknown Subject'} - ${classInfo?.name || 'Unknown Class'} (${schedule.day_of_week})`;
  };

  // Helper functions for search and selection
  const getSelectedScheduleName = () => {
    const selectedSchedule = schedules.find(s => s._id === formData.schedule_id);
    return selectedSchedule ? getScheduleDisplay(selectedSchedule) : '';
  };

  const getSelectedStudentName = () => {
    const selectedStudent = filteredStudents.find(s => s._id === formData.student_id);
    return selectedStudent ? `${selectedStudent.first_name} ${selectedStudent.last_name} (${selectedStudent.student_id})` : '';
  };

  const filteredSchedules = schedules.filter(schedule =>
    getScheduleDisplay(schedule).toLowerCase().includes(scheduleSearch.toLowerCase())
  );

  const filteredStudentsForSearch = filteredStudents.filter(student =>
    `${student.first_name} ${student.last_name} ${student.student_id}`.toLowerCase().includes(studentSearch.toLowerCase())
  );

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full mx-4 ${
              isMultiMode ? 'max-w-4xl' : 'max-w-md'
            }`}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                  isMultiMode ? 'bg-green-500' : 'bg-blue-500'
                }`}>
                  {isMultiMode ? <Users className="h-5 w-5 text-white" /> : <FileCheck2 className="h-5 w-5 text-white" />}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {isEditing ? "Edit Attendance" : (isMultiMode ? "Mark Multiple Attendance" : "Mark Attendance")}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isEditing ? "Update attendance record" : (isMultiMode ? "Mark attendance for multiple students" : "Create new attendance record")}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {!isEditing && (
                  <button
                    onClick={() => setIsMultiMode(!isMultiMode)}
                    className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    {isMultiMode ? 'Single Mode' : 'Multi Mode'}
                  </button>
                )}
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className={`p-6 space-y-4 ${isMultiMode ? 'max-h-96 overflow-y-auto' : ''}`}>
              {/* Class Schedule Search */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Class Schedule <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div
                    className={`w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 ${
                      errors.schedule_id
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowScheduleDropdown(!showScheduleDropdown);
                      setShowStudentDropdown(false);
                    }}
                  >
                    <span className="text-gray-900 dark:text-white">
                      {getSelectedScheduleName() || "Select class schedule"}
                    </span>
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  </div>

                  {showScheduleDropdown && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
                      <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            placeholder="Search schedules..."
                            value={scheduleSearch}
                            onChange={(e) => setScheduleSearch(e.target.value)}
                            className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white text-sm"
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                      <div className="max-h-48 overflow-y-auto">
                        {filteredSchedules.length > 0 ? (
                          filteredSchedules.map((schedule) => (
                            <div
                              key={schedule._id}
                              className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleInputChange("schedule_id", schedule._id);
                                setShowScheduleDropdown(false);
                                setScheduleSearch('');
                              }}
                            >
                              {getScheduleDisplay(schedule)}
                            </div>
                          ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                            No schedules found
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                {errors.schedule_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.schedule_id}</p>
                )}
              </div>

              {/* Student Selection */}
              {!isMultiMode ? (
                // Single student selection with search
                <div className="relative">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Student <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div
                      className={`w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 ${
                        errors.student_id
                          ? "border-red-500 dark:border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      } ${!formData.schedule_id ? 'opacity-50 cursor-not-allowed' : ''}`}
                      onClick={(e) => {
                        if (!formData.schedule_id) return;
                        e.stopPropagation();
                        setShowStudentDropdown(!showStudentDropdown);
                        setShowScheduleDropdown(false);
                      }}
                    >
                      <span className="text-gray-900 dark:text-white">
                        {getSelectedStudentName() || (formData.schedule_id ? "Select student" : "Select class schedule first")}
                      </span>
                      <ChevronDown className="h-4 w-4 text-gray-400" />
                    </div>

                    {showStudentDropdown && formData.schedule_id && (
                      <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
                        <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                          <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                            <input
                              type="text"
                              placeholder="Search students..."
                              value={studentSearch}
                              onChange={(e) => setStudentSearch(e.target.value)}
                              className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white text-sm"
                              onClick={(e) => e.stopPropagation()}
                            />
                          </div>
                        </div>
                        <div className="max-h-48 overflow-y-auto">
                          {filteredStudentsForSearch.length > 0 ? (
                            filteredStudentsForSearch.map((student) => (
                              <div
                                key={student._id}
                                className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleInputChange("student_id", student._id);
                                  setShowStudentDropdown(false);
                                  setStudentSearch('');
                                }}
                              >
                                <div className="font-medium">{student.first_name} {student.last_name}</div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">{student.student_id}</div>
                              </div>
                            ))
                          ) : (
                            <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                              No students found
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                  {errors.student_id && (
                    <p className="mt-1 text-sm text-red-500">{errors.student_id}</p>
                  )}
                </div>
              ) : (
                // Multi-student selection
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Students ({selectedStudents.size} selected)
                    </label>
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={selectAllStudents}
                        className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded"
                        disabled={!formData.schedule_id}
                      >
                        Select All
                      </button>
                      <button
                        type="button"
                        onClick={deselectAllStudents}
                        className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded"
                      >
                        Clear
                      </button>
                    </div>
                  </div>

                  {/* Bulk Status Controls */}
                  {selectedStudents.size > 0 && (
                    <div className="mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                      <div className="flex items-center space-x-3">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Apply to selected:
                        </label>
                        <div className="relative">
                          <select
                            value={bulkStatus}
                            onChange={(e) => setBulkStatus(e.target.value as any)}
                            className="px-2 py-1 pr-8 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white appearance-none"
                          >
                            <option value="Present">Present</option>
                            <option value="Absent">Absent</option>
                            <option value="Late">Late</option>
                            <option value="Excused">Excused</option>
                          </select>
                          <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400 pointer-events-none" />
                        </div>
                        <button
                          type="button"
                          onClick={applyBulkStatus}
                          className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
                        >
                          Apply
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Student List */}
                  <div className="max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md">
                    {filteredStudents.length > 0 ? (
                      filteredStudents.map((student) => (
                        <div
                          key={student._id}
                          className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-600 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-700"
                        >
                          <div className="flex items-center space-x-3">
                            <button
                              type="button"
                              onClick={() => toggleStudentSelection(student._id)}
                              className="text-blue-500 hover:text-blue-600"
                            >
                              {selectedStudents.has(student._id) ? (
                                <CheckSquare className="h-4 w-4" />
                              ) : (
                                <Square className="h-4 w-4" />
                              )}
                            </button>
                            <div>
                              <p className="text-sm font-medium text-gray-900 dark:text-white">
                                {student.first_name} {student.last_name}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {student.student_id}
                              </p>
                            </div>
                          </div>

                          {selectedStudents.has(student._id) && (
                            <div className="relative">
                              <select
                                value={studentStatuses[student._id] || "Present"}
                                onChange={(e) => updateStudentStatus(student._id, e.target.value)}
                                className="px-2 py-1 pr-6 text-xs border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white appearance-none"
                              >
                                <option value="Present">Present</option>
                                <option value="Absent">Absent</option>
                                <option value="Late">Late</option>
                                <option value="Excused">Excused</option>
                              </select>
                              <ChevronDown className="absolute right-1 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400 pointer-events-none" />
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                        {formData.schedule_id ? "No students found for this class" : "Please select a class schedule first"}
                      </div>
                    )}
                  </div>

                  {errors.students && (
                    <p className="mt-1 text-sm text-red-500">{errors.students}</p>
                  )}
                </div>
              )}

              {/* Status - Only show in single mode */}
              {!isMultiMode && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Status
                  </label>
                  <div className="relative">
                    <select
                      value={formData.status}
                      onChange={(e) => handleInputChange("status", e.target.value)}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white appearance-none"
                    >
                      <option value="Present">Present</option>
                      <option value="Absent">Absent</option>
                      <option value="Late">Late</option>
                      <option value="Excused">Excused</option>
                    </select>
                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                  </div>
                </div>
              )}

              {/* Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Date
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange("date", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.date 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                />
                {errors.date && (
                  <p className="mt-1 text-sm text-red-500">{errors.date}</p>
                )}
              </div>

              {/* Academic Year */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Academic Year
                </label>
                <input
                  type="text"
                  value={formData.academic_year}
                  onChange={(e) => handleInputChange("academic_year", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.academic_year 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                  placeholder="e.g., 2024-2025"
                />
                {errors.academic_year && (
                  <p className="mt-1 text-sm text-red-500">{errors.academic_year}</p>
                )}
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>
                    {isSubmitting
                      ? "Saving..."
                      : (isEditing
                          ? "Update"
                          : (isMultiMode
                              ? `Mark ${selectedStudents.size} Students`
                              : "Create"
                            )
                        )
                    }
                  </span>
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
