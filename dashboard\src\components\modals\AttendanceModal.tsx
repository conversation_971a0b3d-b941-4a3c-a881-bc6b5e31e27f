"use client";

import React, { useState, useEffect } from "react";
import { X, FileCheck2, Save, Users, CheckSquare, Square } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface AttendanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  attendance?: any | null;
  students: any[];
  classes: any[];
  subjects: any[];
  schedules: any[];
  loading?: boolean;
}

export default function AttendanceModal({
  isOpen,
  onClose,
  onSubmit,
  attendance,
  students,
  classes,
  subjects,
  schedules,
  loading = false
}: AttendanceModalProps) {
  const [formData, setFormData] = useState({
    student_id: "",
    schedule_id: "",
    status: "Present" as "Present" | "Absent" | "Late" | "Excused",
    date: new Date().toISOString().split('T')[0],
    academic_year: "2024-2025"
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [filteredStudents, setFilteredStudents] = useState<any[]>([]);

  // Multi-selection states
  const [isMultiMode, setIsMultiMode] = useState(false);
  const [selectedStudents, setSelectedStudents] = useState<Set<string>>(new Set());
  const [studentStatuses, setStudentStatuses] = useState<Record<string, string>>({});
  const [bulkStatus, setBulkStatus] = useState<"Present" | "Absent" | "Late" | "Excused">("Present");

  const isEditing = !!attendance;

  useEffect(() => {
    if (isOpen) {
      if (attendance) {
        // Single edit mode
        setIsMultiMode(false);
        setFormData({
          student_id: attendance.student_id || "",
          schedule_id: attendance.schedule_id || "",
          status: attendance.status || "Present",
          date: attendance.date ? new Date(attendance.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
          academic_year: attendance.academic_year || "2024-2025"
        });
      } else {
        // New attendance - default to multi mode
        setIsMultiMode(true);
        setFormData({
          student_id: "",
          schedule_id: "",
          status: "Present",
          date: new Date().toISOString().split('T')[0],
          academic_year: "2024-2025"
        });
      }
      setErrors({});
      setSelectedStudents(new Set());
      setStudentStatuses({});
      setBulkStatus("Present");
    }
  }, [isOpen, attendance]);

  // Filter students based on selected schedule
  useEffect(() => {
    if (formData.schedule_id) {
      const selectedSchedule = schedules.find(s => s._id === formData.schedule_id);
      if (selectedSchedule) {
        const classStudents = students.filter(student =>
          student.class_id === selectedSchedule.class_id
        );
        setFilteredStudents(classStudents);

        // Initialize student statuses for multi-mode
        if (isMultiMode) {
          const initialStatuses: Record<string, string> = {};
          classStudents.forEach(student => {
            initialStatuses[student._id] = "Present";
          });
          setStudentStatuses(initialStatuses);
        }
      }
    } else {
      setFilteredStudents(students);
      if (isMultiMode) {
        const initialStatuses: Record<string, string> = {};
        students.forEach(student => {
          initialStatuses[student._id] = "Present";
        });
        setStudentStatuses(initialStatuses);
      }
    }
  }, [formData.schedule_id, schedules, students, isMultiMode]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!isMultiMode && !formData.student_id) {
      newErrors.student_id = "Student is required";
    }
    if (isMultiMode && selectedStudents.size === 0) {
      newErrors.students = "At least one student must be selected";
    }
    if (!formData.schedule_id) {
      newErrors.schedule_id = "Class schedule is required";
    }
    if (!formData.date) {
      newErrors.date = "Date is required";
    }
    if (!formData.academic_year) {
      newErrors.academic_year = "Academic year is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      if (isMultiMode) {
        // Submit multiple attendance records
        const attendancePromises = Array.from(selectedStudents).map(studentId => {
          const studentStatus = studentStatuses[studentId] || "Present";
          return onSubmit({
            ...formData,
            student_id: studentId,
            status: studentStatus
          });
        });

        await Promise.all(attendancePromises);
      } else {
        // Submit single attendance record
        await onSubmit(formData);
      }
      onClose();
    } catch (error) {
      console.error("Error submitting attendance:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  // Multi-selection functions
  const toggleStudentSelection = (studentId: string) => {
    const newSelected = new Set(selectedStudents);
    if (newSelected.has(studentId)) {
      newSelected.delete(studentId);
    } else {
      newSelected.add(studentId);
    }
    setSelectedStudents(newSelected);
  };

  const selectAllStudents = () => {
    const allStudentIds = new Set(filteredStudents.map(s => s._id));
    setSelectedStudents(allStudentIds);
  };

  const deselectAllStudents = () => {
    setSelectedStudents(new Set());
  };

  const applyBulkStatus = () => {
    const newStatuses = { ...studentStatuses };
    selectedStudents.forEach(studentId => {
      newStatuses[studentId] = bulkStatus;
    });
    setStudentStatuses(newStatuses);
  };

  const updateStudentStatus = (studentId: string, status: string) => {
    setStudentStatuses(prev => ({
      ...prev,
      [studentId]: status
    }));
  };

  const getScheduleDisplay = (schedule: any) => {
    const subject = subjects.find(s => s._id === schedule.subject_id);
    const classInfo = classes.find(c => c._id === schedule.class_id);
    return `${subject?.name || 'Unknown Subject'} - ${classInfo?.name || 'Unknown Class'} (${schedule.day_of_week})`;
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full mx-4 ${
              isMultiMode ? 'max-w-4xl' : 'max-w-md'
            }`}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                  isMultiMode ? 'bg-green-500' : 'bg-blue-500'
                }`}>
                  {isMultiMode ? <Users className="h-5 w-5 text-white" /> : <FileCheck2 className="h-5 w-5 text-white" />}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {isEditing ? "Edit Attendance" : (isMultiMode ? "Mark Multiple Attendance" : "Mark Attendance")}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isEditing ? "Update attendance record" : (isMultiMode ? "Mark attendance for multiple students" : "Create new attendance record")}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {!isEditing && (
                  <button
                    onClick={() => setIsMultiMode(!isMultiMode)}
                    className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    {isMultiMode ? 'Single Mode' : 'Multi Mode'}
                  </button>
                )}
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className={`p-6 space-y-4 ${isMultiMode ? 'max-h-96 overflow-y-auto' : ''}`}>
              {/* Class Schedule */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Class Schedule
                </label>
                <select
                  value={formData.schedule_id}
                  onChange={(e) => handleInputChange("schedule_id", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.schedule_id 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                >
                  <option value="">Select class schedule</option>
                  {schedules.map((schedule) => (
                    <option key={schedule._id} value={schedule._id}>
                      {getScheduleDisplay(schedule)}
                    </option>
                  ))}
                </select>
                {errors.schedule_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.schedule_id}</p>
                )}
              </div>

              {/* Student Selection */}
              {!isMultiMode ? (
                // Single student selection
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Student
                  </label>
                  <select
                    value={formData.student_id}
                    onChange={(e) => handleInputChange("student_id", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                      errors.student_id
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    disabled={!formData.schedule_id}
                  >
                    <option value="">Select student</option>
                    {filteredStudents.map((student) => (
                      <option key={student._id} value={student._id}>
                        {student.first_name} {student.last_name} ({student.student_id})
                      </option>
                    ))}
                  </select>
                  {errors.student_id && (
                    <p className="mt-1 text-sm text-red-500">{errors.student_id}</p>
                  )}
                </div>
              ) : (
                // Multi-student selection
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Students ({selectedStudents.size} selected)
                    </label>
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={selectAllStudents}
                        className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded"
                        disabled={!formData.schedule_id}
                      >
                        Select All
                      </button>
                      <button
                        type="button"
                        onClick={deselectAllStudents}
                        className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded"
                      >
                        Clear
                      </button>
                    </div>
                  </div>

                  {/* Bulk Status Controls */}
                  {selectedStudents.size > 0 && (
                    <div className="mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                      <div className="flex items-center space-x-3">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Apply to selected:
                        </label>
                        <select
                          value={bulkStatus}
                          onChange={(e) => setBulkStatus(e.target.value as any)}
                          className="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white"
                        >
                          <option value="Present">Present</option>
                          <option value="Absent">Absent</option>
                          <option value="Late">Late</option>
                          <option value="Excused">Excused</option>
                        </select>
                        <button
                          type="button"
                          onClick={applyBulkStatus}
                          className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
                        >
                          Apply
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Student List */}
                  <div className="max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md">
                    {filteredStudents.length > 0 ? (
                      filteredStudents.map((student) => (
                        <div
                          key={student._id}
                          className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-600 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-700"
                        >
                          <div className="flex items-center space-x-3">
                            <button
                              type="button"
                              onClick={() => toggleStudentSelection(student._id)}
                              className="text-blue-500 hover:text-blue-600"
                            >
                              {selectedStudents.has(student._id) ? (
                                <CheckSquare className="h-4 w-4" />
                              ) : (
                                <Square className="h-4 w-4" />
                              )}
                            </button>
                            <div>
                              <p className="text-sm font-medium text-gray-900 dark:text-white">
                                {student.first_name} {student.last_name}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {student.student_id}
                              </p>
                            </div>
                          </div>

                          {selectedStudents.has(student._id) && (
                            <select
                              value={studentStatuses[student._id] || "Present"}
                              onChange={(e) => updateStudentStatus(student._id, e.target.value)}
                              className="px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white"
                            >
                              <option value="Present">Present</option>
                              <option value="Absent">Absent</option>
                              <option value="Late">Late</option>
                              <option value="Excused">Excused</option>
                            </select>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                        {formData.schedule_id ? "No students found for this class" : "Please select a class schedule first"}
                      </div>
                    )}
                  </div>

                  {errors.students && (
                    <p className="mt-1 text-sm text-red-500">{errors.students}</p>
                  )}
                </div>
              )}

              {/* Status - Only show in single mode */}
              {!isMultiMode && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Status
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => handleInputChange("status", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="Present">Present</option>
                    <option value="Absent">Absent</option>
                    <option value="Late">Late</option>
                    <option value="Excused">Excused</option>
                  </select>
                </div>
              )}

              {/* Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Date
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange("date", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.date 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                />
                {errors.date && (
                  <p className="mt-1 text-sm text-red-500">{errors.date}</p>
                )}
              </div>

              {/* Academic Year */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Academic Year
                </label>
                <input
                  type="text"
                  value={formData.academic_year}
                  onChange={(e) => handleInputChange("academic_year", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                    errors.academic_year 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                  placeholder="e.g., 2024-2025"
                />
                {errors.academic_year && (
                  <p className="mt-1 text-sm text-red-500">{errors.academic_year}</p>
                )}
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>
                    {isSubmitting
                      ? "Saving..."
                      : (isEditing
                          ? "Update"
                          : (isMultiMode
                              ? `Mark ${selectedStudents.size} Students`
                              : "Create"
                            )
                        )
                    }
                  </span>
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
