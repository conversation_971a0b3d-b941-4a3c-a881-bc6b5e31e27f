"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboards)/school-admin/grades/page",{

/***/ "(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/(dashboards)/school-admin/grades/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GradesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Filter,Percent,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/Layouts/SchoolLayout */ \"(app-pages-browser)/./src/components/Dashboard/Layouts/SchoolLayout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/utils/TableFix */ \"(app-pages-browser)/./src/components/utils/TableFix.tsx\");\n/* harmony import */ var _components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/widgets/CircularLoader */ \"(app-pages-browser)/./src/components/widgets/CircularLoader.tsx\");\n/* harmony import */ var _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useAuth */ \"(app-pages-browser)/./src/app/hooks/useAuth.tsx\");\n/* harmony import */ var _components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/utils/ProtectedRoute */ \"(app-pages-browser)/./src/components/utils/ProtectedRoute.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/services/GradeServices */ \"(app-pages-browser)/./src/app/services/GradeServices.tsx\");\n/* harmony import */ var _app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/services/StudentServices */ \"(app-pages-browser)/./src/app/services/StudentServices.tsx\");\n/* harmony import */ var _app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/services/SubjectServices */ \"(app-pages-browser)/./src/app/services/SubjectServices.tsx\");\n/* harmony import */ var _app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/services/ExamTypeServices */ \"(app-pages-browser)/./src/app/services/ExamTypeServices.tsx\");\n/* harmony import */ var _app_services_ClassServices__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/services/ClassServices */ \"(app-pages-browser)/./src/app/services/ClassServices.tsx\");\n/* harmony import */ var _app_services_UserServices__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/services/UserServices */ \"(app-pages-browser)/./src/app/services/UserServices.tsx\");\n/* harmony import */ var _components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/GradeModal */ \"(app-pages-browser)/./src/components/modals/GradeModal.tsx\");\n/* harmony import */ var _components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/PasswordConfirmDeleteModal */ \"(app-pages-browser)/./src/components/modals/PasswordConfirmDeleteModal.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navigation = {\n    icon: _barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    baseHref: \"/school-admin/grades\",\n    title: \"Grades\"\n};\nfunction GradesPage() {\n    var _user_school_ids;\n    _s();\n    const { logout, user } = (0,_app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { toasts, removeToast, showSuccess, showError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    // State management\n    const [gradeRecords, setGradeRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalGrades: 0,\n        averageScore: 0,\n        highestScore: 0,\n        lowestScore: 0,\n        passRate: 0\n    });\n    const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedTerm, setSelectedTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    const [selectedExamType, setSelectedExamType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Modal states\n    const [isGradeModalOpen, setIsGradeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gradeToEdit, setGradeToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gradeToDelete, setGradeToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [deleteType, setDeleteType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const [selectedGrades, setSelectedGrades] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Additional data for forms\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [examTypes, setExamTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // Loading states\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [clearSelection, setClearSelection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get school ID from user\n    const schoolId = (user === null || user === void 0 ? void 0 : (_user_school_ids = user.school_ids) === null || _user_school_ids === void 0 ? void 0 : _user_school_ids[0]) || (user === null || user === void 0 ? void 0 : user.school_id);\n    // Fetch grade data from API\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchGradeData = {\n                \"GradesPage.useEffect.fetchGradeData\": async ()=>{\n                    if (!schoolId) {\n                        showError(\"Error\", \"No school ID found\");\n                        setLoadingData(false);\n                        return;\n                    }\n                    try {\n                        setLoadingData(true);\n                        // Build filters\n                        const filters = {};\n                        if (selectedClass !== 'all') filters.class_id = selectedClass;\n                        if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n                        if (selectedTerm !== 'all') filters.term = selectedTerm;\n                        if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n                        // Fetch records and stats in parallel\n                        const [recordsResponse, statsResponse] = await Promise.all([\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                            (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n                        ]);\n                        setGradeRecords(recordsResponse.grade_records);\n                        setStats(statsResponse.stats);\n                    } catch (error) {\n                        console.error(\"Error fetching grade data:\", error);\n                        showError(\"Error\", \"Failed to load grade data\");\n                    } finally{\n                        setLoadingData(false);\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchGradeData\"];\n            fetchGradeData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId,\n        selectedClass,\n        selectedSubject,\n        selectedTerm,\n        selectedExamType\n    ]);\n    // Fetch additional data for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GradesPage.useEffect\": ()=>{\n            const fetchAdditionalData = {\n                \"GradesPage.useEffect.fetchAdditionalData\": async ()=>{\n                    if (!schoolId) return;\n                    try {\n                        // Fetch data with individual error handling\n                        const results = await Promise.allSettled([\n                            (0,_app_services_StudentServices__WEBPACK_IMPORTED_MODULE_8__.getStudentsBySchool)(schoolId),\n                            (0,_app_services_SubjectServices__WEBPACK_IMPORTED_MODULE_9__.getSubjectsBySchoolId)(schoolId),\n                            (0,_app_services_ExamTypeServices__WEBPACK_IMPORTED_MODULE_10__.getExamTypes)(),\n                            (0,_app_services_ClassServices__WEBPACK_IMPORTED_MODULE_11__.getClassesBySchool)(schoolId)\n                        ]);\n                        // Handle each result individually\n                        if (results[0].status === 'fulfilled') {\n                            setStudents(results[0].value);\n                        } else {\n                            console.error(\"Failed to fetch students:\", results[0].reason);\n                            setStudents([]);\n                        }\n                        if (results[1].status === 'fulfilled') {\n                            setSubjects(results[1].value);\n                        } else {\n                            console.error(\"Failed to fetch subjects:\", results[1].reason);\n                            setSubjects([]);\n                        }\n                        if (results[2].status === 'fulfilled') {\n                            setExamTypes(results[2].value);\n                        } else {\n                            console.error(\"Failed to fetch exam types:\", results[2].reason);\n                            setExamTypes([]);\n                        }\n                        if (results[3].status === 'fulfilled') {\n                            setClasses(results[3].value);\n                        } else {\n                            console.error(\"Failed to fetch classes:\", results[3].reason);\n                            setClasses([]);\n                        }\n                        // Show warning if any critical data failed to load\n                        const anyDataFailed = results.some({\n                            \"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\": (result)=>result.status === 'rejected'\n                        }[\"GradesPage.useEffect.fetchAdditionalData.anyDataFailed\"]);\n                        if (anyDataFailed) {\n                            showError(\"Warning\", \"Some form data could not be loaded. Some features may be limited.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching additional data:\", error);\n                        showError(\"Error\", \"Failed to load form data\");\n                    }\n                }\n            }[\"GradesPage.useEffect.fetchAdditionalData\"];\n            fetchAdditionalData();\n        }\n    }[\"GradesPage.useEffect\"], [\n        schoolId\n    ]); // Removed showError from dependencies\n    // CRUD Functions\n    const handleCreateGrade = ()=>{\n        setGradeToEdit(null);\n        setIsGradeModalOpen(true);\n    };\n    const handleEditGrade = (grade)=>{\n        setGradeToEdit(grade);\n        setIsGradeModalOpen(true);\n    };\n    const handleDeleteGrade = (grade)=>{\n        setGradeToDelete(grade);\n        setDeleteType(\"single\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleDeleteMultiple = ()=>{\n        setDeleteType(\"multiple\");\n        setIsDeleteModalOpen(true);\n    };\n    const handleSelectionChange = (selectedRows)=>{\n        setSelectedGrades(selectedRows);\n    };\n    // Modal submission functions\n    const handleGradeSubmit = async (data)=>{\n        if (!schoolId) {\n            showError(\"Error\", \"No school ID found\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (gradeToEdit) {\n                // Update existing grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.updateGrade)(gradeToEdit._id, data);\n            } else {\n                // Create new grade\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.createGrade)({\n                    ...data,\n                    school_id: schoolId\n                });\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsGradeModalOpen(false);\n            setGradeToEdit(null);\n            // Show success notification\n            showSuccess(gradeToEdit ? \"Grade Updated\" : \"Grade Added\", gradeToEdit ? \"Grade has been updated successfully.\" : \"New grade has been added successfully.\");\n        } catch (error) {\n            console.error(\"Error submitting grade:\", error);\n            showError(\"Error\", \"Failed to save grade. Please try again.\");\n            throw error;\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteConfirm = async (password)=>{\n        if (!schoolId || !user) return;\n        try {\n            // Verify password\n            const passwordVerified = await (0,_app_services_UserServices__WEBPACK_IMPORTED_MODULE_12__.verifyPassword)(password, user.email);\n            if (!passwordVerified) {\n                showError(\"Error\", \"Invalid password. Please try again.\");\n                throw new Error(\"Invalid password\");\n            }\n            if (deleteType === \"single\" && gradeToDelete) {\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteGrade)(gradeToDelete._id);\n            } else if (deleteType === \"multiple\") {\n                const selectedIds = selectedGrades.map((g)=>g._id);\n                await (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.deleteMultipleGrades)(selectedIds);\n                setClearSelection(true);\n            }\n            // Refresh grades list\n            const filters = {};\n            if (selectedClass !== 'all') filters.class_id = selectedClass;\n            if (selectedSubject !== 'all') filters.subject_id = selectedSubject;\n            if (selectedTerm !== 'all') filters.term = selectedTerm;\n            if (selectedExamType !== 'all') filters.exam_type_id = selectedExamType;\n            const [recordsResponse, statsResponse] = await Promise.all([\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeRecords)(schoolId, filters),\n                (0,_app_services_GradeServices__WEBPACK_IMPORTED_MODULE_7__.getGradeStats)(schoolId, filters)\n            ]);\n            setGradeRecords(recordsResponse.grade_records);\n            setStats(statsResponse.stats);\n            setIsDeleteModalOpen(false);\n            setGradeToDelete(null);\n            setSelectedGrades([]);\n            // Show success notification\n            if (deleteType === \"single\") {\n                showSuccess(\"Grade Deleted\", \"Grade record has been deleted successfully.\");\n            } else {\n                showSuccess(\"Grades Deleted\", \"\".concat(selectedGrades.length, \" grade records have been deleted successfully.\"));\n            }\n        } catch (error) {\n            console.error(\"Error deleting grade(s):\", error);\n            if (error.message !== \"Invalid password\") {\n                showError(\"Error\", \"Failed to delete grade record(s). Please try again.\");\n            }\n            throw error;\n        }\n    };\n    const getGradeColor = (grade)=>{\n        switch(grade){\n            case 'A+':\n            case 'A':\n                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n            case 'B+':\n            case 'B':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n            case 'C+':\n            case 'C':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n            case 'D':\n            case 'F':\n                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n        }\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 90) return 'text-green-600 font-semibold';\n        if (score >= 80) return 'text-blue-600 font-semibold';\n        if (score >= 70) return 'text-yellow-600 font-semibold';\n        if (score >= 60) return 'text-orange-600 font-semibold';\n        return 'text-red-600 font-semibold';\n    };\n    // Table columns\n    const columns = [\n        {\n            header: \"Student\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-foreground\",\n                            children: row.student_name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-foreground/60\",\n                            children: row.student_id\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Class\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: row.class_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Subject\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.subject_name\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Exam Type\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.exam_type\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Score\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-bold \".concat(getScoreColor(row.score)),\n                    children: [\n                        row.score,\n                        \"%\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Grade\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 rounded-full text-sm font-bold \".concat(getGradeColor(row.grade)),\n                    children: row.grade\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            header: \"Term\",\n            accessor: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: row.term\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Actions for the table\n    const actions = [\n        {\n            label: \"Edit\",\n            onClick: (grade)=>{\n                handleEditGrade(grade);\n            }\n        },\n        {\n            label: \"Delete\",\n            onClick: (grade)=>{\n                handleDeleteGrade(grade);\n            }\n        }\n    ];\n    // Filter data based on selections\n    const filteredRecords = gradeRecords.filter((record)=>{\n        if (selectedClass !== 'all' && record.class_name !== selectedClass) return false;\n        if (selectedSubject !== 'all' && record.subject_name !== selectedSubject) return false;\n        if (selectedTerm !== 'all' && record.term !== selectedTerm) return false;\n        if (selectedExamType !== 'all' && record.exam_type !== selectedExamType) return false;\n        return true;\n    });\n    if (loadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 32,\n                color: \"teal\"\n            }, void 0, false, {\n                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                lineNumber: 414,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 413,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"school_admin\",\n            \"admin\",\n            \"super\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Layouts_SchoolLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            navigation: navigation,\n            onLogout: logout,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-teal rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: \"Grades Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-foreground/60\",\n                                                        children: \"Monitor and manage student grades across all subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    stats.averageScore,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground/60\",\n                                                children: \"Average Score\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-5 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Total Grades\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: stats.totalGrades\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Average Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            stats.averageScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-teal rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Highest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: [\n                                                            stats.highestScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Lowest Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: [\n                                                            stats.lowestScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-widget rounded-lg border border-stroke p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-foreground/60\",\n                                                        children: \"Pass Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-foreground\",\n                                                        children: [\n                                                            stats.passRate,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 text-foreground/60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Class:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedClass,\n                                                onChange: (e)=>setSelectedClass(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    classes.map((cls)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: cls._id,\n                                                            children: cls.name\n                                                        }, cls._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Subject:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedSubject,\n                                                onChange: (e)=>setSelectedSubject(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Subjects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: subject._id,\n                                                            children: subject.name\n                                                        }, subject._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Term:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedTerm,\n                                                onChange: (e)=>setSelectedTerm(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Terms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"First Term\",\n                                                        children: \"First Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Second Term\",\n                                                        children: \"Second Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Third Term\",\n                                                        children: \"Third Term\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm text-foreground/70\",\n                                                children: \"Exam Type:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedExamType,\n                                                onChange: (e)=>setSelectedExamType(e.target.value),\n                                                className: \"px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    examTypes.map((examType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: examType._id,\n                                                            children: examType.type\n                                                        }, examType._id, false, {\n                                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-widget rounded-lg border border-stroke p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: [\n                                                \"Grade Records (\",\n                                                filteredRecords.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: handleCreateGrade,\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Filter_Percent_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Grade\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_CircularLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        color: \"teal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_TableFix__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: filteredRecords,\n                                        columns: columns,\n                                        actions: actions,\n                                        defaultItemsPerPage: 15,\n                                        onSelectionChange: handleSelectionChange,\n                                        handleDeleteMultiple: handleDeleteMultiple,\n                                        clearSelection: clearSelection,\n                                        onSelectionCleared: ()=>setClearSelection(false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                            lineNumber: 590,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 422,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_GradeModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    isOpen: isGradeModalOpen,\n                    onClose: ()=>{\n                        setIsGradeModalOpen(false);\n                        setGradeToEdit(null);\n                    },\n                    onSubmit: handleGradeSubmit,\n                    grade: gradeToEdit,\n                    students: students,\n                    subjects: subjects,\n                    examTypes: examTypes,\n                    loading: isSubmitting\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 623,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_PasswordConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    isOpen: isDeleteModalOpen,\n                    onClose: ()=>{\n                        setIsDeleteModalOpen(false);\n                        setGradeToDelete(null);\n                    },\n                    onConfirm: handleDeleteConfirm,\n                    title: deleteType === \"single\" ? \"Delete Grade Record\" : \"Delete Selected Grade Records\",\n                    message: deleteType === \"single\" ? \"Are you sure you want to delete this grade record? This action cannot be undone.\" : \"Are you sure you want to delete \".concat(selectedGrades.length, \" selected grade records? This action cannot be undone.\"),\n                    itemName: deleteType === \"single\" && gradeToDelete ? \"\".concat(gradeToDelete.student_name, \" - \").concat(gradeToDelete.subject_name, \" (\").concat(gradeToDelete.exam_type, \")\") : undefined,\n                    itemCount: deleteType === \"multiple\" ? selectedGrades.length : undefined,\n                    type: deleteType\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 638,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.ToastContainer, {\n                    toasts: toasts,\n                    onClose: removeToast\n                }, void 0, false, {\n                    fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n                    lineNumber: 665,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n            lineNumber: 421,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\elysee\\\\Projet\\\\Pers\\\\scholarify\\\\dashboard\\\\src\\\\app\\\\(dashboards)\\\\school-admin\\\\grades\\\\page.tsx\",\n        lineNumber: 420,\n        columnNumber: 5\n    }, this);\n}\n_s(GradesPage, \"Unbmq46oVbBsrl3g2oGxiFvVnHU=\", false, function() {\n    return [\n        _app_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = GradesPage;\nvar _c;\n$RefreshReg$(_c, \"GradesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZHMpL3NjaG9vbC1hZG1pbi9ncmFkZXMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0RjtBQUNyQjtBQUNqQjtBQUNDO0FBRVU7QUFDdkI7QUFDcUI7QUFDeEI7QUFVRDtBQUMrQjtBQUNFO0FBQ1I7QUFDRztBQUNMO0FBQ0w7QUFDZ0M7QUFDdkI7QUFJakUsTUFBTStCLGFBQWE7SUFDakJDLE1BQU1oQyxtSUFBT0E7SUFDYmlDLFVBQVU7SUFDVkMsT0FBTztBQUNUO0FBRWUsU0FBU0M7UUFzQ0xDOztJQXJDakIsTUFBTSxFQUFFQyxNQUFNLEVBQUVELElBQUksRUFBRSxHQUFHdkIsOERBQU9BO0lBQ2hDLE1BQU0sRUFBRXlCLE1BQU0sRUFBRUMsV0FBVyxFQUFFQyxXQUFXLEVBQUVDLFNBQVMsRUFBRSxHQUFHWiwrREFBUUE7SUFFaEUsbUJBQW1CO0lBQ25CLE1BQU0sQ0FBQ2EsY0FBY0MsZ0JBQWdCLEdBQUdqQywrQ0FBUUEsQ0FBZ0IsRUFBRTtJQUNsRSxNQUFNLENBQUNrQyxPQUFPQyxTQUFTLEdBQUduQywrQ0FBUUEsQ0FBYTtRQUM3Q29DLGFBQWE7UUFDYkMsY0FBYztRQUNkQyxjQUFjO1FBQ2RDLGFBQWE7UUFDYkMsVUFBVTtJQUNaO0lBQ0EsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUMyQyxlQUFlQyxpQkFBaUIsR0FBRzVDLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQzZDLGlCQUFpQkMsbUJBQW1CLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUMrQyxjQUFjQyxnQkFBZ0IsR0FBR2hELCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2lELGtCQUFrQkMsb0JBQW9CLEdBQUdsRCwrQ0FBUUEsQ0FBQztJQUV6RCxlQUFlO0lBQ2YsTUFBTSxDQUFDbUQsa0JBQWtCQyxvQkFBb0IsR0FBR3BELCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ3FELG1CQUFtQkMscUJBQXFCLEdBQUd0RCwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUN1RCxhQUFhQyxlQUFlLEdBQUd4RCwrQ0FBUUEsQ0FBcUI7SUFDbkUsTUFBTSxDQUFDeUQsZUFBZUMsaUJBQWlCLEdBQUcxRCwrQ0FBUUEsQ0FBcUI7SUFDdkUsTUFBTSxDQUFDMkQsWUFBWUMsY0FBYyxHQUFHNUQsK0NBQVFBLENBQXdCO0lBQ3BFLE1BQU0sQ0FBQzZELGdCQUFnQkMsa0JBQWtCLEdBQUc5RCwrQ0FBUUEsQ0FBZ0IsRUFBRTtJQUV0RSw0QkFBNEI7SUFDNUIsTUFBTSxDQUFDK0QsVUFBVUMsWUFBWSxHQUFHaEUsK0NBQVFBLENBQVEsRUFBRTtJQUNsRCxNQUFNLENBQUNpRSxVQUFVQyxZQUFZLEdBQUdsRSwrQ0FBUUEsQ0FBUSxFQUFFO0lBQ2xELE1BQU0sQ0FBQ21FLFdBQVdDLGFBQWEsR0FBR3BFLCtDQUFRQSxDQUFRLEVBQUU7SUFDcEQsTUFBTSxDQUFDcUUsU0FBU0MsV0FBVyxHQUFHdEUsK0NBQVFBLENBQVEsRUFBRTtJQUVoRCxpQkFBaUI7SUFDakIsTUFBTSxDQUFDdUUsY0FBY0MsZ0JBQWdCLEdBQUd4RSwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUN5RSxnQkFBZ0JDLGtCQUFrQixHQUFHMUUsK0NBQVFBLENBQUM7SUFFckQsMEJBQTBCO0lBQzFCLE1BQU0yRSxXQUFXakQsQ0FBQUEsaUJBQUFBLDRCQUFBQSxtQkFBQUEsS0FBTWtELFVBQVUsY0FBaEJsRCx1Q0FBQUEsZ0JBQWtCLENBQUMsRUFBRSxNQUFJQSxpQkFBQUEsMkJBQUFBLEtBQU1tRCxTQUFTO0lBRXpELDRCQUE0QjtJQUM1QjlFLGdEQUFTQTtnQ0FBQztZQUNSLE1BQU0rRTt1REFBaUI7b0JBQ3JCLElBQUksQ0FBQ0gsVUFBVTt3QkFDYjVDLFVBQVUsU0FBUzt3QkFDbkJXLGVBQWU7d0JBQ2Y7b0JBQ0Y7b0JBRUEsSUFBSTt3QkFDRkEsZUFBZTt3QkFFZixnQkFBZ0I7d0JBQ2hCLE1BQU1xQyxVQUFlLENBQUM7d0JBQ3RCLElBQUlwQyxrQkFBa0IsT0FBT29DLFFBQVFDLFFBQVEsR0FBR3JDO3dCQUNoRCxJQUFJRSxvQkFBb0IsT0FBT2tDLFFBQVFFLFVBQVUsR0FBR3BDO3dCQUNwRCxJQUFJRSxpQkFBaUIsT0FBT2dDLFFBQVFHLElBQUksR0FBR25DO3dCQUMzQyxJQUFJRSxxQkFBcUIsT0FBTzhCLFFBQVFJLFlBQVksR0FBR2xDO3dCQUV2RCxzQ0FBc0M7d0JBQ3RDLE1BQU0sQ0FBQ21DLGlCQUFpQkMsY0FBYyxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQzs0QkFDekRqRiw0RUFBZUEsQ0FBQ3FFLFVBQW9CSTs0QkFDcEN4RSwwRUFBYUEsQ0FBQ29FLFVBQW9CSTt5QkFDbkM7d0JBRUQ5QyxnQkFBZ0JtRCxnQkFBZ0JJLGFBQWE7d0JBQzdDckQsU0FBU2tELGNBQWNuRCxLQUFLO29CQUM5QixFQUFFLE9BQU91RCxPQUFPO3dCQUNkQyxRQUFRRCxLQUFLLENBQUMsOEJBQThCQTt3QkFDNUMxRCxVQUFVLFNBQVM7b0JBQ3JCLFNBQVU7d0JBQ1JXLGVBQWU7b0JBQ2pCO2dCQUNGOztZQUVBb0M7UUFDRjsrQkFBRztRQUFDSDtRQUFVaEM7UUFBZUU7UUFBaUJFO1FBQWNFO0tBQWlCO0lBRTdFLG1DQUFtQztJQUNuQ2xELGdEQUFTQTtnQ0FBQztZQUNSLE1BQU00Rjs0REFBc0I7b0JBQzFCLElBQUksQ0FBQ2hCLFVBQVU7b0JBRWYsSUFBSTt3QkFDRiw0Q0FBNEM7d0JBQzVDLE1BQU1pQixVQUFVLE1BQU1OLFFBQVFPLFVBQVUsQ0FBQzs0QkFDdkNqRixrRkFBbUJBLENBQUMrRDs0QkFDcEI5RCxvRkFBcUJBLENBQUM4RDs0QkFDdEI3RCw2RUFBWUE7NEJBQ1pDLGdGQUFrQkEsQ0FBQzREO3lCQUNwQjt3QkFFRCxrQ0FBa0M7d0JBQ2xDLElBQUlpQixPQUFPLENBQUMsRUFBRSxDQUFDRSxNQUFNLEtBQUssYUFBYTs0QkFDckM5QixZQUFZNEIsT0FBTyxDQUFDLEVBQUUsQ0FBQ0csS0FBSzt3QkFDOUIsT0FBTzs0QkFDTEwsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkcsT0FBTyxDQUFDLEVBQUUsQ0FBQ0ksTUFBTTs0QkFDNURoQyxZQUFZLEVBQUU7d0JBQ2hCO3dCQUVBLElBQUk0QixPQUFPLENBQUMsRUFBRSxDQUFDRSxNQUFNLEtBQUssYUFBYTs0QkFDckM1QixZQUFZMEIsT0FBTyxDQUFDLEVBQUUsQ0FBQ0csS0FBSzt3QkFDOUIsT0FBTzs0QkFDTEwsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkcsT0FBTyxDQUFDLEVBQUUsQ0FBQ0ksTUFBTTs0QkFDNUQ5QixZQUFZLEVBQUU7d0JBQ2hCO3dCQUVBLElBQUkwQixPQUFPLENBQUMsRUFBRSxDQUFDRSxNQUFNLEtBQUssYUFBYTs0QkFDckMxQixhQUFhd0IsT0FBTyxDQUFDLEVBQUUsQ0FBQ0csS0FBSzt3QkFDL0IsT0FBTzs0QkFDTEwsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkcsT0FBTyxDQUFDLEVBQUUsQ0FBQ0ksTUFBTTs0QkFDOUQ1QixhQUFhLEVBQUU7d0JBQ2pCO3dCQUVBLElBQUl3QixPQUFPLENBQUMsRUFBRSxDQUFDRSxNQUFNLEtBQUssYUFBYTs0QkFDckN4QixXQUFXc0IsT0FBTyxDQUFDLEVBQUUsQ0FBQ0csS0FBSzt3QkFDN0IsT0FBTzs0QkFDTEwsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkcsT0FBTyxDQUFDLEVBQUUsQ0FBQ0ksTUFBTTs0QkFDM0QxQixXQUFXLEVBQUU7d0JBQ2Y7d0JBRUEsbURBQW1EO3dCQUNuRCxNQUFNMkIsZ0JBQWdCTCxRQUFRTSxJQUFJO3NGQUFDQyxDQUFBQSxTQUFVQSxPQUFPTCxNQUFNLEtBQUs7O3dCQUMvRCxJQUFJRyxlQUFlOzRCQUNqQmxFLFVBQVUsV0FBVzt3QkFDdkI7b0JBQ0YsRUFBRSxPQUFPMEQsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLG1DQUFtQ0E7d0JBQ2pEMUQsVUFBVSxTQUFTO29CQUNyQjtnQkFDRjs7WUFFQTREO1FBQ0Y7K0JBQUc7UUFBQ2hCO0tBQVMsR0FBRyxzQ0FBc0M7SUFFdEQsaUJBQWlCO0lBQ2pCLE1BQU15QixvQkFBb0I7UUFDeEI1QyxlQUFlO1FBQ2ZKLG9CQUFvQjtJQUN0QjtJQUVBLE1BQU1pRCxrQkFBa0IsQ0FBQ0M7UUFDdkI5QyxlQUFlOEM7UUFDZmxELG9CQUFvQjtJQUN0QjtJQUVBLE1BQU1tRCxvQkFBb0IsQ0FBQ0Q7UUFDekI1QyxpQkFBaUI0QztRQUNqQjFDLGNBQWM7UUFDZE4scUJBQXFCO0lBQ3ZCO0lBRUEsTUFBTWtELHVCQUF1QjtRQUMzQjVDLGNBQWM7UUFDZE4scUJBQXFCO0lBQ3ZCO0lBRUEsTUFBTW1ELHdCQUF3QixDQUFDQztRQUM3QjVDLGtCQUFrQjRDO0lBQ3BCO0lBRUEsNkJBQTZCO0lBQzdCLE1BQU1DLG9CQUFvQixPQUFPQztRQUMvQixJQUFJLENBQUNqQyxVQUFVO1lBQ2I1QyxVQUFVLFNBQVM7WUFDbkI7UUFDRjtRQUVBeUMsZ0JBQWdCO1FBQ2hCLElBQUk7WUFDRixJQUFJakIsYUFBYTtnQkFDZix3QkFBd0I7Z0JBQ3hCLE1BQU05Qyx3RUFBV0EsQ0FBQzhDLFlBQVlzRCxHQUFHLEVBQUVEO1lBQ3JDLE9BQU87Z0JBQ0wsbUJBQW1CO2dCQUNuQixNQUFNcEcsd0VBQVdBLENBQUM7b0JBQUUsR0FBR29HLElBQUk7b0JBQUUvQixXQUFXRjtnQkFBUztZQUNuRDtZQUVBLHNCQUFzQjtZQUN0QixNQUFNSSxVQUFlLENBQUM7WUFDdEIsSUFBSXBDLGtCQUFrQixPQUFPb0MsUUFBUUMsUUFBUSxHQUFHckM7WUFDaEQsSUFBSUUsb0JBQW9CLE9BQU9rQyxRQUFRRSxVQUFVLEdBQUdwQztZQUNwRCxJQUFJRSxpQkFBaUIsT0FBT2dDLFFBQVFHLElBQUksR0FBR25DO1lBQzNDLElBQUlFLHFCQUFxQixPQUFPOEIsUUFBUUksWUFBWSxHQUFHbEM7WUFFdkQsTUFBTSxDQUFDbUMsaUJBQWlCQyxjQUFjLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO2dCQUN6RGpGLDRFQUFlQSxDQUFDcUUsVUFBb0JJO2dCQUNwQ3hFLDBFQUFhQSxDQUFDb0UsVUFBb0JJO2FBQ25DO1lBRUQ5QyxnQkFBZ0JtRCxnQkFBZ0JJLGFBQWE7WUFDN0NyRCxTQUFTa0QsY0FBY25ELEtBQUs7WUFDNUJrQixvQkFBb0I7WUFDcEJJLGVBQWU7WUFFZiw0QkFBNEI7WUFDNUIxQixZQUNFeUIsY0FBYyxrQkFBa0IsZUFDaENBLGNBQWMseUNBQXlDO1FBRTNELEVBQUUsT0FBT2tDLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekMxRCxVQUFVLFNBQVM7WUFDbkIsTUFBTTBEO1FBQ1IsU0FBVTtZQUNSakIsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNc0Msc0JBQXNCLE9BQU9DO1FBQ2pDLElBQUksQ0FBQ3BDLFlBQVksQ0FBQ2pELE1BQU07UUFFeEIsSUFBSTtZQUNGLGtCQUFrQjtZQUNsQixNQUFNc0YsbUJBQW1CLE1BQU1oRywyRUFBY0EsQ0FBQytGLFVBQVVyRixLQUFLdUYsS0FBSztZQUNsRSxJQUFJLENBQUNELGtCQUFrQjtnQkFDckJqRixVQUFVLFNBQVM7Z0JBQ25CLE1BQU0sSUFBSW1GLE1BQU07WUFDbEI7WUFFQSxJQUFJdkQsZUFBZSxZQUFZRixlQUFlO2dCQUM1QyxNQUFNL0Msd0VBQVdBLENBQUMrQyxjQUFjb0QsR0FBRztZQUNyQyxPQUFPLElBQUlsRCxlQUFlLFlBQVk7Z0JBQ3BDLE1BQU13RCxjQUFjdEQsZUFBZXVELEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRVIsR0FBRztnQkFDakQsTUFBTWxHLGlGQUFvQkEsQ0FBQ3dHO2dCQUMzQnpDLGtCQUFrQjtZQUNwQjtZQUVBLHNCQUFzQjtZQUN0QixNQUFNSyxVQUFlLENBQUM7WUFDdEIsSUFBSXBDLGtCQUFrQixPQUFPb0MsUUFBUUMsUUFBUSxHQUFHckM7WUFDaEQsSUFBSUUsb0JBQW9CLE9BQU9rQyxRQUFRRSxVQUFVLEdBQUdwQztZQUNwRCxJQUFJRSxpQkFBaUIsT0FBT2dDLFFBQVFHLElBQUksR0FBR25DO1lBQzNDLElBQUlFLHFCQUFxQixPQUFPOEIsUUFBUUksWUFBWSxHQUFHbEM7WUFFdkQsTUFBTSxDQUFDbUMsaUJBQWlCQyxjQUFjLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO2dCQUN6RGpGLDRFQUFlQSxDQUFDcUUsVUFBb0JJO2dCQUNwQ3hFLDBFQUFhQSxDQUFDb0UsVUFBb0JJO2FBQ25DO1lBRUQ5QyxnQkFBZ0JtRCxnQkFBZ0JJLGFBQWE7WUFDN0NyRCxTQUFTa0QsY0FBY25ELEtBQUs7WUFDNUJvQixxQkFBcUI7WUFDckJJLGlCQUFpQjtZQUNqQkksa0JBQWtCLEVBQUU7WUFFcEIsNEJBQTRCO1lBQzVCLElBQUlILGVBQWUsVUFBVTtnQkFDM0I3QixZQUFZLGlCQUFpQjtZQUMvQixPQUFPO2dCQUNMQSxZQUFZLGtCQUFrQixHQUF5QixPQUF0QitCLGVBQWV5RCxNQUFNLEVBQUM7WUFDekQ7UUFDRixFQUFFLE9BQU83QixPQUFZO1lBQ25CQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQyxJQUFJQSxNQUFNOEIsT0FBTyxLQUFLLG9CQUFvQjtnQkFDeEN4RixVQUFVLFNBQVM7WUFDckI7WUFDQSxNQUFNMEQ7UUFDUjtJQUNGO0lBRUEsTUFBTStCLGdCQUFnQixDQUFDbEI7UUFDckIsT0FBUUE7WUFDTixLQUFLO1lBQ0wsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztZQUNMLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7WUFDTCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO1lBQ0wsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxNQUFNbUIsZ0JBQWdCLENBQUNDO1FBQ3JCLElBQUlBLFNBQVMsSUFBSSxPQUFPO1FBQ3hCLElBQUlBLFNBQVMsSUFBSSxPQUFPO1FBQ3hCLElBQUlBLFNBQVMsSUFBSSxPQUFPO1FBQ3hCLElBQUlBLFNBQVMsSUFBSSxPQUFPO1FBQ3hCLE9BQU87SUFDVDtJQUVBLGdCQUFnQjtJQUNoQixNQUFNQyxVQUFVO1FBQ2Q7WUFDRUMsUUFBUTtZQUNSQyxVQUFVLENBQUNDLG9CQUNULDhEQUFDQzs7c0NBQ0MsOERBQUNDOzRCQUFFQyxXQUFVO3NDQUErQkgsSUFBSUksWUFBWTs7Ozs7O3NDQUM1RCw4REFBQ0Y7NEJBQUVDLFdBQVU7c0NBQThCSCxJQUFJSyxVQUFVOzs7Ozs7Ozs7Ozs7UUFHL0Q7UUFDQTtZQUNFUCxRQUFRO1lBQ1JDLFVBQVUsQ0FBQ0Msb0JBQ1QsOERBQUNNO29CQUFLSCxXQUFVOzhCQUF1QkgsSUFBSU8sVUFBVTs7Ozs7O1FBRXpEO1FBQ0E7WUFDRVQsUUFBUTtZQUNSQyxVQUFVLENBQUNDLG9CQUNULDhEQUFDTTtvQkFBS0gsV0FBVTs4QkFBV0gsSUFBSVEsWUFBWTs7Ozs7O1FBRS9DO1FBQ0E7WUFDRVYsUUFBUTtZQUNSQyxVQUFVLENBQUNDLG9CQUNULDhEQUFDTTtvQkFBS0gsV0FBVTs4QkFBV0gsSUFBSVMsU0FBUzs7Ozs7O1FBRTVDO1FBQ0E7WUFDRVgsUUFBUTtZQUNSQyxVQUFVLENBQUNDLG9CQUNULDhEQUFDTTtvQkFBS0gsV0FBVyxxQkFBOEMsT0FBekJSLGNBQWNLLElBQUlKLEtBQUs7O3dCQUMxREksSUFBSUosS0FBSzt3QkFBQzs7Ozs7OztRQUdqQjtRQUNBO1lBQ0VFLFFBQVE7WUFDUkMsVUFBVSxDQUFDQyxvQkFDVCw4REFBQ007b0JBQUtILFdBQVcsNENBQXFFLE9BQXpCVCxjQUFjTSxJQUFJeEIsS0FBSzs4QkFDakZ3QixJQUFJeEIsS0FBSzs7Ozs7O1FBR2hCO1FBQ0E7WUFDRXNCLFFBQVE7WUFDUkMsVUFBVSxDQUFDQyxvQkFDVCw4REFBQ007b0JBQUtILFdBQVU7OEJBQVdILElBQUk1QyxJQUFJOzs7Ozs7UUFFdkM7S0FPRDtJQUVELHdCQUF3QjtJQUN4QixNQUFNc0QsVUFBVTtRQUNkO1lBQ0VDLE9BQU87WUFDUEMsU0FBUyxDQUFDcEM7Z0JBQ1JELGdCQUFnQkM7WUFDbEI7UUFDRjtRQUNBO1lBQ0VtQyxPQUFPO1lBQ1BDLFNBQVMsQ0FBQ3BDO2dCQUNSQyxrQkFBa0JEO1lBQ3BCO1FBQ0Y7S0FDRDtJQUVELGtDQUFrQztJQUNsQyxNQUFNcUMsa0JBQWtCM0csYUFBYTRHLE1BQU0sQ0FBQ0MsQ0FBQUE7UUFDMUMsSUFBSWxHLGtCQUFrQixTQUFTa0csT0FBT1IsVUFBVSxLQUFLMUYsZUFBZSxPQUFPO1FBQzNFLElBQUlFLG9CQUFvQixTQUFTZ0csT0FBT1AsWUFBWSxLQUFLekYsaUJBQWlCLE9BQU87UUFDakYsSUFBSUUsaUJBQWlCLFNBQVM4RixPQUFPM0QsSUFBSSxLQUFLbkMsY0FBYyxPQUFPO1FBQ25FLElBQUlFLHFCQUFxQixTQUFTNEYsT0FBT04sU0FBUyxLQUFLdEYsa0JBQWtCLE9BQU87UUFDaEYsT0FBTztJQUNUO0lBRUEsSUFBSVIsYUFBYTtRQUNmLHFCQUNFLDhEQUFDc0Y7WUFBSUUsV0FBVTtzQkFDYiw0RUFBQy9ILDBFQUFjQTtnQkFBQzRJLE1BQU07Z0JBQUlDLE9BQU07Ozs7Ozs7Ozs7O0lBR3RDO0lBRUEscUJBQ0UsOERBQUMzSSx3RUFBY0E7UUFBQzRJLGNBQWM7WUFBQztZQUFnQjtZQUFTO1NBQVE7a0JBQzlELDRFQUFDbkosa0ZBQVlBO1lBQUN3QixZQUFZQTtZQUFZNEgsVUFBVXRIOzs4QkFDOUMsOERBQUNvRztvQkFBSUUsV0FBVTs7c0NBRWIsOERBQUNGOzRCQUFJRSxXQUFVO3NDQUNiLDRFQUFDRjtnQ0FBSUUsV0FBVTs7a0RBQ2IsOERBQUNGO3dDQUFJRSxXQUFVOzswREFDYiw4REFBQ0Y7Z0RBQUlFLFdBQVU7MERBQ2IsNEVBQUMzSSxtSUFBT0E7b0RBQUMySSxXQUFVOzs7Ozs7Ozs7OzswREFFckIsOERBQUNGOztrRUFDQyw4REFBQ21CO3dEQUFHakIsV0FBVTtrRUFBcUM7Ozs7OztrRUFDbkQsOERBQUNEO3dEQUFFQyxXQUFVO2tFQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU10Qyw4REFBQ0Y7d0NBQUlFLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBRUMsV0FBVTs7b0RBQXNDL0YsTUFBTUcsWUFBWTtvREFBQzs7Ozs7OzswREFDdEUsOERBQUMyRjtnREFBRUMsV0FBVTswREFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1oRCw4REFBQ0Y7NEJBQUlFLFdBQVU7OzhDQUNiLDhEQUFDRjtvQ0FBSUUsV0FBVTs4Q0FDYiw0RUFBQ0Y7d0NBQUlFLFdBQVU7OzBEQUNiLDhEQUFDRjs7a0VBQ0MsOERBQUNDO3dEQUFFQyxXQUFVO2tFQUE2Qjs7Ozs7O2tFQUMxQyw4REFBQ0Q7d0RBQUVDLFdBQVU7a0VBQXNDL0YsTUFBTUUsV0FBVzs7Ozs7Ozs7Ozs7OzBEQUV0RSw4REFBQzJGO2dEQUFJRSxXQUFVOzBEQUNiLDRFQUFDeEksbUlBQVFBO29EQUFDd0ksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLMUIsOERBQUNGO29DQUFJRSxXQUFVOzhDQUNiLDRFQUFDRjt3Q0FBSUUsV0FBVTs7MERBQ2IsOERBQUNGOztrRUFDQyw4REFBQ0M7d0RBQUVDLFdBQVU7a0VBQTZCOzs7Ozs7a0VBQzFDLDhEQUFDRDt3REFBRUMsV0FBVTs7NERBQXNDL0YsTUFBTUcsWUFBWTs0REFBQzs7Ozs7Ozs7Ozs7OzswREFFeEUsOERBQUMwRjtnREFBSUUsV0FBVTswREFDYiw0RUFBQzFJLG1JQUFVQTtvREFBQzBJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSzVCLDhEQUFDRjtvQ0FBSUUsV0FBVTs4Q0FDYiw0RUFBQ0Y7d0NBQUlFLFdBQVU7OzBEQUNiLDhEQUFDRjs7a0VBQ0MsOERBQUNDO3dEQUFFQyxXQUFVO2tFQUE2Qjs7Ozs7O2tFQUMxQyw4REFBQ0Q7d0RBQUVDLFdBQVU7OzREQUFxQy9GLE1BQU1JLFlBQVk7NERBQUM7Ozs7Ozs7Ozs7Ozs7MERBRXZFLDhEQUFDeUY7Z0RBQUlFLFdBQVU7MERBQ2IsNEVBQUMxSSxtSUFBVUE7b0RBQUMwSSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUs1Qiw4REFBQ0Y7b0NBQUlFLFdBQVU7OENBQ2IsNEVBQUNGO3dDQUFJRSxXQUFVOzswREFDYiw4REFBQ0Y7O2tFQUNDLDhEQUFDQzt3REFBRUMsV0FBVTtrRUFBNkI7Ozs7OztrRUFDMUMsOERBQUNEO3dEQUFFQyxXQUFVOzs0REFBbUMvRixNQUFNSyxXQUFXOzREQUFDOzs7Ozs7Ozs7Ozs7OzBEQUVwRSw4REFBQ3dGO2dEQUFJRSxXQUFVOzBEQUNiLDRFQUFDekksbUlBQUtBO29EQUFDeUksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLdkIsOERBQUNGO29DQUFJRSxXQUFVOzhDQUNiLDRFQUFDRjt3Q0FBSUUsV0FBVTs7MERBQ2IsOERBQUNGOztrRUFDQyw4REFBQ0M7d0RBQUVDLFdBQVU7a0VBQTZCOzs7Ozs7a0VBQzFDLDhEQUFDRDt3REFBRUMsV0FBVTs7NERBQXNDL0YsTUFBTU0sUUFBUTs0REFBQzs7Ozs7Ozs7Ozs7OzswREFFcEUsOERBQUN1RjtnREFBSUUsV0FBVTswREFDYiw0RUFBQzNJLG1JQUFPQTtvREFBQzJJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTzNCLDhEQUFDRjs0QkFBSUUsV0FBVTtzQ0FDYiw0RUFBQ0Y7Z0NBQUlFLFdBQVU7O2tEQUNiLDhEQUFDRjt3Q0FBSUUsV0FBVTs7MERBQ2IsOERBQUN2SSxtSUFBTUE7Z0RBQUN1SSxXQUFVOzs7Ozs7MERBQ2xCLDhEQUFDRztnREFBS0gsV0FBVTswREFBc0M7Ozs7Ozs7Ozs7OztrREFHeEQsOERBQUNGO3dDQUFJRSxXQUFVOzswREFDYiw4REFBQ1E7Z0RBQU1SLFdBQVU7MERBQTZCOzs7Ozs7MERBQzlDLDhEQUFDa0I7Z0RBQ0NwRCxPQUFPcEQ7Z0RBQ1B5RyxVQUFVLENBQUNDLElBQU16RyxpQkFBaUJ5RyxFQUFFQyxNQUFNLENBQUN2RCxLQUFLO2dEQUNoRGtDLFdBQVU7O2tFQUVWLDhEQUFDc0I7d0RBQU94RCxPQUFNO2tFQUFNOzs7Ozs7b0RBQ25CMUIsUUFBUStDLEdBQUcsQ0FBQyxDQUFDb0Msb0JBQ1osOERBQUNEOzREQUFxQnhELE9BQU95RCxJQUFJM0MsR0FBRztzRUFDakMyQyxJQUFJQyxJQUFJOzJEQURFRCxJQUFJM0MsR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTzFCLDhEQUFDa0I7d0NBQUlFLFdBQVU7OzBEQUNiLDhEQUFDUTtnREFBTVIsV0FBVTswREFBNkI7Ozs7OzswREFDOUMsOERBQUNrQjtnREFDQ3BELE9BQU9sRDtnREFDUHVHLFVBQVUsQ0FBQ0MsSUFBTXZHLG1CQUFtQnVHLEVBQUVDLE1BQU0sQ0FBQ3ZELEtBQUs7Z0RBQ2xEa0MsV0FBVTs7a0VBRVYsOERBQUNzQjt3REFBT3hELE9BQU07a0VBQU07Ozs7OztvREFDbkI5QixTQUFTbUQsR0FBRyxDQUFDLENBQUNzQyx3QkFDYiw4REFBQ0g7NERBQXlCeEQsT0FBTzJELFFBQVE3QyxHQUFHO3NFQUN6QzZDLFFBQVFELElBQUk7MkRBREZDLFFBQVE3QyxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztrREFPOUIsOERBQUNrQjt3Q0FBSUUsV0FBVTs7MERBQ2IsOERBQUNRO2dEQUFNUixXQUFVOzBEQUE2Qjs7Ozs7OzBEQUM5Qyw4REFBQ2tCO2dEQUNDcEQsT0FBT2hEO2dEQUNQcUcsVUFBVSxDQUFDQyxJQUFNckcsZ0JBQWdCcUcsRUFBRUMsTUFBTSxDQUFDdkQsS0FBSztnREFDL0NrQyxXQUFVOztrRUFFViw4REFBQ3NCO3dEQUFPeEQsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQ3dEO3dEQUFPeEQsT0FBTTtrRUFBYTs7Ozs7O2tFQUMzQiw4REFBQ3dEO3dEQUFPeEQsT0FBTTtrRUFBYzs7Ozs7O2tFQUM1Qiw4REFBQ3dEO3dEQUFPeEQsT0FBTTtrRUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUkvQiw4REFBQ2dDO3dDQUFJRSxXQUFVOzswREFDYiw4REFBQ1E7Z0RBQU1SLFdBQVU7MERBQTZCOzs7Ozs7MERBQzlDLDhEQUFDa0I7Z0RBQ0NwRCxPQUFPOUM7Z0RBQ1BtRyxVQUFVLENBQUNDLElBQU1uRyxvQkFBb0JtRyxFQUFFQyxNQUFNLENBQUN2RCxLQUFLO2dEQUNuRGtDLFdBQVU7O2tFQUVWLDhEQUFDc0I7d0RBQU94RCxPQUFNO2tFQUFNOzs7Ozs7b0RBQ25CNUIsVUFBVWlELEdBQUcsQ0FBQyxDQUFDdUMseUJBQ2QsOERBQUNKOzREQUEwQnhELE9BQU80RCxTQUFTOUMsR0FBRztzRUFDM0M4QyxTQUFTQyxJQUFJOzJEQURIRCxTQUFTOUMsR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTy9CLDhEQUFDeEcsa0RBQU1BLENBQUN3SixNQUFNO3dDQUNaQyxZQUFZOzRDQUFFQyxPQUFPO3dDQUFLO3dDQUMxQkMsVUFBVTs0Q0FBRUQsT0FBTzt3Q0FBSzt3Q0FDeEI5QixXQUFVOzswREFFViw4REFBQ3RJLG1JQUFRQTtnREFBQ21KLE1BQU07Ozs7OzswREFDaEIsOERBQUNWOzBEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNWiw4REFBQ0w7NEJBQUlFLFdBQVU7OzhDQUNiLDhEQUFDRjtvQ0FBSUUsV0FBVTs7c0RBQ2IsOERBQUNnQzs0Q0FBR2hDLFdBQVU7O2dEQUF3QztnREFDcENVLGdCQUFnQnJCLE1BQU07Z0RBQUM7Ozs7Ozs7c0RBR3pDLDhEQUFDakgsa0RBQU1BLENBQUN3SixNQUFNOzRDQUNaQyxZQUFZO2dEQUFFQyxPQUFPOzRDQUFLOzRDQUMxQkMsVUFBVTtnREFBRUQsT0FBTzs0Q0FBSzs0Q0FDeEJyQixTQUFTdEM7NENBQ1Q2QixXQUFVOzs4REFFViw4REFBQ3JJLG1JQUFJQTtvREFBQ2tKLE1BQU07Ozs7Ozs4REFDWiw4REFBQ1Y7OERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJViw4REFBQ3RJLDJDQUFRQTtvQ0FBQ29LLHdCQUFVLDhEQUFDaEssMEVBQWNBO3dDQUFDNEksTUFBTTt3Q0FBSUMsT0FBTTs7Ozs7OzhDQUNsRCw0RUFBQzlJLGtFQUFZQTt3Q0FDWDJHLE1BQU0rQjt3Q0FDTmhCLFNBQVNBO3dDQUNUYSxTQUFTQTt3Q0FDVDJCLHFCQUFxQjt3Q0FDckJDLG1CQUFtQjNEO3dDQUNuQkQsc0JBQXNCQTt3Q0FDdEIvQixnQkFBZ0JBO3dDQUNoQjRGLG9CQUFvQixJQUFNM0Ysa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPcEQsOERBQUN6RCxzRUFBVUE7b0JBQ1RxSixRQUFRbkg7b0JBQ1JvSCxTQUFTO3dCQUNQbkgsb0JBQW9CO3dCQUNwQkksZUFBZTtvQkFDakI7b0JBQ0FnSCxVQUFVN0Q7b0JBQ1ZMLE9BQU8vQztvQkFDUFEsVUFBVUE7b0JBQ1ZFLFVBQVVBO29CQUNWRSxXQUFXQTtvQkFDWHNHLFNBQVNsRzs7Ozs7OzhCQUlYLDhEQUFDckQsc0ZBQTBCQTtvQkFDekJvSixRQUFRakg7b0JBQ1JrSCxTQUFTO3dCQUNQakgscUJBQXFCO3dCQUNyQkksaUJBQWlCO29CQUNuQjtvQkFDQWdILFdBQVc1RDtvQkFDWHRGLE9BQ0VtQyxlQUFlLFdBQ1gsd0JBQ0E7b0JBRU40RCxTQUNFNUQsZUFBZSxXQUNYLHFGQUNBLG1DQUF5RCxPQUF0QkUsZUFBZXlELE1BQU0sRUFBQztvQkFFL0RxRCxVQUNFaEgsZUFBZSxZQUFZRixnQkFDdkIsR0FBbUNBLE9BQWhDQSxjQUFjeUUsWUFBWSxFQUFDLE9BQW9DekUsT0FBL0JBLGNBQWM2RSxZQUFZLEVBQUMsTUFBNEIsT0FBeEI3RSxjQUFjOEUsU0FBUyxFQUFDLE9BQzFGcUM7b0JBRU5DLFdBQVdsSCxlQUFlLGFBQWFFLGVBQWV5RCxNQUFNLEdBQUdzRDtvQkFDL0RoQixNQUFNakc7Ozs7Ozs4QkFJUiw4REFBQ3ZDLGlFQUFjQTtvQkFBQ1EsUUFBUUE7b0JBQVEySSxTQUFTMUk7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSWpEO0dBdG5Cd0JKOztRQUNHdEIsMERBQU9BO1FBQ3dCZ0IsMkRBQVFBOzs7S0FGMUNNIiwic291cmNlcyI6WyJEOlxcZWx5c2VlXFxQcm9qZXRcXFBlcnNcXHNjaG9sYXJpZnlcXGRhc2hib2FyZFxcc3JjXFxhcHBcXChkYXNoYm9hcmRzKVxcc2Nob29sLWFkbWluXFxncmFkZXNcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0IHsgUGVyY2VudCwgVHJlbmRpbmdVcCwgVXNlcnMsIEJvb2tPcGVuLCBGaWx0ZXIsIERvd25sb2FkLCBQbHVzIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xyXG5pbXBvcnQgU2Nob29sTGF5b3V0IGZyb20gXCJAL2NvbXBvbmVudHMvRGFzaGJvYXJkL0xheW91dHMvU2Nob29sTGF5b3V0XCI7XHJcbmltcG9ydCB7IFN1c3BlbnNlLCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBEYXRhVGFibGVGaXggZnJvbSBcIkAvY29tcG9uZW50cy91dGlscy9UYWJsZUZpeFwiO1xyXG5cclxuaW1wb3J0IENpcmN1bGFyTG9hZGVyIGZyb20gXCJAL2NvbXBvbmVudHMvd2lkZ2V0cy9DaXJjdWxhckxvYWRlclwiO1xyXG5pbXBvcnQgdXNlQXV0aCBmcm9tIFwiQC9hcHAvaG9va3MvdXNlQXV0aFwiO1xyXG5pbXBvcnQgUHJvdGVjdGVkUm91dGUgZnJvbSBcIkAvY29tcG9uZW50cy91dGlscy9Qcm90ZWN0ZWRSb3V0ZVwiO1xyXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tIFwiZnJhbWVyLW1vdGlvblwiO1xyXG5pbXBvcnQge1xyXG4gIGdldEdyYWRlUmVjb3JkcyxcclxuICBnZXRHcmFkZVN0YXRzLFxyXG4gIGNyZWF0ZUdyYWRlLFxyXG4gIHVwZGF0ZUdyYWRlLFxyXG4gIGRlbGV0ZUdyYWRlLFxyXG4gIGRlbGV0ZU11bHRpcGxlR3JhZGVzLFxyXG4gIEdyYWRlUmVjb3JkLFxyXG4gIEdyYWRlU3RhdHNcclxufSBmcm9tIFwiQC9hcHAvc2VydmljZXMvR3JhZGVTZXJ2aWNlc1wiO1xyXG5pbXBvcnQgeyBnZXRTdHVkZW50c0J5U2Nob29sIH0gZnJvbSBcIkAvYXBwL3NlcnZpY2VzL1N0dWRlbnRTZXJ2aWNlc1wiO1xyXG5pbXBvcnQgeyBnZXRTdWJqZWN0c0J5U2Nob29sSWQgfSBmcm9tIFwiQC9hcHAvc2VydmljZXMvU3ViamVjdFNlcnZpY2VzXCI7XHJcbmltcG9ydCB7IGdldEV4YW1UeXBlcyB9IGZyb20gXCJAL2FwcC9zZXJ2aWNlcy9FeGFtVHlwZVNlcnZpY2VzXCI7XHJcbmltcG9ydCB7IGdldENsYXNzZXNCeVNjaG9vbCB9IGZyb20gXCJAL2FwcC9zZXJ2aWNlcy9DbGFzc1NlcnZpY2VzXCI7XHJcbmltcG9ydCB7IHZlcmlmeVBhc3N3b3JkIH0gZnJvbSBcIkAvYXBwL3NlcnZpY2VzL1VzZXJTZXJ2aWNlc1wiO1xyXG5pbXBvcnQgR3JhZGVNb2RhbCBmcm9tIFwiQC9jb21wb25lbnRzL21vZGFscy9HcmFkZU1vZGFsXCI7XHJcbmltcG9ydCBQYXNzd29yZENvbmZpcm1EZWxldGVNb2RhbCBmcm9tIFwiQC9jb21wb25lbnRzL21vZGFscy9QYXNzd29yZENvbmZpcm1EZWxldGVNb2RhbFwiO1xyXG5pbXBvcnQgeyB1c2VUb2FzdCwgVG9hc3RDb250YWluZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL1RvYXN0XCI7XHJcblxyXG5cclxuXHJcbmNvbnN0IG5hdmlnYXRpb24gPSB7XHJcbiAgaWNvbjogUGVyY2VudCxcclxuICBiYXNlSHJlZjogXCIvc2Nob29sLWFkbWluL2dyYWRlc1wiLFxyXG4gIHRpdGxlOiBcIkdyYWRlc1wiXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBHcmFkZXNQYWdlKCkge1xyXG4gIGNvbnN0IHsgbG9nb3V0LCB1c2VyIH0gPSB1c2VBdXRoKCk7XHJcbiAgY29uc3QgeyB0b2FzdHMsIHJlbW92ZVRvYXN0LCBzaG93U3VjY2Vzcywgc2hvd0Vycm9yIH0gPSB1c2VUb2FzdCgpO1xyXG5cclxuICAvLyBTdGF0ZSBtYW5hZ2VtZW50XHJcbiAgY29uc3QgW2dyYWRlUmVjb3Jkcywgc2V0R3JhZGVSZWNvcmRzXSA9IHVzZVN0YXRlPEdyYWRlUmVjb3JkW10+KFtdKTtcclxuICBjb25zdCBbc3RhdHMsIHNldFN0YXRzXSA9IHVzZVN0YXRlPEdyYWRlU3RhdHM+KHtcclxuICAgIHRvdGFsR3JhZGVzOiAwLFxyXG4gICAgYXZlcmFnZVNjb3JlOiAwLFxyXG4gICAgaGlnaGVzdFNjb3JlOiAwLFxyXG4gICAgbG93ZXN0U2NvcmU6IDAsXHJcbiAgICBwYXNzUmF0ZTogMFxyXG4gIH0pO1xyXG4gIGNvbnN0IFtsb2FkaW5nRGF0YSwgc2V0TG9hZGluZ0RhdGFdID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW3NlbGVjdGVkQ2xhc3MsIHNldFNlbGVjdGVkQ2xhc3NdID0gdXNlU3RhdGUoJ2FsbCcpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZFN1YmplY3QsIHNldFNlbGVjdGVkU3ViamVjdF0gPSB1c2VTdGF0ZSgnYWxsJyk7XHJcbiAgY29uc3QgW3NlbGVjdGVkVGVybSwgc2V0U2VsZWN0ZWRUZXJtXSA9IHVzZVN0YXRlKCdhbGwnKTtcclxuICBjb25zdCBbc2VsZWN0ZWRFeGFtVHlwZSwgc2V0U2VsZWN0ZWRFeGFtVHlwZV0gPSB1c2VTdGF0ZSgnYWxsJyk7XHJcblxyXG4gIC8vIE1vZGFsIHN0YXRlc1xyXG4gIGNvbnN0IFtpc0dyYWRlTW9kYWxPcGVuLCBzZXRJc0dyYWRlTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNEZWxldGVNb2RhbE9wZW4sIHNldElzRGVsZXRlTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbZ3JhZGVUb0VkaXQsIHNldEdyYWRlVG9FZGl0XSA9IHVzZVN0YXRlPEdyYWRlUmVjb3JkIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2dyYWRlVG9EZWxldGUsIHNldEdyYWRlVG9EZWxldGVdID0gdXNlU3RhdGU8R3JhZGVSZWNvcmQgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbZGVsZXRlVHlwZSwgc2V0RGVsZXRlVHlwZV0gPSB1c2VTdGF0ZTxcInNpbmdsZVwiIHwgXCJtdWx0aXBsZVwiPihcInNpbmdsZVwiKTtcclxuICBjb25zdCBbc2VsZWN0ZWRHcmFkZXMsIHNldFNlbGVjdGVkR3JhZGVzXSA9IHVzZVN0YXRlPEdyYWRlUmVjb3JkW10+KFtdKTtcclxuXHJcbiAgLy8gQWRkaXRpb25hbCBkYXRhIGZvciBmb3Jtc1xyXG4gIGNvbnN0IFtzdHVkZW50cywgc2V0U3R1ZGVudHNdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcclxuICBjb25zdCBbc3ViamVjdHMsIHNldFN1YmplY3RzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XHJcbiAgY29uc3QgW2V4YW1UeXBlcywgc2V0RXhhbVR5cGVzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XHJcbiAgY29uc3QgW2NsYXNzZXMsIHNldENsYXNzZXNdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcclxuXHJcbiAgLy8gTG9hZGluZyBzdGF0ZXNcclxuICBjb25zdCBbaXNTdWJtaXR0aW5nLCBzZXRJc1N1Ym1pdHRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtjbGVhclNlbGVjdGlvbiwgc2V0Q2xlYXJTZWxlY3Rpb25dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICAvLyBHZXQgc2Nob29sIElEIGZyb20gdXNlclxyXG4gIGNvbnN0IHNjaG9vbElkID0gdXNlcj8uc2Nob29sX2lkcz8uWzBdIHx8IHVzZXI/LnNjaG9vbF9pZDtcclxuXHJcbiAgLy8gRmV0Y2ggZ3JhZGUgZGF0YSBmcm9tIEFQSVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBmZXRjaEdyYWRlRGF0YSA9IGFzeW5jICgpID0+IHtcclxuICAgICAgaWYgKCFzY2hvb2xJZCkge1xyXG4gICAgICAgIHNob3dFcnJvcihcIkVycm9yXCIsIFwiTm8gc2Nob29sIElEIGZvdW5kXCIpO1xyXG4gICAgICAgIHNldExvYWRpbmdEYXRhKGZhbHNlKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgc2V0TG9hZGluZ0RhdGEodHJ1ZSk7XHJcblxyXG4gICAgICAgIC8vIEJ1aWxkIGZpbHRlcnNcclxuICAgICAgICBjb25zdCBmaWx0ZXJzOiBhbnkgPSB7fTtcclxuICAgICAgICBpZiAoc2VsZWN0ZWRDbGFzcyAhPT0gJ2FsbCcpIGZpbHRlcnMuY2xhc3NfaWQgPSBzZWxlY3RlZENsYXNzO1xyXG4gICAgICAgIGlmIChzZWxlY3RlZFN1YmplY3QgIT09ICdhbGwnKSBmaWx0ZXJzLnN1YmplY3RfaWQgPSBzZWxlY3RlZFN1YmplY3Q7XHJcbiAgICAgICAgaWYgKHNlbGVjdGVkVGVybSAhPT0gJ2FsbCcpIGZpbHRlcnMudGVybSA9IHNlbGVjdGVkVGVybTtcclxuICAgICAgICBpZiAoc2VsZWN0ZWRFeGFtVHlwZSAhPT0gJ2FsbCcpIGZpbHRlcnMuZXhhbV90eXBlX2lkID0gc2VsZWN0ZWRFeGFtVHlwZTtcclxuXHJcbiAgICAgICAgLy8gRmV0Y2ggcmVjb3JkcyBhbmQgc3RhdHMgaW4gcGFyYWxsZWxcclxuICAgICAgICBjb25zdCBbcmVjb3Jkc1Jlc3BvbnNlLCBzdGF0c1Jlc3BvbnNlXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcclxuICAgICAgICAgIGdldEdyYWRlUmVjb3JkcyhzY2hvb2xJZCBhcyBzdHJpbmcsIGZpbHRlcnMpLFxyXG4gICAgICAgICAgZ2V0R3JhZGVTdGF0cyhzY2hvb2xJZCBhcyBzdHJpbmcsIGZpbHRlcnMpXHJcbiAgICAgICAgXSk7XHJcblxyXG4gICAgICAgIHNldEdyYWRlUmVjb3JkcyhyZWNvcmRzUmVzcG9uc2UuZ3JhZGVfcmVjb3Jkcyk7XHJcbiAgICAgICAgc2V0U3RhdHMoc3RhdHNSZXNwb25zZS5zdGF0cyk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIGdyYWRlIGRhdGE6XCIsIGVycm9yKTtcclxuICAgICAgICBzaG93RXJyb3IoXCJFcnJvclwiLCBcIkZhaWxlZCB0byBsb2FkIGdyYWRlIGRhdGFcIik7XHJcbiAgICAgIH0gZmluYWxseSB7XHJcbiAgICAgICAgc2V0TG9hZGluZ0RhdGEoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGZldGNoR3JhZGVEYXRhKCk7XHJcbiAgfSwgW3NjaG9vbElkLCBzZWxlY3RlZENsYXNzLCBzZWxlY3RlZFN1YmplY3QsIHNlbGVjdGVkVGVybSwgc2VsZWN0ZWRFeGFtVHlwZV0pO1xyXG5cclxuICAvLyBGZXRjaCBhZGRpdGlvbmFsIGRhdGEgZm9yIG1vZGFsc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBmZXRjaEFkZGl0aW9uYWxEYXRhID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICBpZiAoIXNjaG9vbElkKSByZXR1cm47XHJcblxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIC8vIEZldGNoIGRhdGEgd2l0aCBpbmRpdmlkdWFsIGVycm9yIGhhbmRsaW5nXHJcbiAgICAgICAgY29uc3QgcmVzdWx0cyA9IGF3YWl0IFByb21pc2UuYWxsU2V0dGxlZChbXHJcbiAgICAgICAgICBnZXRTdHVkZW50c0J5U2Nob29sKHNjaG9vbElkIGFzIHN0cmluZyksXHJcbiAgICAgICAgICBnZXRTdWJqZWN0c0J5U2Nob29sSWQoc2Nob29sSWQgYXMgc3RyaW5nKSxcclxuICAgICAgICAgIGdldEV4YW1UeXBlcygpLFxyXG4gICAgICAgICAgZ2V0Q2xhc3Nlc0J5U2Nob29sKHNjaG9vbElkIGFzIHN0cmluZylcclxuICAgICAgICBdKTtcclxuXHJcbiAgICAgICAgLy8gSGFuZGxlIGVhY2ggcmVzdWx0IGluZGl2aWR1YWxseVxyXG4gICAgICAgIGlmIChyZXN1bHRzWzBdLnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcpIHtcclxuICAgICAgICAgIHNldFN0dWRlbnRzKHJlc3VsdHNbMF0udmFsdWUpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGZldGNoIHN0dWRlbnRzOlwiLCByZXN1bHRzWzBdLnJlYXNvbik7XHJcbiAgICAgICAgICBzZXRTdHVkZW50cyhbXSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAocmVzdWx0c1sxXS5zdGF0dXMgPT09ICdmdWxmaWxsZWQnKSB7XHJcbiAgICAgICAgICBzZXRTdWJqZWN0cyhyZXN1bHRzWzFdLnZhbHVlKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBmZXRjaCBzdWJqZWN0czpcIiwgcmVzdWx0c1sxXS5yZWFzb24pO1xyXG4gICAgICAgICAgc2V0U3ViamVjdHMoW10pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKHJlc3VsdHNbMl0uc3RhdHVzID09PSAnZnVsZmlsbGVkJykge1xyXG4gICAgICAgICAgc2V0RXhhbVR5cGVzKHJlc3VsdHNbMl0udmFsdWUpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGZldGNoIGV4YW0gdHlwZXM6XCIsIHJlc3VsdHNbMl0ucmVhc29uKTtcclxuICAgICAgICAgIHNldEV4YW1UeXBlcyhbXSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAocmVzdWx0c1szXS5zdGF0dXMgPT09ICdmdWxmaWxsZWQnKSB7XHJcbiAgICAgICAgICBzZXRDbGFzc2VzKHJlc3VsdHNbM10udmFsdWUpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGZldGNoIGNsYXNzZXM6XCIsIHJlc3VsdHNbM10ucmVhc29uKTtcclxuICAgICAgICAgIHNldENsYXNzZXMoW10pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gU2hvdyB3YXJuaW5nIGlmIGFueSBjcml0aWNhbCBkYXRhIGZhaWxlZCB0byBsb2FkXHJcbiAgICAgICAgY29uc3QgYW55RGF0YUZhaWxlZCA9IHJlc3VsdHMuc29tZShyZXN1bHQgPT4gcmVzdWx0LnN0YXR1cyA9PT0gJ3JlamVjdGVkJyk7XHJcbiAgICAgICAgaWYgKGFueURhdGFGYWlsZWQpIHtcclxuICAgICAgICAgIHNob3dFcnJvcihcIldhcm5pbmdcIiwgXCJTb21lIGZvcm0gZGF0YSBjb3VsZCBub3QgYmUgbG9hZGVkLiBTb21lIGZlYXR1cmVzIG1heSBiZSBsaW1pdGVkLlwiKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIGFkZGl0aW9uYWwgZGF0YTpcIiwgZXJyb3IpO1xyXG4gICAgICAgIHNob3dFcnJvcihcIkVycm9yXCIsIFwiRmFpbGVkIHRvIGxvYWQgZm9ybSBkYXRhXCIpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGZldGNoQWRkaXRpb25hbERhdGEoKTtcclxuICB9LCBbc2Nob29sSWRdKTsgLy8gUmVtb3ZlZCBzaG93RXJyb3IgZnJvbSBkZXBlbmRlbmNpZXNcclxuXHJcbiAgLy8gQ1JVRCBGdW5jdGlvbnNcclxuICBjb25zdCBoYW5kbGVDcmVhdGVHcmFkZSA9ICgpID0+IHtcclxuICAgIHNldEdyYWRlVG9FZGl0KG51bGwpO1xyXG4gICAgc2V0SXNHcmFkZU1vZGFsT3Blbih0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVFZGl0R3JhZGUgPSAoZ3JhZGU6IEdyYWRlUmVjb3JkKSA9PiB7XHJcbiAgICBzZXRHcmFkZVRvRWRpdChncmFkZSk7XHJcbiAgICBzZXRJc0dyYWRlTW9kYWxPcGVuKHRydWUpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUdyYWRlID0gKGdyYWRlOiBHcmFkZVJlY29yZCkgPT4ge1xyXG4gICAgc2V0R3JhZGVUb0RlbGV0ZShncmFkZSk7XHJcbiAgICBzZXREZWxldGVUeXBlKFwic2luZ2xlXCIpO1xyXG4gICAgc2V0SXNEZWxldGVNb2RhbE9wZW4odHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRGVsZXRlTXVsdGlwbGUgPSAoKSA9PiB7XHJcbiAgICBzZXREZWxldGVUeXBlKFwibXVsdGlwbGVcIik7XHJcbiAgICBzZXRJc0RlbGV0ZU1vZGFsT3Blbih0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UgPSAoc2VsZWN0ZWRSb3dzOiBHcmFkZVJlY29yZFtdKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZEdyYWRlcyhzZWxlY3RlZFJvd3MpO1xyXG4gIH07XHJcblxyXG4gIC8vIE1vZGFsIHN1Ym1pc3Npb24gZnVuY3Rpb25zXHJcbiAgY29uc3QgaGFuZGxlR3JhZGVTdWJtaXQgPSBhc3luYyAoZGF0YTogYW55KSA9PiB7XHJcbiAgICBpZiAoIXNjaG9vbElkKSB7XHJcbiAgICAgIHNob3dFcnJvcihcIkVycm9yXCIsIFwiTm8gc2Nob29sIElEIGZvdW5kXCIpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgc2V0SXNTdWJtaXR0aW5nKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgaWYgKGdyYWRlVG9FZGl0KSB7XHJcbiAgICAgICAgLy8gVXBkYXRlIGV4aXN0aW5nIGdyYWRlXHJcbiAgICAgICAgYXdhaXQgdXBkYXRlR3JhZGUoZ3JhZGVUb0VkaXQuX2lkLCBkYXRhKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyBDcmVhdGUgbmV3IGdyYWRlXHJcbiAgICAgICAgYXdhaXQgY3JlYXRlR3JhZGUoeyAuLi5kYXRhLCBzY2hvb2xfaWQ6IHNjaG9vbElkIH0pO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBSZWZyZXNoIGdyYWRlcyBsaXN0XHJcbiAgICAgIGNvbnN0IGZpbHRlcnM6IGFueSA9IHt9O1xyXG4gICAgICBpZiAoc2VsZWN0ZWRDbGFzcyAhPT0gJ2FsbCcpIGZpbHRlcnMuY2xhc3NfaWQgPSBzZWxlY3RlZENsYXNzO1xyXG4gICAgICBpZiAoc2VsZWN0ZWRTdWJqZWN0ICE9PSAnYWxsJykgZmlsdGVycy5zdWJqZWN0X2lkID0gc2VsZWN0ZWRTdWJqZWN0O1xyXG4gICAgICBpZiAoc2VsZWN0ZWRUZXJtICE9PSAnYWxsJykgZmlsdGVycy50ZXJtID0gc2VsZWN0ZWRUZXJtO1xyXG4gICAgICBpZiAoc2VsZWN0ZWRFeGFtVHlwZSAhPT0gJ2FsbCcpIGZpbHRlcnMuZXhhbV90eXBlX2lkID0gc2VsZWN0ZWRFeGFtVHlwZTtcclxuXHJcbiAgICAgIGNvbnN0IFtyZWNvcmRzUmVzcG9uc2UsIHN0YXRzUmVzcG9uc2VdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xyXG4gICAgICAgIGdldEdyYWRlUmVjb3JkcyhzY2hvb2xJZCBhcyBzdHJpbmcsIGZpbHRlcnMpLFxyXG4gICAgICAgIGdldEdyYWRlU3RhdHMoc2Nob29sSWQgYXMgc3RyaW5nLCBmaWx0ZXJzKVxyXG4gICAgICBdKTtcclxuXHJcbiAgICAgIHNldEdyYWRlUmVjb3JkcyhyZWNvcmRzUmVzcG9uc2UuZ3JhZGVfcmVjb3Jkcyk7XHJcbiAgICAgIHNldFN0YXRzKHN0YXRzUmVzcG9uc2Uuc3RhdHMpO1xyXG4gICAgICBzZXRJc0dyYWRlTW9kYWxPcGVuKGZhbHNlKTtcclxuICAgICAgc2V0R3JhZGVUb0VkaXQobnVsbCk7XHJcblxyXG4gICAgICAvLyBTaG93IHN1Y2Nlc3Mgbm90aWZpY2F0aW9uXHJcbiAgICAgIHNob3dTdWNjZXNzKFxyXG4gICAgICAgIGdyYWRlVG9FZGl0ID8gXCJHcmFkZSBVcGRhdGVkXCIgOiBcIkdyYWRlIEFkZGVkXCIsXHJcbiAgICAgICAgZ3JhZGVUb0VkaXQgPyBcIkdyYWRlIGhhcyBiZWVuIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5LlwiIDogXCJOZXcgZ3JhZGUgaGFzIGJlZW4gYWRkZWQgc3VjY2Vzc2Z1bGx5LlwiXHJcbiAgICAgICk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3Igc3VibWl0dGluZyBncmFkZTpcIiwgZXJyb3IpO1xyXG4gICAgICBzaG93RXJyb3IoXCJFcnJvclwiLCBcIkZhaWxlZCB0byBzYXZlIGdyYWRlLiBQbGVhc2UgdHJ5IGFnYWluLlwiKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc1N1Ym1pdHRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUNvbmZpcm0gPSBhc3luYyAocGFzc3dvcmQ6IHN0cmluZykgPT4ge1xyXG4gICAgaWYgKCFzY2hvb2xJZCB8fCAhdXNlcikgcmV0dXJuO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFZlcmlmeSBwYXNzd29yZFxyXG4gICAgICBjb25zdCBwYXNzd29yZFZlcmlmaWVkID0gYXdhaXQgdmVyaWZ5UGFzc3dvcmQocGFzc3dvcmQsIHVzZXIuZW1haWwpO1xyXG4gICAgICBpZiAoIXBhc3N3b3JkVmVyaWZpZWQpIHtcclxuICAgICAgICBzaG93RXJyb3IoXCJFcnJvclwiLCBcIkludmFsaWQgcGFzc3dvcmQuIFBsZWFzZSB0cnkgYWdhaW4uXCIpO1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgcGFzc3dvcmRcIik7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChkZWxldGVUeXBlID09PSBcInNpbmdsZVwiICYmIGdyYWRlVG9EZWxldGUpIHtcclxuICAgICAgICBhd2FpdCBkZWxldGVHcmFkZShncmFkZVRvRGVsZXRlLl9pZCk7XHJcbiAgICAgIH0gZWxzZSBpZiAoZGVsZXRlVHlwZSA9PT0gXCJtdWx0aXBsZVwiKSB7XHJcbiAgICAgICAgY29uc3Qgc2VsZWN0ZWRJZHMgPSBzZWxlY3RlZEdyYWRlcy5tYXAoZyA9PiBnLl9pZCk7XHJcbiAgICAgICAgYXdhaXQgZGVsZXRlTXVsdGlwbGVHcmFkZXMoc2VsZWN0ZWRJZHMpO1xyXG4gICAgICAgIHNldENsZWFyU2VsZWN0aW9uKHRydWUpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBSZWZyZXNoIGdyYWRlcyBsaXN0XHJcbiAgICAgIGNvbnN0IGZpbHRlcnM6IGFueSA9IHt9O1xyXG4gICAgICBpZiAoc2VsZWN0ZWRDbGFzcyAhPT0gJ2FsbCcpIGZpbHRlcnMuY2xhc3NfaWQgPSBzZWxlY3RlZENsYXNzO1xyXG4gICAgICBpZiAoc2VsZWN0ZWRTdWJqZWN0ICE9PSAnYWxsJykgZmlsdGVycy5zdWJqZWN0X2lkID0gc2VsZWN0ZWRTdWJqZWN0O1xyXG4gICAgICBpZiAoc2VsZWN0ZWRUZXJtICE9PSAnYWxsJykgZmlsdGVycy50ZXJtID0gc2VsZWN0ZWRUZXJtO1xyXG4gICAgICBpZiAoc2VsZWN0ZWRFeGFtVHlwZSAhPT0gJ2FsbCcpIGZpbHRlcnMuZXhhbV90eXBlX2lkID0gc2VsZWN0ZWRFeGFtVHlwZTtcclxuXHJcbiAgICAgIGNvbnN0IFtyZWNvcmRzUmVzcG9uc2UsIHN0YXRzUmVzcG9uc2VdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xyXG4gICAgICAgIGdldEdyYWRlUmVjb3JkcyhzY2hvb2xJZCBhcyBzdHJpbmcsIGZpbHRlcnMpLFxyXG4gICAgICAgIGdldEdyYWRlU3RhdHMoc2Nob29sSWQgYXMgc3RyaW5nLCBmaWx0ZXJzKVxyXG4gICAgICBdKTtcclxuXHJcbiAgICAgIHNldEdyYWRlUmVjb3JkcyhyZWNvcmRzUmVzcG9uc2UuZ3JhZGVfcmVjb3Jkcyk7XHJcbiAgICAgIHNldFN0YXRzKHN0YXRzUmVzcG9uc2Uuc3RhdHMpO1xyXG4gICAgICBzZXRJc0RlbGV0ZU1vZGFsT3BlbihmYWxzZSk7XHJcbiAgICAgIHNldEdyYWRlVG9EZWxldGUobnVsbCk7XHJcbiAgICAgIHNldFNlbGVjdGVkR3JhZGVzKFtdKTtcclxuXHJcbiAgICAgIC8vIFNob3cgc3VjY2VzcyBub3RpZmljYXRpb25cclxuICAgICAgaWYgKGRlbGV0ZVR5cGUgPT09IFwic2luZ2xlXCIpIHtcclxuICAgICAgICBzaG93U3VjY2VzcyhcIkdyYWRlIERlbGV0ZWRcIiwgXCJHcmFkZSByZWNvcmQgaGFzIGJlZW4gZGVsZXRlZCBzdWNjZXNzZnVsbHkuXCIpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNob3dTdWNjZXNzKFwiR3JhZGVzIERlbGV0ZWRcIiwgYCR7c2VsZWN0ZWRHcmFkZXMubGVuZ3RofSBncmFkZSByZWNvcmRzIGhhdmUgYmVlbiBkZWxldGVkIHN1Y2Nlc3NmdWxseS5gKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZGVsZXRpbmcgZ3JhZGUocyk6XCIsIGVycm9yKTtcclxuICAgICAgaWYgKGVycm9yLm1lc3NhZ2UgIT09IFwiSW52YWxpZCBwYXNzd29yZFwiKSB7XHJcbiAgICAgICAgc2hvd0Vycm9yKFwiRXJyb3JcIiwgXCJGYWlsZWQgdG8gZGVsZXRlIGdyYWRlIHJlY29yZChzKS4gUGxlYXNlIHRyeSBhZ2Fpbi5cIik7XHJcbiAgICAgIH1cclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0R3JhZGVDb2xvciA9IChncmFkZTogc3RyaW5nKSA9PiB7XHJcbiAgICBzd2l0Y2ggKGdyYWRlKSB7XHJcbiAgICAgIGNhc2UgJ0ErJzpcclxuICAgICAgY2FzZSAnQSc6XHJcbiAgICAgICAgcmV0dXJuICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAgZGFyazpiZy1ncmVlbi05MDAvMzAgZGFyazp0ZXh0LWdyZWVuLTMwMCc7XHJcbiAgICAgIGNhc2UgJ0IrJzpcclxuICAgICAgY2FzZSAnQic6XHJcbiAgICAgICAgcmV0dXJuICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwIGRhcms6YmctYmx1ZS05MDAvMzAgZGFyazp0ZXh0LWJsdWUtMzAwJztcclxuICAgICAgY2FzZSAnQysnOlxyXG4gICAgICBjYXNlICdDJzpcclxuICAgICAgICByZXR1cm4gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwIGRhcms6YmcteWVsbG93LTkwMC8zMCBkYXJrOnRleHQteWVsbG93LTMwMCc7XHJcbiAgICAgIGNhc2UgJ0QnOlxyXG4gICAgICBjYXNlICdGJzpcclxuICAgICAgICByZXR1cm4gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwIGRhcms6YmctcmVkLTkwMC8zMCBkYXJrOnRleHQtcmVkLTMwMCc7XHJcbiAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgcmV0dXJuICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwIGRhcms6YmctZ3JheS05MDAvMzAgZGFyazp0ZXh0LWdyYXktMzAwJztcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBnZXRTY29yZUNvbG9yID0gKHNjb3JlOiBudW1iZXIpID0+IHtcclxuICAgIGlmIChzY29yZSA+PSA5MCkgcmV0dXJuICd0ZXh0LWdyZWVuLTYwMCBmb250LXNlbWlib2xkJztcclxuICAgIGlmIChzY29yZSA+PSA4MCkgcmV0dXJuICd0ZXh0LWJsdWUtNjAwIGZvbnQtc2VtaWJvbGQnO1xyXG4gICAgaWYgKHNjb3JlID49IDcwKSByZXR1cm4gJ3RleHQteWVsbG93LTYwMCBmb250LXNlbWlib2xkJztcclxuICAgIGlmIChzY29yZSA+PSA2MCkgcmV0dXJuICd0ZXh0LW9yYW5nZS02MDAgZm9udC1zZW1pYm9sZCc7XHJcbiAgICByZXR1cm4gJ3RleHQtcmVkLTYwMCBmb250LXNlbWlib2xkJztcclxuICB9O1xyXG5cclxuICAvLyBUYWJsZSBjb2x1bW5zXHJcbiAgY29uc3QgY29sdW1ucyA9IFtcclxuICAgIHsgXHJcbiAgICAgIGhlYWRlcjogXCJTdHVkZW50XCIsIFxyXG4gICAgICBhY2Nlc3NvcjogKHJvdzogR3JhZGVSZWNvcmQpID0+IChcclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1mb3JlZ3JvdW5kXCI+e3Jvdy5zdHVkZW50X25hbWV9PC9wPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWZvcmVncm91bmQvNjBcIj57cm93LnN0dWRlbnRfaWR9PC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApXHJcbiAgICB9LFxyXG4gICAgeyBcclxuICAgICAgaGVhZGVyOiBcIkNsYXNzXCIsIFxyXG4gICAgICBhY2Nlc3NvcjogKHJvdzogR3JhZGVSZWNvcmQpID0+IChcclxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+e3Jvdy5jbGFzc19uYW1lfTwvc3Bhbj5cclxuICAgICAgKVxyXG4gICAgfSxcclxuICAgIHsgXHJcbiAgICAgIGhlYWRlcjogXCJTdWJqZWN0XCIsIFxyXG4gICAgICBhY2Nlc3NvcjogKHJvdzogR3JhZGVSZWNvcmQpID0+IChcclxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e3Jvdy5zdWJqZWN0X25hbWV9PC9zcGFuPlxyXG4gICAgICApXHJcbiAgICB9LFxyXG4gICAgeyBcclxuICAgICAgaGVhZGVyOiBcIkV4YW0gVHlwZVwiLCBcclxuICAgICAgYWNjZXNzb3I6IChyb3c6IEdyYWRlUmVjb3JkKSA9PiAoXHJcbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPntyb3cuZXhhbV90eXBlfTwvc3Bhbj5cclxuICAgICAgKVxyXG4gICAgfSxcclxuICAgIHsgXHJcbiAgICAgIGhlYWRlcjogXCJTY29yZVwiLCBcclxuICAgICAgYWNjZXNzb3I6IChyb3c6IEdyYWRlUmVjb3JkKSA9PiAoXHJcbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC1sZyBmb250LWJvbGQgJHtnZXRTY29yZUNvbG9yKHJvdy5zY29yZSl9YH0+XHJcbiAgICAgICAgICB7cm93LnNjb3JlfSVcclxuICAgICAgICA8L3NwYW4+XHJcbiAgICAgIClcclxuICAgIH0sXHJcbiAgICB7IFxyXG4gICAgICBoZWFkZXI6IFwiR3JhZGVcIiwgXHJcbiAgICAgIGFjY2Vzc29yOiAocm93OiBHcmFkZVJlY29yZCkgPT4gKFxyXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmb250LWJvbGQgJHtnZXRHcmFkZUNvbG9yKHJvdy5ncmFkZSl9YH0+XHJcbiAgICAgICAgICB7cm93LmdyYWRlfVxyXG4gICAgICAgIDwvc3Bhbj5cclxuICAgICAgKVxyXG4gICAgfSxcclxuICAgIHsgXHJcbiAgICAgIGhlYWRlcjogXCJUZXJtXCIsIFxyXG4gICAgICBhY2Nlc3NvcjogKHJvdzogR3JhZGVSZWNvcmQpID0+IChcclxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e3Jvdy50ZXJtfTwvc3Bhbj5cclxuICAgICAgKVxyXG4gICAgfSxcclxuICAgIC8vIHsgXHJcbiAgICAvLyAgIGhlYWRlcjogXCJUZWFjaGVyXCIsIFxyXG4gICAgLy8gICBhY2Nlc3NvcjogKHJvdzogR3JhZGVSZWNvcmQpID0+IChcclxuICAgIC8vICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e3Jvdy50ZWFjaGVyX25hbWV9PC9zcGFuPlxyXG4gICAgLy8gICApXHJcbiAgICAvLyB9XHJcbiAgXTtcclxuXHJcbiAgLy8gQWN0aW9ucyBmb3IgdGhlIHRhYmxlXHJcbiAgY29uc3QgYWN0aW9ucyA9IFtcclxuICAgIHtcclxuICAgICAgbGFiZWw6IFwiRWRpdFwiLFxyXG4gICAgICBvbkNsaWNrOiAoZ3JhZGU6IEdyYWRlUmVjb3JkKSA9PiB7XHJcbiAgICAgICAgaGFuZGxlRWRpdEdyYWRlKGdyYWRlKTtcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGxhYmVsOiBcIkRlbGV0ZVwiLFxyXG4gICAgICBvbkNsaWNrOiAoZ3JhZGU6IEdyYWRlUmVjb3JkKSA9PiB7XHJcbiAgICAgICAgaGFuZGxlRGVsZXRlR3JhZGUoZ3JhZGUpO1xyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICBdO1xyXG5cclxuICAvLyBGaWx0ZXIgZGF0YSBiYXNlZCBvbiBzZWxlY3Rpb25zXHJcbiAgY29uc3QgZmlsdGVyZWRSZWNvcmRzID0gZ3JhZGVSZWNvcmRzLmZpbHRlcihyZWNvcmQgPT4ge1xyXG4gICAgaWYgKHNlbGVjdGVkQ2xhc3MgIT09ICdhbGwnICYmIHJlY29yZC5jbGFzc19uYW1lICE9PSBzZWxlY3RlZENsYXNzKSByZXR1cm4gZmFsc2U7XHJcbiAgICBpZiAoc2VsZWN0ZWRTdWJqZWN0ICE9PSAnYWxsJyAmJiByZWNvcmQuc3ViamVjdF9uYW1lICE9PSBzZWxlY3RlZFN1YmplY3QpIHJldHVybiBmYWxzZTtcclxuICAgIGlmIChzZWxlY3RlZFRlcm0gIT09ICdhbGwnICYmIHJlY29yZC50ZXJtICE9PSBzZWxlY3RlZFRlcm0pIHJldHVybiBmYWxzZTtcclxuICAgIGlmIChzZWxlY3RlZEV4YW1UeXBlICE9PSAnYWxsJyAmJiByZWNvcmQuZXhhbV90eXBlICE9PSBzZWxlY3RlZEV4YW1UeXBlKSByZXR1cm4gZmFsc2U7XHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9KTtcclxuXHJcbiAgaWYgKGxvYWRpbmdEYXRhKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1iYWNrZ3JvdW5kIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgPENpcmN1bGFyTG9hZGVyIHNpemU9ezMyfSBjb2xvcj1cInRlYWxcIiAvPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPFByb3RlY3RlZFJvdXRlIGFsbG93ZWRSb2xlcz17W1wic2Nob29sX2FkbWluXCIsIFwiYWRtaW5cIiwgXCJzdXBlclwiXX0+XHJcbiAgICAgIDxTY2hvb2xMYXlvdXQgbmF2aWdhdGlvbj17bmF2aWdhdGlvbn0gb25Mb2dvdXQ9e2xvZ291dH0+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgICAgIHsvKiBIZWFkZXIgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdpZGdldCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItc3Ryb2tlIHAtNlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy10ZWFsIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8UGVyY2VudCBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtd2hpdGVcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZm9yZWdyb3VuZFwiPkdyYWRlcyBNYW5hZ2VtZW50PC9oMT5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1mb3JlZ3JvdW5kLzYwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgTW9uaXRvciBhbmQgbWFuYWdlIHN0dWRlbnQgZ3JhZGVzIGFjcm9zcyBhbGwgc3ViamVjdHNcclxuICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1mb3JlZ3JvdW5kXCI+e3N0YXRzLmF2ZXJhZ2VTY29yZX0lPC9wPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWZvcmVncm91bmQvNjBcIj5BdmVyYWdlIFNjb3JlPC9wPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBTdGF0cyBDYXJkcyAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNSBnYXAtNlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdpZGdldCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItc3Ryb2tlIHAtNlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZm9yZWdyb3VuZC82MFwiPlRvdGFsIEdyYWRlczwvcD5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZm9yZWdyb3VuZFwiPntzdGF0cy50b3RhbEdyYWRlc308L3A+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLWJsdWUtNTAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgPEJvb2tPcGVuIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC13aGl0ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdpZGdldCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItc3Ryb2tlIHAtNlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZm9yZWdyb3VuZC82MFwiPkF2ZXJhZ2UgU2NvcmU8L3A+XHJcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWZvcmVncm91bmRcIj57c3RhdHMuYXZlcmFnZVNjb3JlfSU8L3A+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLXRlYWwgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtd2hpdGVcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aWRnZXQgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXN0cm9rZSBwLTZcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWZvcmVncm91bmQvNjBcIj5IaWdoZXN0IFNjb3JlPC9wPlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj57c3RhdHMuaGlnaGVzdFNjb3JlfSU8L3A+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLWdyZWVuLTUwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC13aGl0ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdpZGdldCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItc3Ryb2tlIHAtNlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZm9yZWdyb3VuZC82MFwiPkxvd2VzdCBTY29yZTwvcD5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcmVkLTYwMFwiPntzdGF0cy5sb3dlc3RTY29yZX0lPC9wPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1yZWQtNTAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgPFVzZXJzIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC13aGl0ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdpZGdldCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItc3Ryb2tlIHAtNlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZm9yZWdyb3VuZC82MFwiPlBhc3MgUmF0ZTwvcD5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZm9yZWdyb3VuZFwiPntzdGF0cy5wYXNzUmF0ZX0lPC9wPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1wdXJwbGUtNTAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgPFBlcmNlbnQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXdoaXRlXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBGaWx0ZXJzICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aWRnZXQgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXN0cm9rZSBwLTZcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxyXG4gICAgICAgICAgICAgICAgPEZpbHRlciBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZm9yZWdyb3VuZC82MFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZm9yZWdyb3VuZFwiPkZpbHRlcnM6PC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XHJcbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWZvcmVncm91bmQvNzBcIj5DbGFzczo8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgPHNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRDbGFzc31cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWxlY3RlZENsYXNzKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIGJvcmRlciBib3JkZXItc3Ryb2tlIHJvdW5kZWQtbWQgdGV4dC1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctdGVhbCBiZy13aWRnZXQgdGV4dC1mb3JlZ3JvdW5kXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFsbFwiPkFsbCBDbGFzc2VzPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgIHtjbGFzc2VzLm1hcCgoY2xzKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2Nscy5faWR9IHZhbHVlPXtjbHMuX2lkfT5cclxuICAgICAgICAgICAgICAgICAgICAgIHtjbHMubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICA8L3NlbGVjdD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cclxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZm9yZWdyb3VuZC83MFwiPlN1YmplY3Q6PC9sYWJlbD5cclxuICAgICAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkU3ViamVjdH1cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWxlY3RlZFN1YmplY3QoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTEgYm9yZGVyIGJvcmRlci1zdHJva2Ugcm91bmRlZC1tZCB0ZXh0LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy10ZWFsIGJnLXdpZGdldCB0ZXh0LWZvcmVncm91bmRcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYWxsXCI+QWxsIFN1YmplY3RzPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgIHtzdWJqZWN0cy5tYXAoKHN1YmplY3QpID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17c3ViamVjdC5faWR9IHZhbHVlPXtzdWJqZWN0Ll9pZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7c3ViamVjdC5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxyXG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1mb3JlZ3JvdW5kLzcwXCI+VGVybTo8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgPHNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRUZXJtfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlbGVjdGVkVGVybShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBib3JkZXIgYm9yZGVyLXN0cm9rZSByb3VuZGVkLW1kIHRleHQtc20gZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXRlYWwgYmctd2lkZ2V0IHRleHQtZm9yZWdyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhbGxcIj5BbGwgVGVybXM8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkZpcnN0IFRlcm1cIj5GaXJzdCBUZXJtPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJTZWNvbmQgVGVybVwiPlNlY29uZCBUZXJtPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJUaGlyZCBUZXJtXCI+VGhpcmQgVGVybTwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgPC9zZWxlY3Q+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XHJcbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWZvcmVncm91bmQvNzBcIj5FeGFtIFR5cGU6PC9sYWJlbD5cclxuICAgICAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkRXhhbVR5cGV9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VsZWN0ZWRFeGFtVHlwZShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBib3JkZXIgYm9yZGVyLXN0cm9rZSByb3VuZGVkLW1kIHRleHQtc20gZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXRlYWwgYmctd2lkZ2V0IHRleHQtZm9yZWdyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhbGxcIj5BbGwgVHlwZXM8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAge2V4YW1UeXBlcy5tYXAoKGV4YW1UeXBlKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2V4YW1UeXBlLl9pZH0gdmFsdWU9e2V4YW1UeXBlLl9pZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7ZXhhbVR5cGUudHlwZX1cclxuICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICA8L3NlbGVjdD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cclxuICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDUgfX1cclxuICAgICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk1IH19XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtNCBweS0yIGJnLXRlYWwgdGV4dC13aGl0ZSByb3VuZGVkLW1kIGhvdmVyOmJnLXRlYWwtNjAwIHRleHQtc21cIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxEb3dubG9hZCBzaXplPXsxNH0gLz5cclxuICAgICAgICAgICAgICAgIDxzcGFuPkV4cG9ydDwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L21vdGlvbi5idXR0b24+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIEdyYWRlcyBUYWJsZSAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2lkZ2V0IHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1zdHJva2UgcC02XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTZcIj5cclxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgICAgR3JhZGUgUmVjb3JkcyAoe2ZpbHRlcmVkUmVjb3Jkcy5sZW5ndGh9KVxyXG4gICAgICAgICAgICAgIDwvaDI+XHJcbiAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cclxuICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDUgfX1cclxuICAgICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk1IH19XHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDcmVhdGVHcmFkZX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweC00IHB5LTIgYmctdGVhbCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgaG92ZXI6YmctdGVhbC02MDBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxQbHVzIHNpemU9ezE2fSAvPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4+QWRkIEdyYWRlPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8U3VzcGVuc2UgZmFsbGJhY2s9ezxDaXJjdWxhckxvYWRlciBzaXplPXsyNH0gY29sb3I9XCJ0ZWFsXCIgLz59PlxyXG4gICAgICAgICAgICAgIDxEYXRhVGFibGVGaXg8R3JhZGVSZWNvcmQ+XHJcbiAgICAgICAgICAgICAgICBkYXRhPXtmaWx0ZXJlZFJlY29yZHN9XHJcbiAgICAgICAgICAgICAgICBjb2x1bW5zPXtjb2x1bW5zfVxyXG4gICAgICAgICAgICAgICAgYWN0aW9ucz17YWN0aW9uc31cclxuICAgICAgICAgICAgICAgIGRlZmF1bHRJdGVtc1BlclBhZ2U9ezE1fVxyXG4gICAgICAgICAgICAgICAgb25TZWxlY3Rpb25DaGFuZ2U9e2hhbmRsZVNlbGVjdGlvbkNoYW5nZX1cclxuICAgICAgICAgICAgICAgIGhhbmRsZURlbGV0ZU11bHRpcGxlPXtoYW5kbGVEZWxldGVNdWx0aXBsZX1cclxuICAgICAgICAgICAgICAgIGNsZWFyU2VsZWN0aW9uPXtjbGVhclNlbGVjdGlvbn1cclxuICAgICAgICAgICAgICAgIG9uU2VsZWN0aW9uQ2xlYXJlZD17KCkgPT4gc2V0Q2xlYXJTZWxlY3Rpb24oZmFsc2UpfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvU3VzcGVuc2U+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qIEdyYWRlIE1vZGFsICovfVxyXG4gICAgICAgIDxHcmFkZU1vZGFsXHJcbiAgICAgICAgICBpc09wZW49e2lzR3JhZGVNb2RhbE9wZW59XHJcbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiB7XHJcbiAgICAgICAgICAgIHNldElzR3JhZGVNb2RhbE9wZW4oZmFsc2UpO1xyXG4gICAgICAgICAgICBzZXRHcmFkZVRvRWRpdChudWxsKTtcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBvblN1Ym1pdD17aGFuZGxlR3JhZGVTdWJtaXR9XHJcbiAgICAgICAgICBncmFkZT17Z3JhZGVUb0VkaXR9XHJcbiAgICAgICAgICBzdHVkZW50cz17c3R1ZGVudHN9XHJcbiAgICAgICAgICBzdWJqZWN0cz17c3ViamVjdHN9XHJcbiAgICAgICAgICBleGFtVHlwZXM9e2V4YW1UeXBlc31cclxuICAgICAgICAgIGxvYWRpbmc9e2lzU3VibWl0dGluZ31cclxuICAgICAgICAvPlxyXG5cclxuICAgICAgICB7LyogRGVsZXRlIENvbmZpcm1hdGlvbiBNb2RhbCAqL31cclxuICAgICAgICA8UGFzc3dvcmRDb25maXJtRGVsZXRlTW9kYWxcclxuICAgICAgICAgIGlzT3Blbj17aXNEZWxldGVNb2RhbE9wZW59XHJcbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiB7XHJcbiAgICAgICAgICAgIHNldElzRGVsZXRlTW9kYWxPcGVuKGZhbHNlKTtcclxuICAgICAgICAgICAgc2V0R3JhZGVUb0RlbGV0ZShudWxsKTtcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBvbkNvbmZpcm09e2hhbmRsZURlbGV0ZUNvbmZpcm19XHJcbiAgICAgICAgICB0aXRsZT17XHJcbiAgICAgICAgICAgIGRlbGV0ZVR5cGUgPT09IFwic2luZ2xlXCJcclxuICAgICAgICAgICAgICA/IFwiRGVsZXRlIEdyYWRlIFJlY29yZFwiXHJcbiAgICAgICAgICAgICAgOiBcIkRlbGV0ZSBTZWxlY3RlZCBHcmFkZSBSZWNvcmRzXCJcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIG1lc3NhZ2U9e1xyXG4gICAgICAgICAgICBkZWxldGVUeXBlID09PSBcInNpbmdsZVwiXHJcbiAgICAgICAgICAgICAgPyBcIkFyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byBkZWxldGUgdGhpcyBncmFkZSByZWNvcmQ/IFRoaXMgYWN0aW9uIGNhbm5vdCBiZSB1bmRvbmUuXCJcclxuICAgICAgICAgICAgICA6IGBBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlICR7c2VsZWN0ZWRHcmFkZXMubGVuZ3RofSBzZWxlY3RlZCBncmFkZSByZWNvcmRzPyBUaGlzIGFjdGlvbiBjYW5ub3QgYmUgdW5kb25lLmBcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIGl0ZW1OYW1lPXtcclxuICAgICAgICAgICAgZGVsZXRlVHlwZSA9PT0gXCJzaW5nbGVcIiAmJiBncmFkZVRvRGVsZXRlXHJcbiAgICAgICAgICAgICAgPyBgJHtncmFkZVRvRGVsZXRlLnN0dWRlbnRfbmFtZX0gLSAke2dyYWRlVG9EZWxldGUuc3ViamVjdF9uYW1lfSAoJHtncmFkZVRvRGVsZXRlLmV4YW1fdHlwZX0pYFxyXG4gICAgICAgICAgICAgIDogdW5kZWZpbmVkXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBpdGVtQ291bnQ9e2RlbGV0ZVR5cGUgPT09IFwibXVsdGlwbGVcIiA/IHNlbGVjdGVkR3JhZGVzLmxlbmd0aCA6IHVuZGVmaW5lZH1cclxuICAgICAgICAgIHR5cGU9e2RlbGV0ZVR5cGV9XHJcbiAgICAgICAgLz5cclxuXHJcbiAgICAgICAgey8qIFRvYXN0IE5vdGlmaWNhdGlvbnMgKi99XHJcbiAgICAgICAgPFRvYXN0Q29udGFpbmVyIHRvYXN0cz17dG9hc3RzfSBvbkNsb3NlPXtyZW1vdmVUb2FzdH0gLz5cclxuICAgICAgPC9TY2hvb2xMYXlvdXQ+XHJcbiAgICA8L1Byb3RlY3RlZFJvdXRlPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlBlcmNlbnQiLCJUcmVuZGluZ1VwIiwiVXNlcnMiLCJCb29rT3BlbiIsIkZpbHRlciIsIkRvd25sb2FkIiwiUGx1cyIsIlNjaG9vbExheW91dCIsIlN1c3BlbnNlIiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJEYXRhVGFibGVGaXgiLCJDaXJjdWxhckxvYWRlciIsInVzZUF1dGgiLCJQcm90ZWN0ZWRSb3V0ZSIsIm1vdGlvbiIsImdldEdyYWRlUmVjb3JkcyIsImdldEdyYWRlU3RhdHMiLCJjcmVhdGVHcmFkZSIsInVwZGF0ZUdyYWRlIiwiZGVsZXRlR3JhZGUiLCJkZWxldGVNdWx0aXBsZUdyYWRlcyIsImdldFN0dWRlbnRzQnlTY2hvb2wiLCJnZXRTdWJqZWN0c0J5U2Nob29sSWQiLCJnZXRFeGFtVHlwZXMiLCJnZXRDbGFzc2VzQnlTY2hvb2wiLCJ2ZXJpZnlQYXNzd29yZCIsIkdyYWRlTW9kYWwiLCJQYXNzd29yZENvbmZpcm1EZWxldGVNb2RhbCIsInVzZVRvYXN0IiwiVG9hc3RDb250YWluZXIiLCJuYXZpZ2F0aW9uIiwiaWNvbiIsImJhc2VIcmVmIiwidGl0bGUiLCJHcmFkZXNQYWdlIiwidXNlciIsImxvZ291dCIsInRvYXN0cyIsInJlbW92ZVRvYXN0Iiwic2hvd1N1Y2Nlc3MiLCJzaG93RXJyb3IiLCJncmFkZVJlY29yZHMiLCJzZXRHcmFkZVJlY29yZHMiLCJzdGF0cyIsInNldFN0YXRzIiwidG90YWxHcmFkZXMiLCJhdmVyYWdlU2NvcmUiLCJoaWdoZXN0U2NvcmUiLCJsb3dlc3RTY29yZSIsInBhc3NSYXRlIiwibG9hZGluZ0RhdGEiLCJzZXRMb2FkaW5nRGF0YSIsInNlbGVjdGVkQ2xhc3MiLCJzZXRTZWxlY3RlZENsYXNzIiwic2VsZWN0ZWRTdWJqZWN0Iiwic2V0U2VsZWN0ZWRTdWJqZWN0Iiwic2VsZWN0ZWRUZXJtIiwic2V0U2VsZWN0ZWRUZXJtIiwic2VsZWN0ZWRFeGFtVHlwZSIsInNldFNlbGVjdGVkRXhhbVR5cGUiLCJpc0dyYWRlTW9kYWxPcGVuIiwic2V0SXNHcmFkZU1vZGFsT3BlbiIsImlzRGVsZXRlTW9kYWxPcGVuIiwic2V0SXNEZWxldGVNb2RhbE9wZW4iLCJncmFkZVRvRWRpdCIsInNldEdyYWRlVG9FZGl0IiwiZ3JhZGVUb0RlbGV0ZSIsInNldEdyYWRlVG9EZWxldGUiLCJkZWxldGVUeXBlIiwic2V0RGVsZXRlVHlwZSIsInNlbGVjdGVkR3JhZGVzIiwic2V0U2VsZWN0ZWRHcmFkZXMiLCJzdHVkZW50cyIsInNldFN0dWRlbnRzIiwic3ViamVjdHMiLCJzZXRTdWJqZWN0cyIsImV4YW1UeXBlcyIsInNldEV4YW1UeXBlcyIsImNsYXNzZXMiLCJzZXRDbGFzc2VzIiwiaXNTdWJtaXR0aW5nIiwic2V0SXNTdWJtaXR0aW5nIiwiY2xlYXJTZWxlY3Rpb24iLCJzZXRDbGVhclNlbGVjdGlvbiIsInNjaG9vbElkIiwic2Nob29sX2lkcyIsInNjaG9vbF9pZCIsImZldGNoR3JhZGVEYXRhIiwiZmlsdGVycyIsImNsYXNzX2lkIiwic3ViamVjdF9pZCIsInRlcm0iLCJleGFtX3R5cGVfaWQiLCJyZWNvcmRzUmVzcG9uc2UiLCJzdGF0c1Jlc3BvbnNlIiwiUHJvbWlzZSIsImFsbCIsImdyYWRlX3JlY29yZHMiLCJlcnJvciIsImNvbnNvbGUiLCJmZXRjaEFkZGl0aW9uYWxEYXRhIiwicmVzdWx0cyIsImFsbFNldHRsZWQiLCJzdGF0dXMiLCJ2YWx1ZSIsInJlYXNvbiIsImFueURhdGFGYWlsZWQiLCJzb21lIiwicmVzdWx0IiwiaGFuZGxlQ3JlYXRlR3JhZGUiLCJoYW5kbGVFZGl0R3JhZGUiLCJncmFkZSIsImhhbmRsZURlbGV0ZUdyYWRlIiwiaGFuZGxlRGVsZXRlTXVsdGlwbGUiLCJoYW5kbGVTZWxlY3Rpb25DaGFuZ2UiLCJzZWxlY3RlZFJvd3MiLCJoYW5kbGVHcmFkZVN1Ym1pdCIsImRhdGEiLCJfaWQiLCJoYW5kbGVEZWxldGVDb25maXJtIiwicGFzc3dvcmQiLCJwYXNzd29yZFZlcmlmaWVkIiwiZW1haWwiLCJFcnJvciIsInNlbGVjdGVkSWRzIiwibWFwIiwiZyIsImxlbmd0aCIsIm1lc3NhZ2UiLCJnZXRHcmFkZUNvbG9yIiwiZ2V0U2NvcmVDb2xvciIsInNjb3JlIiwiY29sdW1ucyIsImhlYWRlciIsImFjY2Vzc29yIiwicm93IiwiZGl2IiwicCIsImNsYXNzTmFtZSIsInN0dWRlbnRfbmFtZSIsInN0dWRlbnRfaWQiLCJzcGFuIiwiY2xhc3NfbmFtZSIsInN1YmplY3RfbmFtZSIsImV4YW1fdHlwZSIsImFjdGlvbnMiLCJsYWJlbCIsIm9uQ2xpY2siLCJmaWx0ZXJlZFJlY29yZHMiLCJmaWx0ZXIiLCJyZWNvcmQiLCJzaXplIiwiY29sb3IiLCJhbGxvd2VkUm9sZXMiLCJvbkxvZ291dCIsImgxIiwic2VsZWN0Iiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwib3B0aW9uIiwiY2xzIiwibmFtZSIsInN1YmplY3QiLCJleGFtVHlwZSIsInR5cGUiLCJidXR0b24iLCJ3aGlsZUhvdmVyIiwic2NhbGUiLCJ3aGlsZVRhcCIsImgyIiwiZmFsbGJhY2siLCJkZWZhdWx0SXRlbXNQZXJQYWdlIiwib25TZWxlY3Rpb25DaGFuZ2UiLCJvblNlbGVjdGlvbkNsZWFyZWQiLCJpc09wZW4iLCJvbkNsb3NlIiwib25TdWJtaXQiLCJsb2FkaW5nIiwib25Db25maXJtIiwiaXRlbU5hbWUiLCJ1bmRlZmluZWQiLCJpdGVtQ291bnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboards)/school-admin/grades/page.tsx\n"));

/***/ })

});